#!/usr/bin/env python3
"""
Verify Clean Firebase Structure Usage

This script verifies that the entire project is now using the clean Firebase structure
and that all functionality works correctly.
"""

import sys
import os
from datetime import datetime
import asyncio

# Add backend to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.firebase_clean import clean_firebase_service
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def verify_clean_structure_usage():
    """Verify the clean structure is being used correctly"""
    try:
        clean_firebase_service.initialize()
        
        print("🔍 Clean Firebase Structure Usage Verification")
        print("=" * 60)
        
        # 1. Test Settings Management
        print("\n📁 Testing Settings Management:")
        
        # Test ASR training settings
        asr_settings = clean_firebase_service.get_settings('asr_training')
        print(f"  ✅ ASR Training Settings: {len(asr_settings)} keys")
        print(f"     - Epochs: {asr_settings.get('epochs', 'N/A')}")
        print(f"     - Learning Rate: {asr_settings.get('learning_rate', 'N/A')}")
        print(f"     - Last Updated: {asr_settings.get('updated_at', 'N/A')}")
        
        # Test ASR validation settings
        asr_val_settings = clean_firebase_service.get_settings('asr_validation')
        print(f"  ✅ ASR Validation Settings: {len(asr_val_settings)} keys")
        print(f"     - Validation Type: {asr_val_settings.get('validation_type', 'N/A')}")
        print(f"     - Max Samples: {asr_val_settings.get('max_samples', 'N/A')}")
        
        # 2. Test Validation History
        print("\n📊 Testing Validation History:")
        validation_history = clean_firebase_service.get_validation_history(limit=5)
        print(f"  ✅ Found {len(validation_history)} validation records")
        
        for i, validation in enumerate(validation_history[:3]):
            print(f"     {i+1}. ID: {validation.get('validation_id', 'N/A')[:8]}...")
            print(f"        Model: {validation.get('model_id', 'N/A')}")
            print(f"        Status: {validation.get('status', 'N/A')}")
            print(f"        Created: {validation.get('created_at', 'N/A')}")
        
        # 3. Test Training Sessions
        print("\n🏋️ Testing Training Sessions:")
        training_docs = clean_firebase_service.db.collection('training').limit(3).get()
        print(f"  ✅ Found {len(training_docs)} training sessions")
        
        for doc in training_docs:
            data = doc.to_dict()
            print(f"     - Session: {data.get('session_id', doc.id)}")
            print(f"       Model Type: {data.get('model_type', 'N/A')}")
            print(f"       Status: {data.get('status', 'N/A')}")
            print(f"       Updated: {data.get('updated_at', 'N/A')}")
        
        # 4. Test Analytics
        print("\n📈 Testing Analytics:")
        analytics_docs = clean_firebase_service.db.collection('analytics').get()
        print(f"  ✅ Found {len(analytics_docs)} analytics records")
        
        for doc in analytics_docs:
            data = doc.to_dict()
            print(f"     - {doc.id}: {data.get('total_training_sessions', 0)} training sessions")
            print(f"       Validation Runs: {data.get('total_validation_runs', 0)}")
            print(f"       Active Models: {data.get('active_models', 0)}")
        
        # 5. Test API Endpoints
        print("\n🌐 Testing API Integration:")
        
        # Import and test core modules
        try:
            from backend.core.asr.trainer import asr_trainer
            from backend.core.validation.validator import model_validator
        except ImportError:
            # Try alternative import path
            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            from core.asr.trainer import asr_trainer
            from core.validation.validator import model_validator
        
        # Test ASR trainer status
        try:
            status = await asr_trainer.get_training_status()
            print(f"  ✅ ASR Trainer Status: {status.get('status', 'N/A')}")
            print(f"     Progress: {status.get('progress', 0)}%")
            print(f"     Message: {status.get('message', 'N/A')}")
        except Exception as e:
            print(f"  ⚠️ ASR Trainer Status Error: {e}")
        
        # 6. Verify Migration Logs
        print("\n📝 Checking Migration Logs:")
        migration_logs = clean_firebase_service.db.collection('_migration_logs').get()
        print(f"  ✅ Found {len(migration_logs)} migration log records")
        
        for doc in migration_logs:
            data = doc.to_dict()
            print(f"     - {doc.id}")
            print(f"       Completed: {data.get('migration_completed_at', 'N/A')}")
            print(f"       Actions: {data.get('total_actions', 0)}")
        
        # 7. Performance Check
        print("\n⚡ Performance Check:")
        start_time = datetime.now()
        
        # Test multiple operations
        settings_test = clean_firebase_service.get_settings('asr_training')
        validation_test = clean_firebase_service.get_validation_history(limit=1)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print(f"  ✅ Operations completed in {duration:.3f} seconds")
        print(f"     Settings retrieval: {len(settings_test)} keys")
        print(f"     Validation history: {len(validation_test)} records")
        
        # 8. Structure Verification
        print("\n🏗️ Structure Verification:")
        
        # Check that new collections exist and have data
        collections_to_check = ['settings', 'validation', 'training', 'analytics']
        
        for collection_name in collections_to_check:
            try:
                docs = clean_firebase_service.db.collection(collection_name).limit(1).get()
                if docs:
                    print(f"  ✅ {collection_name}: Active with data")
                else:
                    print(f"  ⚠️ {collection_name}: Exists but empty")
            except Exception as e:
                print(f"  ❌ {collection_name}: Error - {e}")
        
        print("\n" + "=" * 60)
        print("✅ Clean Firebase Structure Verification COMPLETED!")
        print("🎉 All systems are using the new clean structure!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Verification failed: {e}")
        return False

def main():
    """Main verification function"""
    print("🚀 Starting Clean Firebase Structure Usage Verification...")
    
    success = asyncio.run(verify_clean_structure_usage())
    
    if success:
        print("\n✅ VERIFICATION SUCCESSFUL!")
        print("💡 The entire project is now using the clean Firebase structure.")
        print("🎯 Benefits achieved:")
        print("   - Unified settings management")
        print("   - Hierarchical validation data")
        print("   - Organized training sessions")
        print("   - Better performance and maintainability")
    else:
        print("\n❌ VERIFICATION FAILED!")
        print("🔧 Check the logs above for details.")

if __name__ == "__main__":
    main()
