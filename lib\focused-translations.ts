export type Language = 'en' | 'ar'

export interface FocusedTranslations {
  // Dashboard
  dashboard: string
  welcomeBack: string
  quickActions: string
  uploadAudio: string
  uploadAudioDesc: string
  viewHistory: string
  viewHistoryDesc: string
  viewProfile: string
  viewProfileDesc: string
  aiTraining: string
  aiTrainingDesc: string
  
  // Upload Page
  uploadRecording: string
  recordOrUpload: string
  createNewRecording: string
  chooseRecordOrUpload: string
  recordingTitle: string
  enterDescriptiveTitle: string
  audioSource: string
  recordLive: string
  uploadFile: string
  chooseAudioFile: string
  dragAndDrop: string
  supportedFormats: string
  recordedAudio: string
  duration: string
  calculating: string
  transcription: string
  enterExactText: string
  provideAccurateTranscription: string
  speakerGender: string
  selectSpeakerGender: string
  male: string
  female: string
  other: string
  submitRecording: string
  submittingRecording: string
  submitting: string
  recordingSubmitted: string
  recordingSubmittedDesc: string
  recordAnother: string
  viewMyRecordings: string

  // Upload Form Details
  recordingSubmittedSuccess: string
  recordingSubmittedForReview: string
  whatNext: string
  uploading: string
  uploadProgress: string
  fileName: string
  fileSize: string
  playAudio: string
  pauseAudio: string
  removeAudio: string
  clearAudio: string

  // File Upload
  dragDropFiles: string
  orClickToSelect: string
  supportedFileTypes: string
  maxFileSize: string
  selectFile: string

  // Form Validation
  titleRequired: string
  audioRequired: string
  transcriptionRequired: string
  genderRequired: string

  // Success Messages
  uploadSuccessful: string
  recordingProcessed: string
  thankYou: string
  
  // Recording States
  startRecording: string
  stopRecording: string
  recording: string
  recordingComplete: string
  
  // Navigation
  back: string
  menu: string
  home: string
  
  // Authentication
  authenticationRequired: string
  pleaseLogin: string
  
  // Common Actions
  submit: string
  cancel: string
  save: string
  delete: string
  edit: string
  view: string
  
  // Stats
  totalMinutes: string
  totalRecordings: string
  approvalRate: string
  statusDistribution: string
  approved: string
  pending: string
  rejected: string
  lastUpload: string
  never: string
  avgDuration: string

  // Charts
  statusDistributionChart: string
  statusDistributionDesc: string
  monthlyContributions: string
  monthlyContributionsDesc: string
  recentUploads: string
  recentUploadsDesc: string
  
  // My Recordings
  myRecordings: string
  recordingsHistory: string
  noRecordings: string
  uploadFirst: string
  viewAndManage: string
  recordingHistory: string
  editOrDelete: string

  // Table Headers
  title: string
  source: string
  date: string
  status: string
  feedback: string
  actions: string

  // Filters
  allStatuses: string
  recordsPerPage: string
  selectStatus: string
  selectRecordsPerPage: string
  clearFilters: string

  // Actions
  play: string
  pause: string
  edit: string
  delete: string
  details: string
  tryAgain: string

  // Edit Dialog
  editRecording: string
  editRecordingDesc: string
  transcript: string
  enterTranscript: string
  saveChanges: string

  // Delete Dialog
  deleteRecording: string
  deleteRecordingDesc: string
  deleteConfirm: string

  // Status
  unknown: string
  directUpload: string

  // Pagination
  showing: string
  to: string
  of: string
  recordings: string

  // Messages
  noRecordingsFound: string
  failedToLoad: string
  recordingDeleted: string
  recordingUpdated: string
  recordingUpdatedDesc: string
  failedToDelete: string
  failedToUpdate: string
  
  // Admin Only
  bulkUpload: string
  bulkUploadDesc: string
  adminOnly: string
  
  // Admin Dashboard
  adminDashboard: string
  userManagement: string
  systemStats: string
  totalUsers: string
  totalAudio: string
  systemHealth: string

  // Language
  language: string
  english: string
  arabic: string
  selectLanguage: string

  // Login/Auth
  signIn: string
  signUp: string
  welcomeToMasalit: string
  platformDescription: string
  usernameOrEmail: string
  password: string
  confirmPassword: string
  forgotPassword: string
  dontHaveAccount: string
  alreadyHaveAccount: string
  createAccount: string
  resetPassword: string
  resetPasswordDesc: string
  sendResetLink: string
  backToLogin: string
  masalitLanguage: string
  languagePlatform: string
  contributeToPreserving: string
  fullName: string
  username: string
  email: string
  joinMasalitPlatform: string
  accountCreatedSuccess: string
  goToLogin: string
  passwordsDoNotMatch: string
  usernameInvalidChars: string
  usernameAlreadyTaken: string
  emailAlreadyRegistered: string
}

export const focusedTranslations: Record<Language, FocusedTranslations> = {
  en: {
    // Dashboard
    dashboard: 'Dashboard',
    welcomeBack: 'Welcome back',
    quickActions: 'Quick Actions',
    uploadAudio: 'Upload Audio',
    uploadAudioDesc: 'Upload audio files and transcripts',
    viewHistory: 'My Recordings',
    viewHistoryDesc: 'View your upload history',
    viewProfile: 'View Profile',
    viewProfileDesc: 'View and edit your profile',
    aiTraining: 'AI Training',
    aiTrainingDesc: 'Manage AI models and training',
    
    // Upload Page
    uploadRecording: 'Upload Recording',
    recordOrUpload: 'Record or upload audio with transcription',
    createNewRecording: 'Create New Recording',
    chooseRecordOrUpload: 'Choose to record live or upload an existing audio file',
    recordingTitle: 'Recording Title',
    enterDescriptiveTitle: 'Enter a descriptive title',
    audioSource: 'Audio Source',
    recordLive: 'Record Live',
    uploadFile: 'Upload File',
    chooseAudioFile: 'Choose audio file',
    dragAndDrop: 'or drag and drop',
    supportedFormats: 'WAV, MP3, OGG up to',
    recordedAudio: 'Recorded Audio',
    duration: 'Duration',
    calculating: 'Calculating...',
    transcription: 'Transcription',
    enterExactText: 'Enter the exact text spoken in the audio...',
    provideAccurateTranscription: 'Please provide an accurate transcription of what is spoken in the audio',
    speakerGender: 'Speaker Gender',
    selectSpeakerGender: 'Select speaker gender',
    male: 'Male',
    female: 'Female',
    other: 'Other',
    submitRecording: 'Submit Recording',
    submittingRecording: 'Submitting Recording...',
    submitting: 'Submitting...',
    recordingSubmitted: 'Recording Submitted Successfully!',
    recordingSubmittedDesc: 'Your recording has been submitted for review. What would you like to do next?',
    recordAnother: 'Record Another',
    viewMyRecordings: 'View My Recordings',

    // Upload Form Details
    recordingSubmittedSuccess: 'Recording Submitted!',
    recordingSubmittedForReview: 'Your recording has been submitted for review.',
    whatNext: 'What would you like to do next?',
    uploading: 'Uploading...',
    uploadProgress: 'Upload Progress',
    fileName: 'File Name',
    fileSize: 'File Size',
    playAudio: 'Play Audio',
    pauseAudio: 'Pause Audio',
    removeAudio: 'Remove Audio',
    clearAudio: 'Clear Audio',

    // File Upload
    dragDropFiles: 'or drag and drop',
    orClickToSelect: 'or click to select',
    supportedFileTypes: 'Supported file types',
    maxFileSize: 'Maximum file size',
    selectFile: 'Select File',

    // Form Validation
    titleRequired: 'Title is required',
    audioRequired: 'Audio file is required',
    transcriptionRequired: 'Transcription is required',
    genderRequired: 'Speaker gender is required',

    // Success Messages
    uploadSuccessful: 'Upload successful',
    recordingProcessed: 'Recording processed successfully',
    thankYou: 'Thank you for your contribution',
    
    // Recording States
    startRecording: 'Start Recording',
    stopRecording: 'Stop Recording',
    recording: 'Recording',
    recordingComplete: 'Recording Complete',
    
    // Navigation
    back: 'Back',
    menu: 'Menu',
    home: 'Home',
    
    // Authentication
    authenticationRequired: 'Authentication Required',
    pleaseLogin: 'Please log in to upload recordings',
    
    // Common Actions
    submit: 'Submit',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    view: 'View',
    
    // Stats
    totalMinutes: 'Total Minutes',
    totalRecordings: 'Total Recordings',
    approvalRate: 'Approval Rate',
    statusDistribution: 'Status Distribution',
    approved: 'Approved',
    pending: 'Pending',
    rejected: 'Rejected',
    lastUpload: 'Last upload',
    never: 'Never',
    avgDuration: 'Avg.',

    // Charts
    statusDistributionChart: 'Status Distribution',
    statusDistributionDesc: 'Breakdown of your recordings by status',
    monthlyContributions: 'Monthly Contributions',
    monthlyContributionsDesc: 'Your contributions over the last 6 months',
    recentUploads: 'Recent Uploads',
    recentUploadsDesc: 'Your latest recordings',
    
    // My Recordings
    myRecordings: 'My Recordings',
    recordingsHistory: 'Your recording history',
    noRecordings: 'No recordings yet',
    uploadFirst: 'Upload your first recording to get started',
    viewAndManage: 'View and manage your submitted recordings',
    recordingHistory: 'Recording History',
    editOrDelete: 'You can edit or delete recordings that are still pending review',

    // Table Headers
    title: 'Title',
    source: 'Source',
    date: 'Date',
    duration: 'Duration',
    status: 'Status',
    feedback: 'Feedback',
    actions: 'Actions',

    // Filters
    allStatuses: 'All Statuses',
    recordsPerPage: 'Records per page',
    selectStatus: 'Select status',
    selectRecordsPerPage: 'Select records per page',
    clearFilters: 'Clear Filters',

    // Actions
    play: 'Play',
    pause: 'Pause',
    edit: 'Edit',
    delete: 'Delete',
    details: 'Details',
    tryAgain: 'Try Again',

    // Edit Dialog
    editRecording: 'Edit Recording',
    editRecordingDesc: 'Make changes to your recording. You can only edit recordings that are pending review.',
    transcript: 'Transcript',
    enterTranscript: 'Enter or edit the transcript content...',
    saveChanges: 'Save Changes',

    // Delete Dialog
    deleteRecording: 'Delete Recording',
    deleteRecordingDesc: 'Are you sure you want to delete this recording? This action cannot be undone.',
    deleteConfirm: 'Delete',

    // Status
    unknown: 'Unknown',
    directUpload: 'Direct Upload',

    // Pagination
    showing: 'Showing',
    to: 'to',
    of: 'of',
    recordings: 'recordings',

    // Messages
    noRecordingsFound: 'No recordings found matching your filters',
    failedToLoad: 'Failed to load your recordings. Please try again later.',
    recordingDeleted: 'Recording deleted successfully',
    recordingUpdated: 'Recording updated',
    recordingUpdatedDesc: 'Your recording has been updated successfully.',
    failedToDelete: 'Failed to delete recording. Please try again.',
    failedToUpdate: 'Failed to update recording. Please try again.',
    
    // Admin Only
    bulkUpload: 'Bulk Upload',
    bulkUploadDesc: 'Upload multiple audio files at once',
    adminOnly: 'Admin Only',

    // Admin Dashboard
    adminDashboard: 'Admin Dashboard',
    userManagement: 'User Management',
    systemStats: 'System Statistics',
    totalUsers: 'Total Users',
    totalAudio: 'Total Audio Files',
    systemHealth: 'System Health',

    // Language
    language: 'Language',
    english: 'English',
    arabic: 'العربية',
    selectLanguage: 'Select Language',

    // Login/Auth
    signIn: 'Sign In',
    signUp: 'Sign Up',
    welcomeToMasalit: 'Welcome back to the Masalit platform',
    platformDescription: 'Contribute to preserving and documenting the Masalit language',
    usernameOrEmail: 'Username or Email',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    forgotPassword: 'Forgot password?',
    dontHaveAccount: "Don't have an account?",
    alreadyHaveAccount: 'Already have an account?',
    createAccount: 'Create Account',
    resetPassword: 'Reset Password',
    resetPasswordDesc: "Enter your email address and we'll send you a link to reset your password.",
    sendResetLink: 'Send Reset Link',
    backToLogin: 'Back to Login',
    masalitLanguage: 'Masalit Language',
    languagePlatform: 'Language Platform',
    contributeToPreserving: 'Contribute to preserving and documenting the Masalit language',
    fullName: 'Full Name',
    username: 'Username',
    email: 'Email',
    joinMasalitPlatform: 'Join the Masalit platform to start contributing',
    accountCreatedSuccess: 'Account created successfully! Please check your email to verify your account before logging in.',
    goToLogin: 'Go to Login',
    passwordsDoNotMatch: 'Passwords do not match',
    usernameInvalidChars: 'Username can only contain letters, numbers, and underscores',
    usernameAlreadyTaken: 'Username is already taken',
    emailAlreadyRegistered: 'Email is already registered',
  },
  ar: {
    // Dashboard
    dashboard: 'لوحة التحكم',
    welcomeBack: 'مرحباً بعودتك',
    quickActions: 'الإجراءات السريعة',
    uploadAudio: 'رفع الصوت',
    uploadAudioDesc: 'رفع ملفات الصوت والنصوص',
    viewHistory: 'تسجيلاتي',
    viewHistoryDesc: 'عرض تاريخ الرفع الخاص بك',
    viewProfile: 'عرض الملف الشخصي',
    viewProfileDesc: 'عرض وتعديل ملفك الشخصي',
    aiTraining: 'تدريب الذكاء الاصطناعي',
    aiTrainingDesc: 'إدارة نماذج الذكاء الاصطناعي والتدريب',
    
    // Upload Page
    uploadRecording: 'رفع التسجيل',
    recordOrUpload: 'تسجيل أو رفع الصوت مع النص',
    createNewRecording: 'إنشاء تسجيل جديد',
    chooseRecordOrUpload: 'اختر التسجيل المباشر أو رفع ملف صوتي موجود',
    recordingTitle: 'عنوان التسجيل',
    enterDescriptiveTitle: 'أدخل عنواناً وصفياً',
    audioSource: 'مصدر الصوت',
    recordLive: 'تسجيل مباشر',
    uploadFile: 'رفع ملف',
    chooseAudioFile: 'اختر ملف صوتي',
    dragAndDrop: 'أو اسحب وأفلت',
    supportedFormats: 'WAV, MP3, OGG حتى',
    recordedAudio: 'الصوت المسجل',
    duration: 'المدة',
    calculating: 'جاري الحساب...',
    transcription: 'النص المكتوب',
    enterExactText: 'أدخل النص الدقيق المنطوق في الصوت...',
    provideAccurateTranscription: 'يرجى تقديم نص دقيق لما يُقال في التسجيل الصوتي',
    speakerGender: 'جنس المتحدث',
    selectSpeakerGender: 'اختر جنس المتحدث',
    male: 'ذكر',
    female: 'أنثى',
    other: 'آخر',
    submitRecording: 'إرسال التسجيل',
    submittingRecording: 'جاري إرسال التسجيل...',
    submitting: 'جاري الإرسال...',
    recordingSubmitted: 'تم إرسال التسجيل بنجاح!',
    recordingSubmittedDesc: 'تم إرسال تسجيلك للمراجعة. ماذا تريد أن تفعل بعد ذلك؟',
    recordAnother: 'تسجيل آخر',
    viewMyRecordings: 'عرض تسجيلاتي',

    // Upload Form Details
    recordingSubmittedSuccess: 'تم إرسال التسجيل!',
    recordingSubmittedForReview: 'تم إرسال تسجيلك للمراجعة.',
    whatNext: 'ماذا تريد أن تفعل بعد ذلك؟',
    uploading: 'جاري الرفع...',
    uploadProgress: 'تقدم الرفع',
    fileName: 'اسم الملف',
    fileSize: 'حجم الملف',
    playAudio: 'تشغيل الصوت',
    pauseAudio: 'إيقاف الصوت',
    removeAudio: 'إزالة الصوت',
    clearAudio: 'مسح الصوت',

    // File Upload
    dragDropFiles: 'أو اسحب وأفلت',
    orClickToSelect: 'أو انقر للاختيار',
    supportedFileTypes: 'أنواع الملفات المدعومة',
    maxFileSize: 'الحد الأقصى لحجم الملف',
    selectFile: 'اختر ملف',

    // Form Validation
    titleRequired: 'العنوان مطلوب',
    audioRequired: 'ملف الصوت مطلوب',
    transcriptionRequired: 'النص المكتوب مطلوب',
    genderRequired: 'جنس المتحدث مطلوب',

    // Success Messages
    uploadSuccessful: 'تم الرفع بنجاح',
    recordingProcessed: 'تم معالجة التسجيل بنجاح',
    thankYou: 'شكراً لمساهمتك',
    
    // Recording States
    startRecording: 'بدء التسجيل',
    stopRecording: 'إيقاف التسجيل',
    recording: 'جاري التسجيل',
    recordingComplete: 'اكتمل التسجيل',
    
    // Navigation
    back: 'رجوع',
    menu: 'القائمة',
    home: 'الرئيسية',
    
    // Authentication
    authenticationRequired: 'مطلوب تسجيل الدخول',
    pleaseLogin: 'يرجى تسجيل الدخول لرفع التسجيلات',
    
    // Common Actions
    submit: 'إرسال',
    cancel: 'إلغاء',
    save: 'حفظ',
    delete: 'حذف',
    edit: 'تعديل',
    view: 'عرض',
    
    // Stats
    totalMinutes: 'إجمالي الدقائق',
    totalRecordings: 'إجمالي التسجيلات',
    approvalRate: 'معدل الموافقة',
    statusDistribution: 'توزيع الحالة',
    approved: 'موافق عليه',
    pending: 'في الانتظار',
    rejected: 'مرفوض',
    lastUpload: 'آخر رفع',
    never: 'أبداً',
    avgDuration: 'متوسط',

    // Charts
    statusDistributionChart: 'توزيع الحالة',
    statusDistributionDesc: 'تفصيل تسجيلاتك حسب الحالة',
    monthlyContributions: 'المساهمات الشهرية',
    monthlyContributionsDesc: 'مساهماتك خلال الـ 6 أشهر الماضية',
    recentUploads: 'الرفوعات الأخيرة',
    recentUploadsDesc: 'أحدث تسجيلاتك',
    
    // My Recordings
    myRecordings: 'تسجيلاتي',
    recordingsHistory: 'تاريخ تسجيلاتك',
    noRecordings: 'لا توجد تسجيلات بعد',
    uploadFirst: 'ارفع تسجيلك الأول للبدء',
    viewAndManage: 'عرض وإدارة التسجيلات المرسلة',
    recordingHistory: 'تاريخ التسجيلات',
    editOrDelete: 'يمكنك تعديل أو حذف التسجيلات التي لا تزال قيد المراجعة',

    // Table Headers
    title: 'العنوان',
    source: 'المصدر',
    date: 'التاريخ',
    duration: 'المدة',
    status: 'الحالة',
    feedback: 'التعليقات',
    actions: 'الإجراءات',

    // Filters
    allStatuses: 'جميع الحالات',
    recordsPerPage: 'السجلات في الصفحة',
    selectStatus: 'اختر الحالة',
    selectRecordsPerPage: 'اختر عدد السجلات في الصفحة',
    clearFilters: 'مسح المرشحات',

    // Actions
    play: 'تشغيل',
    pause: 'إيقاف',
    edit: 'تعديل',
    delete: 'حذف',
    details: 'التفاصيل',
    tryAgain: 'حاول مرة أخرى',

    // Edit Dialog
    editRecording: 'تعديل التسجيل',
    editRecordingDesc: 'قم بإجراء تغييرات على تسجيلك. يمكنك فقط تعديل التسجيلات التي قيد المراجعة.',
    transcript: 'النص المكتوب',
    enterTranscript: 'أدخل أو عدل محتوى النص المكتوب...',
    saveChanges: 'حفظ التغييرات',

    // Delete Dialog
    deleteRecording: 'حذف التسجيل',
    deleteRecordingDesc: 'هل أنت متأكد من أنك تريد حذف هذا التسجيل؟ لا يمكن التراجع عن هذا الإجراء.',
    deleteConfirm: 'حذف',

    // Status
    unknown: 'غير معروف',
    directUpload: 'رفع مباشر',

    // Pagination
    showing: 'عرض',
    to: 'إلى',
    of: 'من',
    recordings: 'تسجيلات',

    // Messages
    noRecordingsFound: 'لم يتم العثور على تسجيلات تطابق المرشحات',
    failedToLoad: 'فشل في تحميل تسجيلاتك. يرجى المحاولة مرة أخرى لاحقاً.',
    recordingDeleted: 'تم حذف التسجيل بنجاح',
    recordingUpdated: 'تم تحديث التسجيل',
    recordingUpdatedDesc: 'تم تحديث تسجيلك بنجاح.',
    failedToDelete: 'فشل في حذف التسجيل. يرجى المحاولة مرة أخرى.',
    failedToUpdate: 'فشل في تحديث التسجيل. يرجى المحاولة مرة أخرى.',
    
    // Admin Only
    bulkUpload: 'الرفع المجمع',
    bulkUploadDesc: 'رفع عدة ملفات صوتية في مرة واحدة',
    adminOnly: 'للمدراء فقط',

    // Admin Dashboard
    adminDashboard: 'لوحة تحكم المدير',
    userManagement: 'إدارة المستخدمين',
    systemStats: 'إحصائيات النظام',
    totalUsers: 'إجمالي المستخدمين',
    totalAudio: 'إجمالي ملفات الصوت',
    systemHealth: 'صحة النظام',

    // Language
    language: 'اللغة',
    english: 'English',
    arabic: 'العربية',
    selectLanguage: 'اختر اللغة',

    // Login/Auth
    signIn: 'تسجيل الدخول',
    signUp: 'إنشاء حساب',
    welcomeToMasalit: 'مرحباً بعودتك إلى منصة المساليت',
    platformDescription: 'ساهم في الحفاظ على لغة المساليت وتوثيقها',
    usernameOrEmail: 'اسم المستخدم أو البريد الإلكتروني',
    password: 'كلمة المرور',
    confirmPassword: 'تأكيد كلمة المرور',
    forgotPassword: 'نسيت كلمة المرور؟',
    dontHaveAccount: 'ليس لديك حساب؟',
    alreadyHaveAccount: 'لديك حساب بالفعل؟',
    createAccount: 'إنشاء حساب',
    resetPassword: 'إعادة تعيين كلمة المرور',
    resetPasswordDesc: 'أدخل عنوان بريدك الإلكتروني وسنرسل لك رابطاً لإعادة تعيين كلمة المرور.',
    sendResetLink: 'إرسال رابط الإعادة',
    backToLogin: 'العودة لتسجيل الدخول',
    masalitLanguage: 'لغة المساليت',
    languagePlatform: 'منصة اللغة',
    contributeToPreserving: 'ساهم في الحفاظ على لغة المساليت وتوثيقها',
    fullName: 'الاسم الكامل',
    username: 'اسم المستخدم',
    email: 'البريد الإلكتروني',
    joinMasalitPlatform: 'انضم إلى منصة المساليت لبدء المساهمة',
    accountCreatedSuccess: 'تم إنشاء الحساب بنجاح! يرجى التحقق من بريدك الإلكتروني للتحقق من حسابك قبل تسجيل الدخول.',
    goToLogin: 'الذهاب لتسجيل الدخول',
    passwordsDoNotMatch: 'كلمات المرور غير متطابقة',
    usernameInvalidChars: 'يمكن أن يحتوي اسم المستخدم على أحرف وأرقام وشرطات سفلية فقط',
    usernameAlreadyTaken: 'اسم المستخدم مأخوذ بالفعل',
    emailAlreadyRegistered: 'البريد الإلكتروني مسجل بالفعل',
  }
}

// Language detection and management
export const detectLanguage = (): Language => {
  // Check localStorage first
  if (typeof window !== 'undefined') {
    const saved = localStorage.getItem('masalit-language') as Language
    if (saved && ['en', 'ar'].includes(saved)) {
      return saved
    }
    
    // Check browser language
    const browserLang = navigator.language.toLowerCase()
    if (browserLang.startsWith('ar')) {
      return 'ar'
    }
  }
  
  // Default to English
  return 'en'
}

export const setLanguage = (lang: Language) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('masalit-language', lang)
    // Update document direction for RTL
    document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr'
    document.documentElement.lang = lang
  }
}

export const getTranslation = (key: keyof FocusedTranslations, lang: Language): string => {
  return focusedTranslations[lang][key] || focusedTranslations.en[key]
}
