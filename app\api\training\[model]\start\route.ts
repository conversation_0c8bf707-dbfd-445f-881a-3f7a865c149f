import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function POST(
  request: Request,
  { params }: { params: Promise<{ model: string }> }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    if (session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // This endpoint is deprecated - use the backend API directly
    return NextResponse.json({
      error: 'This endpoint is deprecated. Use the backend API at /api/asr/train',
      redirect: '/api/asr/train'
    }, { status: 410 })

  } catch (error: any) {
    console.error('Error starting training:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to start training' },
      { status: 500 }
    )
  }
}

export async function GET(
  request: Request,
  { params }: { params: Promise<{ model: string }> }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // This endpoint is deprecated - use the backend API directly
    return NextResponse.json({
      error: 'This endpoint is deprecated. Use the backend API at /api/asr/status',
      redirect: '/api/asr/status'
    }, { status: 410 })

  } catch (error: any) {
    console.error('Error getting training status:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to get training status' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ model: string }> }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    if (session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // This endpoint is deprecated - use the backend API directly
    return NextResponse.json({
      error: 'This endpoint is deprecated. Use the backend API at /api/asr/stop',
      redirect: '/api/asr/stop'
    }, { status: 410 })

  } catch (error: any) {
    console.error('Error stopping training:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to stop training' },
      { status: 500 }
    )
  }
}