import { collection, addDoc, query, where, orderBy, limit, getDocs } from 'firebase/firestore'
import { db } from './firebase'

export interface ActivityEvent {
  userId: string
  type: 'login' | 'logout' | 'audio_upload' | 'audio_approved' | 'audio_rejected' | 'profile_update' | 'password_change' | 'preferences_update' | 'bulk_upload' | 'training_start' | 'training_complete'
  description: string
  metadata?: {
    audioId?: string
    filename?: string
    duration?: number
    status?: string
    reviewedBy?: string
    reviewComment?: string
    bulkCount?: number
    modelType?: string
    trainingId?: string
    [key: string]: any
  }
  ip_address?: string
  user_agent?: string
  timestamp: string
  session_id?: string
}

export class ActivityTracker {
  private static instance: ActivityTracker
  private sessionId: string

  private constructor() {
    this.sessionId = this.generateSessionId()
  }

  public static getInstance(): ActivityTracker {
    if (!ActivityTracker.instance) {
      ActivityTracker.instance = new ActivityTracker()
    }
    return ActivityTracker.instance
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  public async trackActivity(
    userId: string,
    type: ActivityEvent['type'],
    description: string,
    metadata?: ActivityEvent['metadata']
  ): Promise<void> {
    try {
      const activity: ActivityEvent = {
        userId,
        type,
        description,
        metadata: metadata || {},
        timestamp: new Date().toISOString(),
        session_id: this.sessionId
      }

      // Add client-side information if available
      if (typeof window !== 'undefined') {
        activity.user_agent = navigator.userAgent
        // Note: IP address would need to be added server-side
      }

      // Store in user subcollection
      await addDoc(collection(db, 'users', userId, 'activity_logs'), activity)
    } catch (error) {
      console.error('Failed to track activity:', error)
      // Don't throw error to avoid breaking user experience
    }
  }

  public async getUserActivity(
    userId: string,
    options: {
      limit?: number
      type?: ActivityEvent['type']
      startDate?: Date
      endDate?: Date
    } = {}
  ): Promise<ActivityEvent[]> {
    try {
      let activityQuery = query(
        collection(db, 'users', userId, 'activity_logs'),
        orderBy('timestamp', 'desc')
      )

      if (options.type) {
        activityQuery = query(activityQuery, where('type', '==', options.type))
      }

      if (options.limit) {
        activityQuery = query(activityQuery, limit(options.limit))
      }

      const querySnapshot = await getDocs(activityQuery)
      const activities: ActivityEvent[] = []

      querySnapshot.forEach((doc) => {
        const data = doc.data() as ActivityEvent
        
        // Apply date filters client-side if needed
        if (options.startDate || options.endDate) {
          const activityDate = new Date(data.timestamp)
          if (options.startDate && activityDate < options.startDate) return
          if (options.endDate && activityDate > options.endDate) return
        }

        activities.push({
          ...data,
          id: doc.id
        } as ActivityEvent & { id: string })
      })

      return activities
    } catch (error) {
      console.error('Failed to fetch user activity:', error)
      return []
    }
  }

  public async getActivityStats(userId: string): Promise<{
    totalActivities: number
    recentActivities: number
    activityTypes: Record<string, number>
    lastActivity: string | null
  }> {
    try {
      const activities = await this.getUserActivity(userId, { limit: 1000 })
      
      const now = new Date()
      const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      
      const recentActivities = activities.filter(activity => 
        new Date(activity.timestamp) >= sevenDaysAgo
      )

      const activityTypes = activities.reduce((acc, activity) => {
        acc[activity.type] = (acc[activity.type] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      return {
        totalActivities: activities.length,
        recentActivities: recentActivities.length,
        activityTypes,
        lastActivity: activities.length > 0 ? activities[0].timestamp : null
      }
    } catch (error) {
      console.error('Failed to get activity stats:', error)
      return {
        totalActivities: 0,
        recentActivities: 0,
        activityTypes: {},
        lastActivity: null
      }
    }
  }

  // Convenience methods for common activities
  public async trackLogin(userId: string): Promise<void> {
    await this.trackActivity(userId, 'login', 'User logged in')
  }

  public async trackLogout(userId: string): Promise<void> {
    await this.trackActivity(userId, 'logout', 'User logged out')
  }

  public async trackAudioUpload(
    userId: string,
    audioId: string,
    filename: string,
    duration: number
  ): Promise<void> {
    await this.trackActivity(
      userId,
      'audio_upload',
      `Uploaded audio: ${filename}`,
      { audioId, filename, duration }
    )
  }

  public async trackAudioReview(
    userId: string,
    audioId: string,
    status: 'approved' | 'rejected',
    reviewedBy: string,
    comment?: string
  ): Promise<void> {
    await this.trackActivity(
      userId,
      status === 'approved' ? 'audio_approved' : 'audio_rejected',
      `Audio ${status}: ${audioId}`,
      { audioId, status, reviewedBy, reviewComment: comment }
    )
  }

  public async trackProfileUpdate(userId: string, fields: string[]): Promise<void> {
    await this.trackActivity(
      userId,
      'profile_update',
      `Updated profile fields: ${fields.join(', ')}`,
      { updatedFields: fields }
    )
  }

  public async trackPasswordChange(userId: string): Promise<void> {
    await this.trackActivity(userId, 'password_change', 'Changed password')
  }

  public async trackPreferencesUpdate(
    userId: string,
    section: string,
    changes: Record<string, any>
  ): Promise<void> {
    await this.trackActivity(
      userId,
      'preferences_update',
      `Updated ${section} preferences`,
      { section, changes }
    )
  }

  public async trackBulkUpload(
    userId: string,
    count: number,
    format: string
  ): Promise<void> {
    await this.trackActivity(
      userId,
      'bulk_upload',
      `Bulk uploaded ${count} files via ${format}`,
      { bulkCount: count, format }
    )
  }

  public async trackTrainingStart(
    userId: string,
    modelType: string,
    trainingId: string
  ): Promise<void> {
    await this.trackActivity(
      userId,
      'training_start',
      `Started ${modelType} training`,
      { modelType, trainingId }
    )
  }

  public async trackTrainingComplete(
    userId: string,
    modelType: string,
    trainingId: string,
    success: boolean
  ): Promise<void> {
    await this.trackActivity(
      userId,
      'training_complete',
      `${modelType} training ${success ? 'completed successfully' : 'failed'}`,
      { modelType, trainingId, success }
    )
  }
}

// Export singleton instance
export const activityTracker = ActivityTracker.getInstance()

// Hook for React components
export function useActivityTracker() {
  return activityTracker
}
