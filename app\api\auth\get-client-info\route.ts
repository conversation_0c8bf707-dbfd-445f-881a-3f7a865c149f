import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Get IP address from headers
    const forwardedFor = request.headers.get('x-forwarded-for');
    const ip = forwardedFor ? forwardedFor.split(',')[0] : request.ip || 'Unknown';

    // Get user agent from headers
    const userAgent = request.headers.get('user-agent') || 'Unknown';

    return NextResponse.json({
      ip_address: ip,
      user_agent: userAgent
    });
  } catch (error) {
    console.error('Error getting client info:', error);
    return NextResponse.json(
      { error: 'Failed to get client information' },
      { status: 500 }
    );
  }
} 