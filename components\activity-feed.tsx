"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { collection, query, orderBy, limit, getDocs } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { Loader2, Upload, CheckCircle, XCircle, FileText } from "lucide-react"
import { formatDistanceToNow } from "date-fns"

interface Activity {
  id: string
  type: 'upload' | 'approval' | 'rejection'
  title: string
  timestamp: Date
  user: {
    name: string
    username: string
  }
}

export function ActivityFeed() {
  const [activities, setActivities] = useState<Activity[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchActivities = async () => {
      try {
        // Fetch recent audio recordings
        const audioQuery = query(
          collection(db, "audio"),
          orderBy("created_at", "desc"),
          limit(10)
        )
        const audioSnapshot = await getDocs(audioQuery)
        
        const activitiesData: Activity[] = []
        
        for (const doc of audioSnapshot.docs) {
          const data = doc.data()
          
          // Get user data
          const userDoc = await getDocs(collection(db, "users"))
          const userData = userDoc.docs.find(d => d.id === data.user_id)?.data()
          
          activitiesData.push({
            id: doc.id,
            type: data.action === 'approved' ? 'approval' : data.action === 'rejected' ? 'rejection' : 'upload',
            title: data.title || 'Untitled Recording',
            timestamp: data.created_at.toDate(),
            user: {
              name: userData?.name || 'Unknown User',
              username: userData?.username || 'unknown',
            }
          })
        }
        
        setActivities(activitiesData)
      } catch (error) {
        console.error("Error fetching activities:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchActivities()
  }, [])

  const getActivityIcon = (type: Activity['type']) => {
    switch (type) {
      case 'upload':
        return <Upload className="h-4 w-4 text-blue-500" />
      case 'approval':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'rejection':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <FileText className="h-4 w-4 text-gray-500" />
    }
  }

  const getActivityText = (activity: Activity) => {
    switch (activity.type) {
      case 'upload':
        return `uploaded "${activity.title}"`
      case 'approval':
        return `approved "${activity.title}"`
      case 'rejection':
        return `rejected "${activity.title}"`
      default:
        return `updated "${activity.title}"`
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>Loading activities...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-4">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
        <CardDescription>Latest updates from the platform</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start gap-3">
              <div className="mt-1">{getActivityIcon(activity.type)}</div>
              <div className="flex-1 space-y-1">
                <p className="text-sm">
                  <span className="font-medium">{activity.user.name}</span>{" "}
                  {getActivityText(activity)}
                </p>
                <p className="text-xs text-muted-foreground">
                  {formatDistanceToNow(activity.timestamp, { addSuffix: true })}
                </p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
} 