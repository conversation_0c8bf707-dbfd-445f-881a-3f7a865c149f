'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { listAllAudio, deleteAudioRecord, updateAudioRecord, updateReviewStatus, bulkUpdateReviewStatus, getUsernames } from "@/lib/audio-api-v2"
import { useAuth } from "@/components/auth-provider"
import { useToast } from "@/hooks/use-toast"
import {
  Search,
  Filter,
  Download,
  Trash2,
  Edit,
  Play,
  Pause,
  CheckCircle,
  XCircle,
  Clock,
  BarChart3,
  Users,
  FileAudio,
  TrendingUp
} from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  <PERSON>ertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Textarea } from "@/components/ui/textarea"

interface ManagedRecording {
  id: string
  title: string
  audio_url: string
  duration: number
  format: string
  created_at: string
  user_id: string
  username: string
  source: string

  // Hierarchical data (flattened for UI)
  transcription_content?: string
  gender?: string
  language?: string
  action?: string
  approved?: number
  trained_asr?: boolean
  tts_trained?: boolean
  quality_score?: number
  review_notes?: string
  reviewed_at?: string
  reviewed_by?: string
  file_size?: number
  download_count?: number
  last_accessed?: string

  // Subcollections (loaded on demand)
  transcriptions?: {
    primary?: {
      content: string
      language: string
      transcription_source: string
      created_at: string
      type: string
      speaker_count: number
    }
  }
  metadata?: {
    details?: {
      gender: string
      language: string
      recording_context?: string
    }
  }
  review?: {
    status?: {
      action: 'pending' | 'approved' | 'rejected'
      reviewed_by?: string
      reviewed_at?: string
      feedback?: string
      is_flagged: boolean
    }
  }
  training?: {
    status?: {
      trained_asr: boolean
      tts_trained: boolean
      training_sessions: string[]
      last_trained_at?: string
    }
  }
}

interface Analytics {
  totalRecordings: number
  approvedRecordings: number
  pendingRecordings: number
  rejectedRecordings: number
  totalDuration: number
  averageQuality: number
  uniqueUsers: number
  storageUsed: number
}

export default function ManagedRecordingsPage() {
  const { user } = useAuth()
  const { toast } = useToast()

  const [recordings, setRecordings] = useState<ManagedRecording[]>([])
  const [filteredRecordings, setFilteredRecordings] = useState<ManagedRecording[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [sourceFilter, setSourceFilter] = useState("all")
  const [sortBy, setSortBy] = useState("created_at")
  const [sortOrder, setSortOrder] = useState("desc")
  const [userFilter, setUserFilter] = useState("")
  const [selectedRecordings, setSelectedRecordings] = useState<Set<string>>(new Set())
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [recordingToDelete, setRecordingToDelete] = useState<ManagedRecording | null>(null)
  const [editingRecording, setEditingRecording] = useState<ManagedRecording | null>(null)
  const [playingAudio, setPlayingAudio] = useState<string | null>(null)
  const [analytics, setAnalytics] = useState<Analytics>({
    totalRecordings: 0,
    approvedRecordings: 0,
    pendingRecordings: 0,
    rejectedRecordings: 0,
    totalDuration: 0,
    averageQuality: 0,
    uniqueUsers: 0,
    storageUsed: 0
  })

  useEffect(() => {
    if (user) {
      loadRecordings()
    }
  }, [user])

  useEffect(() => {
    filterAndSortRecordings()
  }, [recordings, searchTerm, statusFilter, sourceFilter, userFilter, sortBy, sortOrder])

  const loadRecordings = async () => {
    try {
      setLoading(true)
      console.log('Loading recordings...')

      const result = await listAllAudio({
        limit: 100,
        page: 1,
        sortBy: 'created_at',
        sortOrder: 'desc'
      })

      console.log(`Loaded ${result.audio_records.length} recordings`)

      // Get unique user IDs and fetch usernames
      const userIds = [...new Set(result.audio_records.map(item => item.user_id))]
      const usernames = await getUsernames(userIds)

      // Transform hierarchical data to flat structure for UI compatibility
      const transformedData: ManagedRecording[] = result.audio_records.map(item => ({
        ...item,
        username: usernames[item.user_id] || item.user_id,
        // Flatten transcription data
        transcription_content: item.transcriptions?.primary?.content || '',
        // Flatten metadata
        gender: item.metadata?.details?.gender || '',
        language: item.metadata?.details?.language || 'masalit',
        // Flatten review status
        action: item.review?.status?.action || 'pending',
        approved: item.review?.status?.action === 'approved' ? 1 : 0,
        reviewed_at: item.review?.status?.reviewed_at || '',
        reviewed_by: item.review?.status?.reviewed_by || '',
        review_notes: item.review?.status?.feedback || '',
        // Flatten training status
        trained_asr: item.training?.status?.trained_asr || false,
        tts_trained: item.training?.status?.tts_trained || false,
        quality_score: 3,
        file_size: Math.floor(Math.random() * 10000000), // Mock data
        download_count: Math.floor(Math.random() * 100),
        last_accessed: new Date().toISOString(),
      }))

      setRecordings(transformedData)
      calculateAnalytics(transformedData)
      console.log('Recordings loaded and transformed successfully')
    } catch (error) {
      console.error('Error loading recordings:', error)
      toast({
        title: "Error",
        description: "Failed to load recordings",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const calculateAnalytics = (data: ManagedRecording[]) => {
    const analytics: Analytics = {
      totalRecordings: data.length,
      approvedRecordings: data.filter(r => r.action === 'approved').length,
      pendingRecordings: data.filter(r => r.action === 'pending').length,
      rejectedRecordings: data.filter(r => r.action === 'rejected').length,
      totalDuration: data.reduce((sum, r) => sum + r.duration, 0),
      averageQuality: data.reduce((sum, r) => sum + (r.quality_score || 3), 0) / data.length,
      uniqueUsers: new Set(data.map(r => r.user_id)).size,
      storageUsed: data.reduce((sum, r) => sum + (r.file_size || 0), 0)
    }
    setAnalytics(analytics)
  }

  const filterAndSortRecordings = () => {
    let filtered = recordings

    if (searchTerm) {
      filtered = filtered.filter(recording =>
        recording.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        recording.transcription_content?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        recording.username.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (statusFilter !== "all") {
      filtered = filtered.filter(recording => recording.action === statusFilter)
    }

    if (sourceFilter !== "all") {
      filtered = filtered.filter(recording => recording.source === sourceFilter)
    }

    if (userFilter) {
      filtered = filtered.filter(recording =>
        recording.username.toLowerCase().includes(userFilter.toLowerCase())
      )
    }

    // Sort recordings
    filtered.sort((a, b) => {
      let aValue: any = a[sortBy as keyof ManagedRecording]
      let bValue: any = b[sortBy as keyof ManagedRecording]

      // Handle date sorting
      if (sortBy === 'created_at' || sortBy === 'reviewed_at' || sortBy === 'last_accessed') {
        aValue = new Date(aValue || 0).getTime()
        bValue = new Date(bValue || 0).getTime()
      }

      // Handle string sorting
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    setFilteredRecordings(filtered)
  }

  const handleBulkAction = async (action: string) => {
    if (selectedRecordings.size === 0) {
      toast({
        title: "No recordings selected",
        description: "Please select recordings to perform bulk actions",
        variant: "destructive",
      })
      return
    }

    try {
      const recordingIds = Array.from(selectedRecordings)

      switch (action) {
        case "approve":
          await bulkUpdateReviewStatus(recordingIds, {
            action: 'approved',
            reviewed_by: user?.id,
            reviewed_at: new Date().toISOString(),
            is_flagged: false
          })
          break
        case "reject":
          await bulkUpdateReviewStatus(recordingIds, {
            action: 'rejected',
            reviewed_by: user?.id,
            reviewed_at: new Date().toISOString(),
            is_flagged: false
          })
          break
        case "delete":
          for (const id of recordingIds) {
            await deleteAudioRecord(id)
          }
          break
      }

      await loadRecordings()
      setSelectedRecordings(new Set())

      toast({
        title: "Success",
        description: `Bulk ${action} completed for ${recordingIds.length} recordings`,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to perform bulk ${action}`,
        variant: "destructive",
      })
    }
  }

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const formatFileSize = (bytes: number) => {
    const mb = bytes / (1024 * 1024)
    return `${mb.toFixed(1)} MB`
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A'

    const date = new Date(dateString)
    if (isNaN(date.getTime())) return 'Invalid Date'

    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const toggleRecordingSelection = (recordingId: string) => {
    const newSelection = new Set(selectedRecordings)
    if (newSelection.has(recordingId)) {
      newSelection.delete(recordingId)
    } else {
      newSelection.add(recordingId)
    }
    setSelectedRecordings(newSelection)
  }

  const selectAllRecordings = () => {
    if (selectedRecordings.size === filteredRecordings.length) {
      setSelectedRecordings(new Set())
    } else {
      setSelectedRecordings(new Set(filteredRecordings.map(r => r.id)))
    }
  }

  const handleDelete = (recording: ManagedRecording) => {
    setRecordingToDelete(recording)
    setShowDeleteDialog(true)
  }

  const confirmDelete = async () => {
    if (!recordingToDelete) return

    try {
      await deleteAudioRecord(recordingToDelete.id)
      await loadRecordings()
      setShowDeleteDialog(false)
      setRecordingToDelete(null)
      toast({
        title: "Success",
        description: "Recording deleted successfully",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete recording",
        variant: "destructive",
      })
    }
  }

  const handleEdit = (recording: ManagedRecording) => {
    setEditingRecording({ ...recording })
  }

  const saveEdit = async () => {
    if (!editingRecording) return

    try {
      await updateAudioRecord(editingRecording.id, {
        title: editingRecording.title,
        transcription_content: editingRecording.transcription_content
      })

      await loadRecordings()
      setEditingRecording(null)
      toast({
        title: "Success",
        description: "Recording updated successfully",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update recording",
        variant: "destructive",
      })
    }
  }

  const toggleAudio = (audioUrl: string) => {
    if (playingAudio === audioUrl) {
      setPlayingAudio(null)
    } else {
      setPlayingAudio(audioUrl)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Loading recordings...</div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Managed Recordings</h1>
          <p className="text-muted-foreground">
            Manage and analyze all audio recordings in the system
          </p>
        </div>
      </div>

      {/* Analytics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Recordings</CardTitle>
            <FileAudio className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.totalRecordings}</div>
            <p className="text-xs text-muted-foreground">
              {analytics.totalDuration > 0 && `${Math.round(analytics.totalDuration / 60)} minutes total`}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{analytics.approvedRecordings}</div>
            <p className="text-xs text-muted-foreground">
              {analytics.totalRecordings > 0 && `${Math.round((analytics.approvedRecordings / analytics.totalRecordings) * 100)}% approval rate`}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{analytics.pendingRecordings}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting review
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Unique Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.uniqueUsers}</div>
            <p className="text-xs text-muted-foreground">
              Contributors
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Filters and Actions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search recordings..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            <div className="flex-1">
              <Input
                placeholder="Filter by username..."
                value={userFilter}
                onChange={(e) => setUserFilter(e.target.value)}
              />
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>

            <Select value={sourceFilter} onValueChange={setSourceFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by source" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Sources</SelectItem>
                <SelectItem value="direct_upload">Direct Upload</SelectItem>
                <SelectItem value="bulk_upload">Bulk Upload</SelectItem>
                <SelectItem value="recording">Recording</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex flex-col sm:flex-row gap-4">
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created_at">Date Created</SelectItem>
                <SelectItem value="reviewed_at">Date Reviewed</SelectItem>
                <SelectItem value="last_accessed">Last Accessed</SelectItem>
                <SelectItem value="username">Username</SelectItem>
                <SelectItem value="title">Title</SelectItem>
                <SelectItem value="duration">Duration</SelectItem>
                <SelectItem value="action">Status</SelectItem>
                <SelectItem value="file_size">File Size</SelectItem>
              </SelectContent>
            </Select>

            <Select value={sortOrder} onValueChange={setSortOrder}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Order" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="desc">Newest First</SelectItem>
                <SelectItem value="asc">Oldest First</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Bulk Actions */}
          {selectedRecordings.size > 0 && (
            <div className="flex gap-2 p-4 bg-muted rounded-lg">
              <span className="text-sm text-muted-foreground">
                {selectedRecordings.size} recording(s) selected
              </span>
              <div className="flex gap-2 ml-auto">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkAction("approve")}
                  className="text-green-600 hover:text-green-700"
                >
                  <CheckCircle className="h-4 w-4 mr-1" />
                  Approve
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkAction("reject")}
                  className="text-red-600 hover:text-red-700"
                >
                  <XCircle className="h-4 w-4 mr-1" />
                  Reject
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkAction("delete")}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4 mr-1" />
                  Delete
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recordings Table */}
      <Card>
        <CardHeader>
          <CardTitle>Recordings ({filteredRecordings.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Table Header */}
            <div className="flex items-center space-x-4 p-4 bg-muted rounded-lg">
              <Checkbox
                checked={selectedRecordings.size === filteredRecordings.length && filteredRecordings.length > 0}
                onCheckedChange={selectAllRecordings}
              />
              <div className="flex-1 grid grid-cols-6 gap-4 text-sm font-medium">
                <div>Title</div>
                <div>User</div>
                <div>Status</div>
                <div>Duration</div>
                <div>Created</div>
                <div>Actions</div>
              </div>
            </div>

            {/* Table Rows */}
            <div className="space-y-2">
              {filteredRecordings.map((recording) => (
                <div key={recording.id} className="flex items-center space-x-4 p-4 border rounded-lg hover:bg-muted/50">
                  <Checkbox
                    checked={selectedRecordings.has(recording.id)}
                    onCheckedChange={() => toggleRecordingSelection(recording.id)}
                  />

                  <div className="flex-1 grid grid-cols-6 gap-4 items-center">
                    {/* Title */}
                    <div className="space-y-1">
                      <div className="font-medium">{recording.title}</div>
                      <div className="text-xs text-muted-foreground">
                        {recording.format} • {formatFileSize(recording.file_size || 0)}
                      </div>
                    </div>

                    {/* User */}
                    <div className="text-sm">
                      {recording.username}
                    </div>

                    {/* Status */}
                    <div>
                      <Badge variant={
                        recording.action === 'approved' ? 'default' :
                        recording.action === 'rejected' ? 'destructive' :
                        'secondary'
                      }>
                        {recording.action}
                      </Badge>
                    </div>

                    {/* Duration */}
                    <div className="text-sm">
                      {formatDuration(recording.duration)}
                    </div>

                    {/* Created */}
                    <div className="text-sm">
                      {formatDate(recording.created_at)}
                    </div>

                    {/* Actions */}
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => toggleAudio(recording.audio_url)}
                      >
                        {playingAudio === recording.audio_url ? (
                          <Pause className="h-4 w-4" />
                        ) : (
                          <Play className="h-4 w-4" />
                        )}
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEdit(recording)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDelete(recording)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {filteredRecordings.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                No recordings found matching your criteria.
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Recording</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{recordingToDelete?.title}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-red-600 hover:bg-red-700">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Edit Dialog */}
      {editingRecording && (
        <AlertDialog open={!!editingRecording} onOpenChange={() => setEditingRecording(null)}>
          <AlertDialogContent className="max-w-2xl">
            <AlertDialogHeader>
              <AlertDialogTitle>Edit Recording</AlertDialogTitle>
              <AlertDialogDescription>
                Update the title and transcription for this recording.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Title</label>
                <Input
                  value={editingRecording.title}
                  onChange={(e) => setEditingRecording({
                    ...editingRecording,
                    title: e.target.value
                  })}
                />
              </div>
              <div>
                <label className="text-sm font-medium">Transcription</label>
                <Textarea
                  value={editingRecording.transcription_content || ''}
                  onChange={(e) => setEditingRecording({
                    ...editingRecording,
                    transcription_content: e.target.value
                  })}
                  rows={6}
                />
              </div>
            </div>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={saveEdit}>
                Save Changes
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}

      {/* Audio Player */}
      {playingAudio && (
        <div className="fixed bottom-4 right-4 bg-background border rounded-lg p-4 shadow-lg">
          <audio
            src={playingAudio}
            controls
            autoPlay
            onEnded={() => setPlayingAudio(null)}
            className="w-64"
          />
        </div>
      )}
    </div>
  )
}