#!/bin/bash

# Development startup script for local laptop

echo "🚀 Starting Masalit AI Platform (Development Mode)"
echo "=================================================="

# Create logs directory
mkdir -p logs

echo "Starting backend (development mode)..."
python start-backend.py > logs/backend-dev.log 2>&1 &
BACKEND_PID=$!

sleep 3

echo "Starting frontend (development mode)..."
if command -v pnpm &> /dev/null; then
    pnpm dev > logs/frontend-dev.log 2>&1 &
else
    npm run dev > logs/frontend-dev.log 2>&1 &
fi
FRONTEND_PID=$!

echo ""
echo "🎯 Development Services Started:"
echo "  Frontend: http://localhost:3000"
echo "  Backend:  http://127.0.0.1:8000"
echo "  API Docs: http://127.0.0.1:8000/docs"
echo ""
echo "📝 Logs:"
echo "  Backend:  logs/backend-dev.log"
echo "  Frontend: logs/frontend-dev.log"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for interrupt
trap 'kill $BACKEND_PID $FRONTEND_PID; exit' INT
wait
