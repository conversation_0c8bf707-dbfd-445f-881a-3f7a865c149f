"use client"

import { useAuth } from '@/components/auth-provider'
import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function TestAuthPage() {
  const { user, isLoading } = useAuth()
  const [testResults, setTestResults] = useState<any>(null)
  const [testing, setTesting] = useState(false)

  const testAuthentication = async () => {
    setTesting(true)
    const results: any = {
      clientAuth: null,
      sessionCookie: null,
      apiCall: null,
      preferences: null,
      activity: null
    }

    try {
      // Test 1: Client-side auth state
      results.clientAuth = {
        isAuthenticated: !!user,
        userId: user?.id,
        email: user?.email,
        role: user?.role
      }

      // Test 2: Check if session cookie exists
      try {
        const sessionResponse = await fetch('/api/auth/session', {
          method: 'GET',
          credentials: 'include'
        })
        results.sessionCookie = {
          status: sessionResponse.status,
          hasSession: sessionResponse.ok
        }
      } catch (error) {
        results.sessionCookie = { error: error.message }
      }

      // Test 3: Test API call that requires auth
      if (user?.id) {
        try {
          const userResponse = await fetch(`/api/users/${user.id}`, {
            credentials: 'include'
          })
          results.apiCall = {
            status: userResponse.status,
            success: userResponse.ok,
            error: userResponse.ok ? null : await userResponse.text()
          }
        } catch (error) {
          results.apiCall = { error: error.message }
        }

        // Test 4: Test preferences API
        try {
          const prefsResponse = await fetch(`/api/users/${user.id}/preferences`, {
            credentials: 'include'
          })
          results.preferences = {
            status: prefsResponse.status,
            success: prefsResponse.ok,
            error: prefsResponse.ok ? null : await prefsResponse.text()
          }
        } catch (error) {
          results.preferences = { error: error.message }
        }

        // Test 5: Test activity API
        try {
          const activityResponse = await fetch(`/api/users/${user.id}/activity?limit=5`, {
            credentials: 'include'
          })
          results.activity = {
            status: activityResponse.status,
            success: activityResponse.ok,
            error: activityResponse.ok ? null : await activityResponse.text()
          }
        } catch (error) {
          results.activity = { error: error.message }
        }

        // Test 6: Test detailed auth endpoint
        try {
          const authTestResponse = await fetch('/api/test-auth', {
            credentials: 'include'
          })
          results.detailedAuth = {
            status: authTestResponse.status,
            success: authTestResponse.ok,
            data: authTestResponse.ok ? await authTestResponse.json() : await authTestResponse.text()
          }
        } catch (error) {
          results.detailedAuth = { error: error.message }
        }
      }

    } catch (error) {
      results.error = error.message
    }

    setTestResults(results)
    setTesting(false)
  }

  const refreshSession = async () => {
    if (user) {
      try {
        // Get fresh ID token
        const idToken = await user.getIdToken(true)
        
        // Create new session cookie
        const response = await fetch('/api/auth/session', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ idToken }),
        })

        if (response.ok) {
          alert('Session refreshed successfully!')
        } else {
          alert('Failed to refresh session')
        }
      } catch (error) {
        alert('Error refreshing session: ' + error.message)
      }
    }
  }

  if (isLoading) {
    return <div className="p-8">Loading...</div>
  }

  return (
    <div className="container mx-auto p-8">
      <Card>
        <CardHeader>
          <CardTitle>Authentication Test Page</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold">Current User:</h3>
            <pre className="bg-gray-100 p-2 rounded text-sm">
              {JSON.stringify(user, null, 2)}
            </pre>
          </div>

          <div className="flex gap-2">
            <Button onClick={testAuthentication} disabled={testing}>
              {testing ? 'Testing...' : 'Test Authentication'}
            </Button>
            <Button onClick={refreshSession} disabled={!user} variant="outline">
              Refresh Session
            </Button>
          </div>

          {testResults && (
            <div>
              <h3 className="font-semibold">Test Results:</h3>
              <pre className="bg-gray-100 p-2 rounded text-sm overflow-auto max-h-96">
                {JSON.stringify(testResults, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
