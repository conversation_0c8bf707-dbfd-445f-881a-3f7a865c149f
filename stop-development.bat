@echo off
title Stop Masalit AI Development Services
color 0C

echo.
echo ========================================================
echo  Stopping Masalit AI Development Services
echo ========================================================
echo.

echo [1/3] Stopping Python backend processes...
taskkill /f /im python.exe 2>nul
if %errorlevel% == 0 (
    echo ✅ Python processes stopped
) else (
    echo ℹ️  No Python processes found
)

echo.
echo [2/3] Stopping Node.js frontend processes...
taskkill /f /im node.exe 2>nul
if %errorlevel% == 0 (
    echo ✅ Node.js processes stopped
) else (
    echo ℹ️  No Node.js processes found
)

echo.
echo [3/3] Cleaning up process files...
if exist "backend.pid" del "backend.pid"
if exist "frontend.pid" del "frontend.pid"

echo.
echo ========================================================
echo  🛑 ALL DEVELOPMENT SERVICES STOPPED
echo ========================================================
echo.
echo All Masalit AI development services have been stopped.
echo You can now safely close this window.
echo.
echo To restart development:
echo - Regular HTTP: start-development.bat
echo - With HTTPS:   start-development-ssl.bat
echo.
pause
