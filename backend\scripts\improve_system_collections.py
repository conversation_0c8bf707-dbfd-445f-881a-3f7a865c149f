#!/usr/bin/env python3
"""
Improve System Collections Structure

Consolidates system settings, logs, and monitoring into a cleaner, more logical structure.

CURRENT STRUCTURE:
- system_logs (scattered)
- system_settings (scattered)  
- _migration_logs (temporary)
- _migration_backup (temporary)

NEW IMPROVED STRUCTURE:
- system/config/* (all system configuration)
- logs/* (all logs organized by type)
- monitoring/* (all monitoring data)
"""

import sys
import os
from datetime import datetime
from typing import Dict, Any, List

# Add backend to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.firebase_clean import clean_firebase_service
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SystemCollectionsImprover:
    def __init__(self):
        self.db = None
        self.improvement_log = []
        
    def initialize(self):
        """Initialize Firebase connection"""
        try:
            clean_firebase_service.initialize()
            self.db = clean_firebase_service.db
            logger.info("✅ Firebase initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Firebase: {e}")
            raise
    
    def log_improvement(self, action: str, details: str):
        """Log improvement actions"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "action": action,
            "details": details
        }
        self.improvement_log.append(log_entry)
        logger.info(f"📝 {action}: {details}")
    
    def improve_system_settings(self):
        """Consolidate system settings into unified structure"""
        logger.info("🔄 Improving system settings structure...")
        
        try:
            # 1. Migrate system_settings to system/config/*
            try:
                settings_docs = self.db.collection('system_settings').get()
                for doc in settings_docs:
                    doc_id = doc.id
                    data = doc.to_dict()
                    
                    # Save to new unified structure
                    new_doc_ref = self.db.collection('system').document('config').collection('settings').document(doc_id)
                    new_doc_ref.set({
                        **data,
                        'migrated_at': datetime.now().isoformat(),
                        'migrated_from': f'system_settings/{doc_id}'
                    })
                    
                    self.log_improvement("IMPROVE_SYSTEM_SETTINGS", f"system_settings/{doc_id} -> system/config/settings/{doc_id}")
                    
            except Exception as e:
                logger.error(f"❌ Error improving system settings: {e}")
            
            # 2. Create default system configuration if none exists
            try:
                default_configs = {
                    'logging': {
                        'level': 'INFO',
                        'max_file_size_mb': 100,
                        'retention_days': 30,
                        'enabled_sources': ['system', 'training', 'validation', 'api']
                    },
                    'security': {
                        'max_login_attempts': 5,
                        'session_timeout_minutes': 60,
                        'require_2fa': False,
                        'allowed_origins': ['localhost:3000', 'buragatechnologies.com']
                    },
                    'performance': {
                        'max_concurrent_trainings': 1,
                        'max_concurrent_validations': 3,
                        'cache_ttl_minutes': 15,
                        'request_timeout_seconds': 30
                    },
                    'features': {
                        'asr_training_enabled': True,
                        'tts_training_enabled': True,
                        'validation_enabled': True,
                        'analytics_enabled': True,
                        'real_time_updates': True
                    }
                }
                
                for config_type, config_data in default_configs.items():
                    config_ref = self.db.collection('system').document('config').collection('settings').document(config_type)
                    config_doc = config_ref.get()
                    
                    if not config_doc.exists:
                        config_ref.set({
                            **config_data,
                            'created_at': datetime.now().isoformat(),
                            'created_by': 'system_improvement_script'
                        })
                        
                        self.log_improvement("CREATE_SYSTEM_CONFIG", f"Created default {config_type} configuration")
                
            except Exception as e:
                logger.error(f"❌ Error creating default system config: {e}")
                
        except Exception as e:
            logger.error(f"❌ Error in system settings improvement: {e}")
    
    def improve_logs_structure(self):
        """Consolidate all logs into unified structure"""
        logger.info("🔄 Improving logs structure...")
        
        try:
            # 1. Migrate system_logs to logs/system/*
            try:
                system_logs = self.db.collection('system_logs').get()
                for doc in system_logs:
                    log_id = doc.id
                    data = doc.to_dict()
                    
                    # Save to new unified structure
                    new_log_ref = self.db.collection('logs').document('system').collection('entries').document(log_id)
                    new_log_ref.set({
                        **data,
                        'migrated_at': datetime.now().isoformat(),
                        'migrated_from': f'system_logs/{log_id}'
                    })
                    
                    self.log_improvement("IMPROVE_LOGS", f"system_logs/{log_id} -> logs/system/entries/{log_id}")
                    
            except Exception as e:
                logger.error(f"❌ Error improving system logs: {e}")
            
            # 2. Migrate migration logs to logs/migration/*
            try:
                migration_logs = self.db.collection('_migration_logs').get()
                for doc in migration_logs:
                    log_id = doc.id
                    data = doc.to_dict()
                    
                    # Save to new unified structure
                    new_log_ref = self.db.collection('logs').document('migration').collection('entries').document(log_id)
                    new_log_ref.set({
                        **data,
                        'migrated_at': datetime.now().isoformat(),
                        'migrated_from': f'_migration_logs/{log_id}'
                    })
                    
                    self.log_improvement("IMPROVE_MIGRATION_LOGS", f"_migration_logs/{log_id} -> logs/migration/entries/{log_id}")
                    
            except Exception as e:
                logger.error(f"❌ Error improving migration logs: {e}")
                
        except Exception as e:
            logger.error(f"❌ Error in logs structure improvement: {e}")
    
    def create_monitoring_structure(self):
        """Create unified monitoring structure"""
        logger.info("🔄 Creating monitoring structure...")
        
        try:
            # Create monitoring structure with initial data
            monitoring_data = {
                'health': {
                    'last_check': datetime.now().isoformat(),
                    'status': 'healthy',
                    'services': {
                        'firebase': 'healthy',
                        'backend': 'healthy',
                        'frontend': 'healthy'
                    }
                },
                'metrics': {
                    'last_updated': datetime.now().isoformat(),
                    'total_users': 0,
                    'total_audio_files': 0,
                    'total_training_sessions': 0,
                    'total_validations': 0
                },
                'alerts': {
                    'last_check': datetime.now().isoformat(),
                    'active_alerts': [],
                    'alert_history': []
                }
            }
            
            for monitor_type, monitor_data in monitoring_data.items():
                monitor_ref = self.db.collection('monitoring').document(monitor_type)
                monitor_ref.set({
                    **monitor_data,
                    'created_at': datetime.now().isoformat(),
                    'created_by': 'system_improvement_script'
                })
                
                self.log_improvement("CREATE_MONITORING", f"Created monitoring/{monitor_type}")
                
        except Exception as e:
            logger.error(f"❌ Error creating monitoring structure: {e}")
    
    def create_backup(self):
        """Create backup of current structure before improvement"""
        logger.info("💾 Creating backup of current system structure...")
        
        backup_data = {
            'backup_created_at': datetime.now().isoformat(),
            'collections_to_improve': [
                'system_logs',
                'system_settings',
                '_migration_logs',
                '_migration_backup'
            ],
            'improvement_type': 'system_collections_consolidation'
        }
        
        self.db.collection('_improvement_backup').document('system_structure_backup').set(backup_data)
        self.log_improvement("BACKUP", "Created backup record for system structure improvement")
    
    def save_improvement_log(self):
        """Save improvement log to Firebase"""
        try:
            log_doc = {
                'improvement_completed_at': datetime.now().isoformat(),
                'total_actions': len(self.improvement_log),
                'actions': self.improvement_log
            }
            
            self.db.collection('logs').document('system').collection('entries').document(f'improvement_{datetime.now().strftime("%Y%m%d_%H%M%S")}').set(log_doc)
            logger.info(f"✅ Improvement log saved with {len(self.improvement_log)} actions")
            
        except Exception as e:
            logger.error(f"❌ Error saving improvement log: {e}")
    
    def run_improvement(self, create_backup: bool = True):
        """Run the complete system structure improvement"""
        try:
            self.initialize()
            
            if create_backup:
                self.create_backup()
            
            logger.info("🚀 Starting system collections improvement...")
            
            # Run improvements
            self.improve_system_settings()
            self.improve_logs_structure()
            self.create_monitoring_structure()
            
            # Save log
            self.save_improvement_log()
            
            logger.info("✅ System structure improvement completed successfully!")
            logger.info(f"📊 Total actions performed: {len(self.improvement_log)}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ System improvement failed: {e}")
            return False

def main():
    """Main improvement function"""
    improver = SystemCollectionsImprover()
    
    print("🔄 System Collections Structure Improvement")
    print("=" * 50)
    print("This will consolidate system settings, logs, and monitoring")
    print("into a cleaner, more organized structure.")
    print()
    
    confirm = input("Do you want to proceed? (y/N): ").lower().strip()
    
    if confirm == 'y':
        success = improver.run_improvement()
        if success:
            print("\n✅ System structure improvement completed successfully!")
            print("📊 New structure:")
            print("   - system/config/* (unified system configuration)")
            print("   - logs/* (all logs organized by type)")
            print("   - monitoring/* (unified monitoring data)")
        else:
            print("\n❌ System improvement failed. Check logs for details.")
    else:
        print("System improvement cancelled.")

if __name__ == "__main__":
    main()
