"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress, getDynamicTextColor } from "@/components/ui/progress"
import { useState, useEffect } from "react"
import { useToast } from "@/hooks/use-toast"
import { 
  Loader2, 
  AlertCircle, 
  Settings, 
  RefreshCw, 
  Trash2, 
  RotateCcw,
  History,
  Clock,
  CheckCircle,
  Package,
  GitCompare,
  ArrowLeft
} from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"
import { db } from "@/lib/firebase"
import { collection, query, where, getDocs, orderBy, limit, onSnapshot, doc, setDoc, writeBatch, getDoc } from "firebase/firestore"
import Link from "next/link"

interface TTSSettings {
  epochs: number
  learning_rate: number
  model_name: string
  validation_split: number
  early_stopping_patience: number
  use_augmentation: boolean
  samples_per_sample: number
}

interface TrainingStatus {
  status: string
  current_epoch: number
  total_epochs: number
  progress: number
  current_accuracy: number
  current_confidence: number
  current_wer: number
  current_cer: number
  current_ser: number
  samples_processed: number
  total_samples: number
  training_progress: number
  error: string | null
  model_version: string
  start_time: string
  end_time: string
}

interface TrainingSchedule {
  enabled: boolean
  interval: string
  time: string
}

interface Stats {
  total_audio_files?: number;
  approved_audio_files?: number;
  trained_samples?: number;
  untrained_samples?: number;
  gender_distribution?: {
    male: number;
    female: number;
    trained: {
      male: number;
      female: number;
    };
  };
  last_prediction?: {
    text: string;
    confidence: number;
    segments: any[];
    language: string;
    created_at: string;
  };
  transcriptions?: Record<string, any[]>;
}

export default function TTSTrainingPage() {
  const { toast } = useToast()
  const [isTraining, setIsTraining] = useState(false)
  const [status, setStatus] = useState<TrainingStatus>({
    status: "not_started",
    current_epoch: 0,
    total_epochs: 0,
    progress: 0,
    current_accuracy: 0,
    current_confidence: 0,
    current_wer: 0,
    current_cer: 0,
    current_ser: 0,
    samples_processed: 0,
    total_samples: 0,
    training_progress: 0,
    error: null,
    model_version: "",
    start_time: "",
    end_time: ""
  })
  const [isLoading, setIsLoading] = useState(true)
  const [isServerAvailable, setIsServerAvailable] = useState(true)
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)
  const [settings, setSettings] = useState<TTSSettings>({
    epochs: 5,
    learning_rate: 0.001,
    model_name: "small",
    validation_split: 0.2,
    early_stopping_patience: 3,
    use_augmentation: false,
    samples_per_sample: 1
  })
  const [stats, setStats] = useState<Stats | null>(null)
  const [history, setHistory] = useState<any[]>([])
  const [schedule, setSchedule] = useState<TrainingSchedule>({
    enabled: false,
    interval: "monthly",
    time: "00:00"
  })
  const [isHistoryOpen, setIsHistoryOpen] = useState(false)
  const [isScheduleOpen, setIsScheduleOpen] = useState(false)
  const [isResetConfirmOpen, setIsResetConfirmOpen] = useState(false)
  const [isTrainConfirmOpen, setIsTrainConfirmOpen] = useState(false)
  const [isStopConfirmOpen, setIsStopConfirmOpen] = useState(false)
  const [isStatsLoading, setIsStatsLoading] = useState(true)
  const [isTranscriptionsLoading, setIsTranscriptionsLoading] = useState(true)

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const settingsRef = doc(db, 'training_settings', 'tts')
        const settingsDoc = await getDoc(settingsRef)
        
        if (settingsDoc.exists()) {
          setSettings(settingsDoc.data() as TrainingSettings)
        }
      } catch (error) {
        console.error("Error fetching settings:", error)
      }
    }

    fetchSettings()
  }, [])

  useEffect(() => {
    // Set up real-time listeners for audio files
    const audioRef = collection(db, 'audio')
    const approvedQuery = query(audioRef, where('action', '==', 'approved'))
    
    // Initial stats calculation
    const calculateStats = async (docs: any[]) => {
      let total_audio_files = 0
      let approved_audio_files = 0
      let trained_samples = 0
      let male = 0
      let female = 0
      let trained_male = 0
      let trained_female = 0
      
      // Process each approved audio file
      for (const doc of docs) {
        const data = doc.data()
        total_audio_files++
        approved_audio_files++
        
        // Check gender and training status
        if (data.gender === 'male') {
          male++
          if (data.trained_tts) {
            trained_male++
            trained_samples++
          }
        } else if (data.gender === 'female') {
          female++
          if (data.trained_tts) {
            trained_female++
            trained_samples++
          }
        }
      }

      // Update audio stats immediately
      setStats((prev: Stats | null) => ({
        ...prev,
        total_audio_files,
        approved_audio_files,
        trained_samples,
        untrained_samples: approved_audio_files - trained_samples,
        gender_distribution: {
          male,
          female,
          trained: {
            male: trained_male,
            female: trained_female
          }
        }
      }))
      setIsStatsLoading(false)

      // Load transcriptions in parallel for the first 10 documents
      const transcriptionPromises = docs.slice(0, 10).map(async (doc) => {
        const transcriptionRef = collection(db, 'transcription')
        const transcriptionQuery = query(transcriptionRef, where('audio_id', '==', doc.id))
        const transcriptionSnapshot = await getDocs(transcriptionQuery)
        
        const transcriptions = transcriptionSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }))

        return { audioId: doc.id, transcriptions }
      })

      const transcriptionResults = await Promise.all(transcriptionPromises)
      
      // Update transcriptions
      setStats((prev: Stats | null) => ({
        ...prev,
        transcriptions: transcriptionResults.reduce((acc, { audioId, transcriptions }) => ({
          ...acc,
          [audioId]: transcriptions
        }), {})
      }))
      setIsTranscriptionsLoading(false)
    }

    const audioUnsubscribe = onSnapshot(approvedQuery, async (snapshot) => {
      await calculateStats(snapshot.docs)
    })

    // Set up real-time listener for training status
    const statusRef = doc(db, 'training_status', 'tts')
    const statusUnsubscribe = onSnapshot(statusRef, async (doc) => {
      if (doc.exists()) {
        const data = doc.data()
        setStatus({
          status: data.status || 'not_started',
          current_epoch: data.current_epoch || 0,
          total_epochs: data.total_epochs || 0,
          progress: data.progress || 0,
          current_accuracy: data.current_accuracy || 0,
          current_confidence: data.current_confidence || 0,
          current_wer: data.current_wer || 0,
          current_cer: data.current_cer || 0,
          current_ser: data.current_ser || 0,
          samples_processed: data.samples_processed || 0,
          total_samples: data.total_samples || 0,
          training_progress: data.training_progress || 0,
          error: data.error,
          model_version: data.model_version,
          start_time: data.start_time,
          end_time: data.end_time
        })
        setIsTraining(data.status === 'training')
      }
    })

    // Set up real-time listener for TTS history
    const historyRef = collection(db, 'training_history')
    const historyQuery = query(historyRef, orderBy('timestamp', 'desc'))
    const historyUnsubscribe = onSnapshot(historyQuery, (snapshot) => {
      const historyData = snapshot.docs
        .map(doc => ({
          id: doc.id,
          ...doc.data()
        }))
        .filter((item: any) => item.model_type === 'tts') // Filter client-side to avoid index requirement
      setHistory(historyData)
    })

    // Set up real-time listener for TTS predictions
    const predictionsDocRef = doc(db, 'training_predictions', 'tts')
    const predictionsRef = collection(predictionsDocRef, 'entries')
    const predictionsQuery = query(predictionsRef, orderBy('created_at', 'desc'), limit(1))
    const predictionsUnsubscribe = onSnapshot(predictionsQuery, (snapshot) => {
      if (!snapshot.empty) {
        const predictionData = snapshot.docs[0].data()
        setStats((prev: Stats | null) => ({
          ...prev,
          last_prediction: {
            text: predictionData.text,
            confidence: predictionData.confidence,
            segments: predictionData.segments,
            language: predictionData.language,
            created_at: predictionData.created_at
          }
        }))
      }
    })

    // Set up real-time listener for TTS schedule
    const scheduleRef = doc(db, 'training_schedules', 'tts')
    const scheduleUnsubscribe = onSnapshot(scheduleRef, (doc) => {
      if (doc.exists()) {
        const data = doc.data()
        setSchedule({
          enabled: data.enabled || false,
          interval: data.interval || 'daily',
          time: data.time || '00:00'
        })
      }
    })

    return () => {
      audioUnsubscribe()
      statusUnsubscribe()
      historyUnsubscribe()
      predictionsUnsubscribe()
      scheduleUnsubscribe()
    }
  }, [])

  const handleStartTraining = async () => {
    try {
      const available = await checkServerStatus()
      if (!available) {
        toast({
          title: "Server Unavailable",
          description: "The TTS server is not running. Please start the server to use TTS features.",
          variant: "destructive",
        })
        return
      }

      // Clear any existing errors before starting
      const statusRef = doc(db, 'training_status', 'tts')
      await setDoc(statusRef, {
        status: "training",
        error: null,
        start_time: new Date().toISOString(),
        current_epoch: 0,
        total_epochs: settings.epochs,
        samples_processed: 0,
        total_samples: settings.number_of_samples,
        current_accuracy: 0,
        current_confidence: 0,
        training_progress: 0
      }, { merge: true })

      setIsTraining(true)
      await startTraining()
      toast({
        title: "Training started",
        description: "The TTS model training has been initiated.",
      })
    } catch (error) {
      console.error("Error starting training:", error)
      toast({
        title: "Error",
        description: "Failed to start training. Please try again.",
        variant: "destructive",
      })
      setIsTraining(false)
    }
  }

  const handleStopTraining = async () => {
    try {
      setIsTraining(false)
      // TODO: Implement TTS training stop
      toast({
        title: "Training stopped",
        description: "The TTS model training has been stopped.",
      })
    } catch (error) {
      console.error("Error stopping training:", error)
      toast({
        title: "Error",
        description: "Failed to stop training. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleResetTraining = async () => {
    try {
      // Reset status
      const statusRef = doc(db, 'training_status', 'tts')
      await setDoc(statusRef, {
        status: "idle",
        current_epoch: 0,
        total_epochs: 0,
        samples_processed: 0,
        total_samples: 0,
        current_accuracy: 0.0,
        current_confidence: 0.0,
        training_progress: 0.0,
        error: null,
        updated_at: new Date()
      })

      // Reset training flags for all approved audio files
      const audioRef = collection(db, 'audio')
      const approvedQuery = query(audioRef, where('action', '==', 'approved'))
      const audioSnapshot = await getDocs(approvedQuery)
      
      // Use sampled writes for better performance
      const sample = writeBatch(db)
      audioSnapshot.forEach((doc) => {
        sample.update(doc.ref, {
          trained_tts: false,
          training_epoch: null,
          updated_at: new Date()
        })
      })
      
      // Commit the sample update
      await sample.commit()

      // Update local state
      setStatus({
        status: "idle",
        current_epoch: 0,
        total_epochs: 0,
        progress: 0,
        current_accuracy: 0,
        current_confidence: 0,
        current_wer: 0,
        current_cer: 0,
        current_ser: 0,
        samples_processed: 0,
        total_samples: 0,
        training_progress: 0,
        error: null,
        model_version: "",
        start_time: "",
        end_time: ""
      })
      setIsTraining(false)

      toast({
        title: "Training reset",
        description: "All training progress has been reset. Audio and transcription data are preserved.",
      })
    } catch (error) {
      console.error("Error resetting training:", error)
      toast({
        title: "Error",
        description: "Failed to reset training. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleUpdateSettings = async () => {
    try {
      const settingsRef = doc(db, 'training_settings', 'tts')
      await setDoc(settingsRef, {
        ...settings,
        updated_at: new Date()
      }, { merge: true })

      toast({
        title: "Settings updated",
        description: "Training settings have been updated successfully.",
      })
      setIsSettingsOpen(false)
    } catch (error) {
      console.error("Error updating settings:", error)
      toast({
        title: "Error",
        description: "Failed to update settings. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleResetSettings = async () => {
    try {
      const defaultSettings = {
        epochs: 5,
        learning_rate: 0.001,
        model_name: "small",
        validation_split: 0.2,
        early_stopping_patience: 3,
        use_augmentation: false,
        samples_per_sample: 1,
        updated_at: new Date()
      }

      const settingsRef = doc(db, 'training_settings', 'tts')
      await setDoc(settingsRef, defaultSettings)
      
      setSettings(defaultSettings)

      toast({
        title: "Settings reset",
        description: "Training settings have been reset to default values.",
      })
    } catch (error) {
      console.error("Error resetting settings:", error)
      toast({
        title: "Error",
        description: "Failed to reset settings. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleScheduleUpdate = async () => {
    try {
      const scheduleRef = doc(db, 'training_schedules', 'tts')
      await setDoc(scheduleRef, {
        enabled: schedule.enabled,
        interval: schedule.interval,
        time: schedule.time,
        updated_at: new Date()
      })

      toast({
        title: "Schedule updated",
        description: "Training schedule has been updated successfully.",
      })
      setIsScheduleOpen(false)
    } catch (error) {
      console.error("Error updating schedule:", error)
      toast({
        title: "Error",
        description: "Failed to update schedule. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleNumericInput = (value: string, field: keyof TTSSettings) => {
    const numValue = parseFloat(value)
    if (!isNaN(numValue)) {
      setSettings(prev => ({ ...prev, [field]: numValue }))
    }
  }

  const handleRollback = async (version: string) => {
    try {
      const historyRef = doc(db, 'training_history', 'tts', version)
      const historyDoc = await getDoc(historyRef)
      
      if (!historyDoc.exists()) {
        throw new Error('History record not found')
      }

      // Update current model version
      const statusRef = doc(db, 'training_status', 'tts')
      await setDoc(statusRef, {
        ...historyDoc.data(),
        status: 'idle',
        updated_at: new Date()
      })

      toast({
        title: "Model rolled back",
        description: `Successfully rolled back to version ${version}`,
      })
    } catch (error) {
      console.error("Error rolling back model:", error)
      toast({
        title: "Error",
        description: "Failed to rollback model. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">TTS Training</h3>
          <p className="text-sm text-muted-foreground">
            Train and manage the Text-to-Speech model
          </p>
        </div>
        <div className="flex gap-2">
          <Link href="/dashboard/ai">
            <Button variant="outline" size="icon" title="Back to AI Dashboard">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>

          <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="icon" title="Training Settings">
                <Settings className="h-4 w-4" />
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>TTS Training Settings</DialogTitle>
                <DialogDescription>
                  Configure the TTS model training parameters. Settings will persist until changed.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="epochs" className="text-right">
                    Epochs
                  </Label>
                  <Input
                    id="epochs"
                    type="number"
                    min="1"
                    max="100"
                    value={settings.epochs}
                    onChange={(e) => handleNumericInput(e.target.value, 'epochs')}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="learning_rate" className="text-right">
                    Learning Rate
                  </Label>
                  <Input
                    id="learning_rate"
                    type="number"
                    min="0.0001"
                    max="0.1"
                    step="0.0001"
                    value={settings.learning_rate}
                    onChange={(e) => handleNumericInput(e.target.value, 'learning_rate')}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="samples_per_sample" className="text-right">
                    Samples per Training
                  </Label>
                  <div className="col-span-3 space-y-1">
                    <Input
                      id="samples_per_sample"
                      type="number"
                      min="1"
                      value={settings.samples_per_sample}
                      onChange={(e) => handleNumericInput(e.target.value, 'samples_per_sample')}
                    />
                    <p className="text-sm text-muted-foreground">
                      Number of samples to use for training. Set to 1 to train on a single sample.
                    </p>
                  </div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="model_name" className="text-right">
                    Model
                  </Label>
                  <Select
                    value={settings.model_name}
                    onValueChange={(value) => setSettings({ ...settings, model_name: value })}
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select model" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="tiny">Tiny</SelectItem>
                      <SelectItem value="base">Base</SelectItem>
                      <SelectItem value="small">Small</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="large">Large</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="validation_split" className="text-right">
                    Validation Split
                  </Label>
                  <Input
                    id="validation_split"
                    type="number"
                    step="0.1"
                    min="0"
                    max="1"
                    value={settings.validation_split}
                    onChange={(e) => handleNumericInput(e.target.value, 'validation_split')}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="early_stopping" className="text-right">
                    Early Stopping
                  </Label>
                  <Input
                    id="early_stopping"
                    type="number"
                    min="1"
                    value={settings.early_stopping_patience}
                    onChange={(e) => handleNumericInput(e.target.value, 'early_stopping_patience')}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="augmentation" className="text-right">
                    Use Augmentation
                  </Label>
                  <Switch
                    id="augmentation"
                    checked={settings.use_augmentation}
                    onCheckedChange={(checked) => setSettings({ ...settings, use_augmentation: checked })}
                    className="col-span-3"
                  />
                </div>
              </div>
              <DialogFooter className="flex justify-between">
                <Button variant="outline" onClick={handleResetSettings}>
                  Reset to Default
                </Button>
                <Button onClick={handleUpdateSettings}>Save changes</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Dialog open={isHistoryOpen} onOpenChange={setIsHistoryOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="icon" title="Training History">
                <History className="h-4 w-4" />
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>TTS Training History</DialogTitle>
                <DialogDescription>
                  View and manage previous training sessions
                </DialogDescription>
              </DialogHeader>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Version</TableHead>
                    <TableHead>Accuracy</TableHead>
                    <TableHead>Confidence</TableHead>
                    <TableHead>Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {history.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>{new Date(item.trained_at).toLocaleString()}</TableCell>
                      <TableCell>{item.model_version}</TableCell>
                      <TableCell>{Math.round(item.accuracy * 100)}%</TableCell>
                      <TableCell>{Math.round(item.confidence * 100)}%</TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleRollback(item.id)}
                        >
                          Rollback
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </DialogContent>
          </Dialog>

          <Dialog open={isScheduleOpen} onOpenChange={setIsScheduleOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="icon" title="Training Schedule">
                <Clock className="h-4 w-4" />
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>TTS Training Schedule</DialogTitle>
                <DialogDescription>
                  Configure automatic training schedule
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="interval" className="text-right">
                    Interval
                  </Label>
                  <Select
                    value={schedule.interval}
                    onValueChange={(value) => setSchedule({ ...schedule, interval: value })}
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select interval" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="time" className="text-right">
                    Time
                  </Label>
                  <Input
                    id="time"
                    type="time"
                    value={schedule.time}
                    onChange={(e) => setSchedule({ ...schedule, time: e.target.value })}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="enabled" className="text-right">
                    Enabled
                  </Label>
                  <Switch
                    id="enabled"
                    checked={schedule.enabled}
                    onCheckedChange={(checked) => setSchedule({ ...schedule, enabled: checked })}
                    className="col-span-3"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button onClick={handleScheduleUpdate}>Save schedule</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Button 
            variant="destructive" 
            size="icon" 
            onClick={() => setIsResetConfirmOpen(true)}
            title="Reset Training"
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
          <div className="flex gap-4">
            <Button 
              variant="destructive" 
              onClick={() => setIsStopConfirmOpen(true)}
              disabled={!isServerAvailable || !isTraining}
            >
              Stop Training
            </Button>
            <Button 
              onClick={() => setIsTrainConfirmOpen(true)}
              disabled={isTraining || isLoading || !isServerAvailable}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Loading...
                </>
              ) : isTraining ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Training in Progress
                </>
              ) : (
                "Start Training"
              )}
            </Button>
          </div>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader>
            <CardTitle>Training Status</CardTitle>
            <CardDescription>Current training progress</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Training Progress</span>
                  <span className={getDynamicTextColor(Math.round(status.progress * 100), 'inverse')}>
                    {Math.round(status.progress * 100)}%
                  </span>
                </div>
                <Progress
                  value={status.progress * 100}
                  showDynamicColors={true}
                  colorScheme="inverse"
                />
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Current Epoch</span>
                  <span className="text-sm text-muted-foreground">{status.current_epoch}/{status.total_epochs}</span>
                </div>
                <Progress
                  value={(status.current_epoch / status.total_epochs) * 100}
                  showDynamicColors={true}
                  colorScheme="inverse"
                />
              </div>
              {stats?.last_prediction?.created_at && (
                <div className="text-sm text-muted-foreground">
                  Last prediction: {new Date(stats.last_prediction.created_at).toLocaleString()}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Training Data</CardTitle>
            <CardDescription>Available training samples</CardDescription>
          </CardHeader>
          <CardContent>
            {isStatsLoading ? (
              <div className="space-y-4">
                <div className="h-4 w-full animate-pulse bg-muted rounded" />
                <div className="h-4 w-3/4 animate-pulse bg-muted rounded" />
                <div className="h-4 w-1/2 animate-pulse bg-muted rounded" />
              </div>
            ) : (
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Approved Samples</span>
                    <span className="text-sm text-muted-foreground">{stats?.approved_audio_files || 0}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Trained Samples</span>
                    <span className="text-sm text-muted-foreground">{stats?.trained_samples || 0}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Untrained Samples</span>
                    <span className="text-sm text-muted-foreground">{stats?.untrained_samples || 0}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Gender Distribution</span>
                  </div>
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm text-muted-foreground">
                      <span>Male: {stats?.gender_distribution?.male || 0}</span>
                      <span>Trained: {stats?.gender_distribution?.trained?.male || 0}</span>
                    </div>
                    <div className="flex justify-between text-sm text-muted-foreground">
                      <span>Female: {stats?.gender_distribution?.female || 0}</span>
                      <span>Trained: {stats?.gender_distribution?.trained?.female || 0}</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Model Performance</CardTitle>
            <CardDescription>Current model metrics</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Confidence</span>
                  <span className={stats?.last_prediction?.confidence ?
                    getDynamicTextColor(Math.round(stats.last_prediction.confidence * 100), 'inverse') :
                    "text-muted-foreground"
                  }>
                    {stats?.last_prediction?.confidence ? `${Math.round(stats.last_prediction.confidence * 100)}%` : 'N/A'}
                  </span>
                </div>
                <Progress
                  value={stats?.last_prediction?.confidence ? stats.last_prediction.confidence * 100 : 0}
                  showDynamicColors={true}
                  colorScheme="inverse"
                />
              </div>
              {stats?.last_prediction?.language && (
                <div className="text-sm text-muted-foreground">
                  Language: {stats.last_prediction.language}
                </div>
              )}
              {stats?.last_prediction?.text && (
                <div className="text-sm text-muted-foreground truncate">
                  Last prediction: {stats.last_prediction.text}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Training Schedule</CardTitle>
            <CardDescription>Automatic training configuration</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Status</span>
                  <span className="text-sm text-muted-foreground">
                    {schedule.enabled ? 'Enabled' : 'Disabled'}
                  </span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Interval</span>
                  <span className="text-sm text-muted-foreground capitalize">
                    {schedule.interval}
                  </span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Time</span>
                  <span className="text-sm text-muted-foreground">
                    {schedule.time}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <AlertDialog open={isResetConfirmOpen} onOpenChange={setIsResetConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="text-destructive">Reset Training Progress</AlertDialogTitle>
            <AlertDialogDescription>
              This will reset all training progress. Your audio and transcription data will be preserved, but all training flags will be cleared.
              Are you sure you want to continue?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleResetTraining} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">Reset</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={isTrainConfirmOpen} onOpenChange={setIsTrainConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Start Training</AlertDialogTitle>
            <AlertDialogDescription>
              This will start training the TTS model with the current settings. Training may take some time.
              Are you sure you want to start training?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleStartTraining}>Start Training</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={isStopConfirmOpen} onOpenChange={setIsStopConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="text-destructive">Stop Training</AlertDialogTitle>
            <AlertDialogDescription>
              This will stop the current training session. Any progress made in the current epoch will be lost.
              Are you sure you want to stop training?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleStopTraining} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">Stop Training</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}