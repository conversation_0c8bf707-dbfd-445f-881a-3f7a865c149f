# Frontend Components Update Plan

## Remaining Components to Update for New Hierarchical Structure

### 1. Review Submissions Page
**File**: `app/dashboard/review/page.tsx`
**Changes Needed**:
- Update imports to use `listUserAudio` from `audio-api-v2`
- Update data structure to use hierarchical format
- Update status handling to use `review.status.action`
- Update transcription access to use `transcriptions.primary.content`

### 2. Managed Recordings (Admin)
**File**: `app/dashboard/admin/recordings/page.tsx` or similar
**Changes Needed**:
- Update to use new admin API endpoints
- Update data structure for hierarchical audio
- Update review status management
- Update bulk operations for new structure

### 3. User Management (Admin)
**File**: `app/dashboard/admin/users/page.tsx` or similar
**Changes Needed**:
- Update to use `getUserWithSubcollections` from `audio-api-v2`
- Update user statistics to use hierarchical structure
- Update user profile management
- Update user preferences handling

### 4. My Profile Page
**File**: `app/dashboard/profile/page.tsx`
**Changes Needed**:
- Update to use `getUserWithSubcollections`
- Update profile editing to use new structure
- Update statistics display
- Update preferences management

### 5. Audio Recorder Component
**File**: `components/audio-recorder.tsx`
**Changes Needed**:
- Update upload success callback to use new structure
- Update API calls to use new endpoints

## Backend API Endpoints Still Needed

### 1. Bulk Upload V2
**Endpoint**: `/api/audio/bulk-upload-v2`
**Purpose**: Handle bulk uploads with hierarchical structure

### 2. Admin Review API
**Endpoint**: `/api/admin/recordings`
**Purpose**: Admin management of recordings with hierarchical data

### 3. Admin Users API
**Endpoint**: `/api/admin/users`
**Purpose**: Admin user management with hierarchical user data

### 4. Profile Management API
**Endpoint**: `/api/users/{id}/profile`
**Purpose**: User profile management with hierarchical structure

## Update Strategy

1. **Create missing backend endpoints first**
2. **Update each frontend component systematically**
3. **Test each component after update**
4. **Ensure all data flows use new hierarchical structure**
5. **Remove old API dependencies**

## Key Changes Pattern

For each component:
1. Replace old imports with new API library
2. Update data types to hierarchical format
3. Update data access patterns:
   - `recording.action` → `recording.review?.status?.action`
   - `recording.transcript.content` → `recording.transcriptions?.primary?.content`
   - `user.contribution_count` → `user.statistics?.summary?.contribution_count`
4. Update API calls to use new endpoints
5. Update error handling for new response format

## Testing Checklist

After each update:
- [ ] Component loads without errors
- [ ] Data displays correctly
- [ ] CRUD operations work
- [ ] Error handling works
- [ ] Loading states work
- [ ] Pagination works (if applicable)
- [ ] Filtering works (if applicable)
- [ ] Real-time updates work (if applicable)
