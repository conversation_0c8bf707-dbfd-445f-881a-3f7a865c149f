'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/hooks/use-toast"
import { TimestampMigration, MigrationProgress } from "@/lib/migration-utils"
import { 
  Database, 
  Play, 
  Eye, 
  CheckCircle, 
  AlertTriangle, 
  Clock,
  FileText
} from 'lucide-react'

export default function MigrateTimestampsPage() {
  const [isRunning, setIsRunning] = useState(false)
  const [progress, setProgress] = useState<MigrationProgress | null>(null)
  const [batchSize, setBatchSize] = useState(50)
  const [verbose, setVerbose] = useState(false)
  const [lastResult, setLastResult] = useState<any>(null)
  const { toast } = useToast()

  const runDryRun = async () => {
    setIsRunning(true)
    setProgress(null)
    setLastResult(null)

    try {
      const migration = new TimestampMigration({
        dryRun: true,
        batchSize,
        verbose
      })

      const result = await migration.dryRun()
      setProgress(result)
      setLastResult(result)

      toast({
        title: "Dry Run Completed",
        description: `Found ${result.totalDocuments} documents, ${result.updatedDocuments} would be updated`,
      })

    } catch (error) {
      console.error('Dry run failed:', error)
      toast({
        title: "Dry Run Failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      })
    } finally {
      setIsRunning(false)
    }
  }

  const runMigration = async () => {
    setIsRunning(true)
    setProgress(null)
    setLastResult(null)

    try {
      const migration = new TimestampMigration({
        dryRun: false,
        batchSize,
        verbose
      })

      const result = await migration.migrate()
      setProgress(result)
      setLastResult(result)

      toast({
        title: "Migration Completed",
        description: `Successfully updated ${result.updatedDocuments} documents`,
      })

    } catch (error) {
      console.error('Migration failed:', error)
      toast({
        title: "Migration Failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      })
    } finally {
      setIsRunning(false)
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-2">
        <Database className="h-8 w-8" />
        <div>
          <h1 className="text-3xl font-bold">Timestamp Migration</h1>
          <p className="text-muted-foreground">
            Update all Firestore recordings to use ISO 8601 timestamp format
          </p>
        </div>
      </div>

      {/* Warning Alert */}
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <strong>Important:</strong> This migration will update timestamp formats across all audio recordings. 
          Always run a dry run first to see what changes will be made.
        </AlertDescription>
      </Alert>

      {/* Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Migration Configuration</CardTitle>
          <CardDescription>
            Configure the migration settings before running
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="batchSize">Batch Size</Label>
              <Input
                id="batchSize"
                type="number"
                value={batchSize}
                onChange={(e) => setBatchSize(parseInt(e.target.value) || 50)}
                min={1}
                max={500}
                disabled={isRunning}
              />
              <p className="text-xs text-muted-foreground mt-1">
                Number of documents to process per batch (1-500)
              </p>
            </div>
            
            <div className="flex items-center space-x-2 pt-6">
              <Checkbox
                id="verbose"
                checked={verbose}
                onCheckedChange={(checked) => setVerbose(checked as boolean)}
                disabled={isRunning}
              />
              <Label htmlFor="verbose">Verbose logging</Label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Migration Actions</CardTitle>
          <CardDescription>
            Run a dry run first to preview changes, then execute the migration
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex space-x-4">
            <Button
              onClick={runDryRun}
              disabled={isRunning}
              variant="outline"
              className="flex-1"
            >
              <Eye className="h-4 w-4 mr-2" />
              {isRunning ? 'Running...' : 'Dry Run'}
            </Button>
            
            <Button
              onClick={runMigration}
              disabled={isRunning || !lastResult}
              className="flex-1"
            >
              <Play className="h-4 w-4 mr-2" />
              {isRunning ? 'Migrating...' : 'Run Migration'}
            </Button>
          </div>
          
          {!lastResult && (
            <p className="text-sm text-muted-foreground">
              Run a dry run first to see what changes will be made
            </p>
          )}
        </CardContent>
      </Card>

      {/* Progress */}
      {isRunning && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="h-5 w-5" />
              <span>Migration in Progress</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Progress value={75} className="w-full" />
              <p className="text-sm text-muted-foreground">
                Processing documents... This may take a few minutes.
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results */}
      {lastResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span>Migration Results</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{lastResult.totalDocuments}</div>
                <div className="text-sm text-muted-foreground">Total Documents</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{lastResult.updatedDocuments}</div>
                <div className="text-sm text-muted-foreground">Updated</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{lastResult.errors?.length || 0}</div>
                <div className="text-sm text-muted-foreground">Errors</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {Math.round(((lastResult.updatedDocuments || 0) / (lastResult.totalDocuments || 1)) * 100)}%
                </div>
                <div className="text-sm text-muted-foreground">Success Rate</div>
              </div>
            </div>

            {lastResult.errors && lastResult.errors.length > 0 && (
              <div className="mt-4">
                <h4 className="font-medium text-red-600 mb-2">Errors:</h4>
                <div className="bg-red-50 p-3 rounded text-sm">
                  {lastResult.errors.map((error: string, index: number) => (
                    <div key={index} className="text-red-700">
                      {index + 1}. {error}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>What This Migration Does</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <p>• Updates all <code>created_at</code> fields to ISO 8601 format</p>
            <p>• Updates all <code>updated_at</code> fields to ISO 8601 format</p>
            <p>• Updates all <code>reviewed_at</code> fields to ISO 8601 format</p>
            <p>• Processes main documents and all subcollections</p>
            <p>• Converts Firestore timestamps to ISO strings</p>
            <p>• Handles missing timestamps by setting current time</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
