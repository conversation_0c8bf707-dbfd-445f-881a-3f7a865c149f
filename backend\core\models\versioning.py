"""
Model Versioning System

Handles version control and history for AI models
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

from backend.config.settings import settings
from .types import ModelMetadata, ModelType

logger = logging.getLogger(__name__)


class ModelVersioning:
    """Model version control system"""
    
    def __init__(self):
        self.versions_path = Path(settings.FINAL_MODEL_DIR) / "versions"
        self.versions_path.mkdir(parents=True, exist_ok=True)
    
    def get_version_file(self, model_type: ModelType, model_name: str) -> Path:
        """Get version history file path"""
        return self.versions_path / f"{model_type.value}_{model_name}_versions.json"
    
    async def register_version(
        self, 
        model_id: str, 
        version: str, 
        metadata: ModelMetadata
    ) -> bool:
        """Register a new model version"""
        try:
            version_file = self.get_version_file(metadata.type, metadata.name)
            
            # Load existing version history
            version_history = {}
            if version_file.exists():
                with open(version_file, 'r') as f:
                    version_history = json.load(f)
            
            # Add new version
            version_entry = {
                "model_id": model_id,
                "version": version,
                "created_at": metadata.created_at.isoformat(),
                "status": metadata.status.value,
                "size_bytes": metadata.size_bytes,
                "tags": metadata.tags,
                "metrics": metadata.metrics,
                "training_config": metadata.training_config,
                "description": metadata.description
            }
            
            if "versions" not in version_history:
                version_history["versions"] = []
            
            version_history["versions"].append(version_entry)
            
            # Sort versions by creation date (newest first)
            version_history["versions"].sort(
                key=lambda x: x["created_at"], 
                reverse=True
            )
            
            # Update metadata
            version_history["model_name"] = metadata.name
            version_history["model_type"] = metadata.type.value
            version_history["latest_version"] = version
            version_history["total_versions"] = len(version_history["versions"])
            version_history["updated_at"] = datetime.now().isoformat()
            
            # Save version history
            with open(version_file, 'w') as f:
                json.dump(version_history, f, indent=2, default=str)
            
            logger.info(f"Version {version} registered for model {metadata.name}")
            return True
            
        except Exception as e:
            logger.error(f"Error registering version: {e}")
            raise
    
    async def get_version_history(
        self, 
        model_type: ModelType, 
        model_name: str
    ) -> Optional[Dict[str, Any]]:
        """Get version history for a model"""
        try:
            version_file = self.get_version_file(model_type, model_name)
            
            if not version_file.exists():
                return None
            
            with open(version_file, 'r') as f:
                return json.load(f)
                
        except Exception as e:
            logger.error(f"Error getting version history: {e}")
            return None
    
    async def get_latest_version(
        self, 
        model_type: ModelType, 
        model_name: str
    ) -> Optional[str]:
        """Get the latest version of a model"""
        try:
            history = await self.get_version_history(model_type, model_name)
            return history.get("latest_version") if history else None
            
        except Exception as e:
            logger.error(f"Error getting latest version: {e}")
            return None
    
    async def get_version_info(
        self, 
        model_type: ModelType, 
        model_name: str, 
        version: str
    ) -> Optional[Dict[str, Any]]:
        """Get information about a specific version"""
        try:
            history = await self.get_version_history(model_type, model_name)
            if not history:
                return None
            
            for version_entry in history.get("versions", []):
                if version_entry["version"] == version:
                    return version_entry
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting version info: {e}")
            return None
    
    async def list_versions(
        self, 
        model_type: ModelType, 
        model_name: str,
        limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """List all versions of a model"""
        try:
            history = await self.get_version_history(model_type, model_name)
            if not history:
                return []
            
            versions = history.get("versions", [])
            
            if limit:
                versions = versions[:limit]
            
            return versions
            
        except Exception as e:
            logger.error(f"Error listing versions: {e}")
            return []
    
    async def compare_versions(
        self, 
        model_type: ModelType, 
        model_name: str,
        version1: str, 
        version2: str
    ) -> Optional[Dict[str, Any]]:
        """Compare two versions of the same model"""
        try:
            v1_info = await self.get_version_info(model_type, model_name, version1)
            v2_info = await self.get_version_info(model_type, model_name, version2)
            
            if not v1_info or not v2_info:
                return None
            
            comparison = {
                "version1": v1_info,
                "version2": v2_info,
                "differences": {}
            }
            
            # Compare metrics
            if v1_info.get("metrics") and v2_info.get("metrics"):
                metrics_diff = {}
                for metric in v1_info["metrics"]:
                    if metric in v2_info["metrics"]:
                        diff = v2_info["metrics"][metric] - v1_info["metrics"][metric]
                        metrics_diff[metric] = {
                            "v1": v1_info["metrics"][metric],
                            "v2": v2_info["metrics"][metric],
                            "difference": diff,
                            "improvement": diff > 0 if metric in ['accuracy', 'confidence'] else diff < 0
                        }
                comparison["differences"]["metrics"] = metrics_diff
            
            # Compare size
            if v1_info.get("size_bytes") and v2_info.get("size_bytes"):
                size_diff = v2_info["size_bytes"] - v1_info["size_bytes"]
                comparison["differences"]["size"] = {
                    "v1_mb": v1_info["size_bytes"] / (1024 * 1024),
                    "v2_mb": v2_info["size_bytes"] / (1024 * 1024),
                    "difference_mb": size_diff / (1024 * 1024)
                }
            
            # Compare creation dates
            v1_date = datetime.fromisoformat(v1_info["created_at"])
            v2_date = datetime.fromisoformat(v2_info["created_at"])
            time_diff = (v2_date - v1_date).total_seconds()
            
            comparison["differences"]["time"] = {
                "v1_date": v1_info["created_at"],
                "v2_date": v2_info["created_at"],
                "days_apart": time_diff / (24 * 3600)
            }
            
            return comparison
            
        except Exception as e:
            logger.error(f"Error comparing versions: {e}")
            return None
    
    async def rollback_to_version(
        self, 
        model_type: ModelType, 
        model_name: str,
        target_version: str
    ) -> Optional[str]:
        """Mark a specific version as the current/active version"""
        try:
            version_info = await self.get_version_info(model_type, model_name, target_version)
            if not version_info:
                return None
            
            # Update version history to mark this as latest
            history = await self.get_version_history(model_type, model_name)
            if not history:
                return None
            
            history["latest_version"] = target_version
            history["rollback_at"] = datetime.now().isoformat()
            history["updated_at"] = datetime.now().isoformat()
            
            # Save updated history
            version_file = self.get_version_file(model_type, model_name)
            with open(version_file, 'w') as f:
                json.dump(history, f, indent=2, default=str)
            
            logger.info(f"Rolled back {model_name} to version {target_version}")
            return version_info["model_id"]
            
        except Exception as e:
            logger.error(f"Error rolling back to version: {e}")
            return None
    
    async def delete_version(
        self, 
        model_type: ModelType, 
        model_name: str,
        version: str
    ) -> bool:
        """Remove a version from history (does not delete model files)"""
        try:
            history = await self.get_version_history(model_type, model_name)
            if not history:
                return False
            
            # Remove version from history
            versions = history.get("versions", [])
            updated_versions = [v for v in versions if v["version"] != version]
            
            if len(updated_versions) == len(versions):
                # Version not found
                return False
            
            history["versions"] = updated_versions
            history["total_versions"] = len(updated_versions)
            history["updated_at"] = datetime.now().isoformat()
            
            # Update latest version if necessary
            if history.get("latest_version") == version and updated_versions:
                history["latest_version"] = updated_versions[0]["version"]
            
            # Save updated history
            version_file = self.get_version_file(model_type, model_name)
            with open(version_file, 'w') as f:
                json.dump(history, f, indent=2, default=str)
            
            logger.info(f"Version {version} removed from {model_name} history")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting version: {e}")
            return False
