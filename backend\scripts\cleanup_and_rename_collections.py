#!/usr/bin/env python3
"""
Cleanup and Rename Collections

This script:
1. Creates a final backup of old collections
2. Deletes redundant old collections
3. Renames new collections to clean names (audio_new → audio, users_new → users)
"""

import sys
import os
from datetime import datetime
from typing import List

# Add backend to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.firebase_clean import clean_firebase_service
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CollectionCleanup:
    def __init__(self):
        self.db = None
        self.cleanup_log = []
        
    def initialize(self):
        """Initialize Firebase connection"""
        try:
            clean_firebase_service.initialize()
            self.db = clean_firebase_service.db
            logger.info("✅ Firebase initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Firebase: {e}")
            raise
    
    def log_action(self, action: str, details: str):
        """Log cleanup actions"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "action": action,
            "details": details
        }
        self.cleanup_log.append(log_entry)
        logger.info(f"📝 {action}: {details}")
    
    def create_final_backup(self):
        """Create final backup before cleanup"""
        logger.info("💾 Creating final backup before cleanup...")
        
        backup_data = {
            'backup_created_at': datetime.now().isoformat(),
            'backup_type': 'final_cleanup_backup',
            'collections_to_delete': [
                'audio', 'transcription', 'users', 'system_logs', 
                'system_settings', '_migration_logs', '_migration_backup', 
                '_improvement_backup', 'content'
            ],
            'collections_to_rename': {
                'audio_new': 'audio',
                'users_new': 'users'
            },
            'note': 'Final backup before cleaning up redundant collections and renaming to clean names'
        }
        
        self.db.collection('_final_backup').document('cleanup_backup').set(backup_data)
        self.log_action("FINAL_BACKUP", "Created final backup record")
    
    def count_documents_in_collection(self, collection_name: str) -> int:
        """Count documents in a collection"""
        try:
            docs = self.db.collection(collection_name).get()
            return len(docs)
        except Exception as e:
            logger.warning(f"Could not count documents in {collection_name}: {e}")
            return 0
    
    def delete_old_collections(self):
        """Delete old redundant collections"""
        logger.info("🗑️ Deleting old redundant collections...")
        
        collections_to_delete = [
            'audio',           # Migrated to audio_new
            'transcription',   # Migrated to audio_new/{id}/transcriptions/
            'users',           # Migrated to users_new
            'system_logs',     # Migrated to logs/system/
            'system_settings', # Migrated to system/config/
            '_migration_logs',
            '_migration_backup',
            '_improvement_backup',
            'content'          # Not actively used
        ]
        
        for collection_name in collections_to_delete:
            try:
                # Count documents first
                doc_count = self.count_documents_in_collection(collection_name)
                
                if doc_count > 0:
                    print(f"⚠️ Collection '{collection_name}' has {doc_count} documents")
                    confirm = input(f"Delete '{collection_name}' with {doc_count} documents? (y/N): ").lower().strip()
                    
                    if confirm == 'y':
                        # Delete all documents in the collection
                        docs = self.db.collection(collection_name).get()
                        for doc in docs:
                            doc.reference.delete()
                        
                        self.log_action("DELETE_COLLECTION", f"Deleted {collection_name} ({doc_count} documents)")
                        print(f"✅ Deleted collection '{collection_name}'")
                    else:
                        self.log_action("SKIP_DELETE", f"Skipped deletion of {collection_name}")
                        print(f"⏭️ Skipped deletion of '{collection_name}'")
                else:
                    self.log_action("SKIP_DELETE", f"Collection {collection_name} is empty or doesn't exist")
                    print(f"ℹ️ Collection '{collection_name}' is empty or doesn't exist")
                    
            except Exception as e:
                logger.error(f"❌ Error deleting collection {collection_name}: {e}")
    
    def rename_collections_to_clean_names(self):
        """Rename new collections to clean names"""
        logger.info("🔄 Renaming collections to clean names...")
        
        renames = {
            'audio_new': 'audio',
            'users_new': 'users'
        }
        
        for old_name, new_name in renames.items():
            try:
                # Check if old collection exists
                old_docs = self.db.collection(old_name).get()
                if not old_docs:
                    print(f"⚠️ Collection '{old_name}' doesn't exist or is empty")
                    continue
                
                print(f"🔄 Renaming '{old_name}' → '{new_name}' ({len(old_docs)} documents)")
                
                # Copy all documents to new collection name
                for doc in old_docs:
                    doc_id = doc.id
                    data = doc.to_dict()
                    
                    # Create document in new collection
                    self.db.collection(new_name).document(doc_id).set(data)
                    
                    # Copy subcollections if they exist
                    self.copy_subcollections(old_name, doc_id, new_name, doc_id)
                
                # Verify the copy worked
                new_docs = self.db.collection(new_name).get()
                if len(new_docs) == len(old_docs):
                    print(f"✅ Successfully copied {len(new_docs)} documents")
                    
                    # Delete old collection
                    confirm = input(f"Delete old collection '{old_name}'? (y/N): ").lower().strip()
                    if confirm == 'y':
                        for doc in old_docs:
                            doc.reference.delete()
                        
                        self.log_action("RENAME_COLLECTION", f"Renamed {old_name} → {new_name} ({len(old_docs)} documents)")
                        print(f"✅ Renamed '{old_name}' → '{new_name}'")
                    else:
                        self.log_action("COPY_COLLECTION", f"Copied {old_name} → {new_name} (original kept)")
                        print(f"📋 Copied '{old_name}' → '{new_name}' (original kept)")
                else:
                    print(f"❌ Copy verification failed: {len(old_docs)} → {len(new_docs)}")
                    
            except Exception as e:
                logger.error(f"❌ Error renaming {old_name} → {new_name}: {e}")
    
    def copy_subcollections(self, old_collection: str, old_doc_id: str, new_collection: str, new_doc_id: str):
        """Copy subcollections from old document to new document"""
        try:
            old_doc_ref = self.db.collection(old_collection).document(old_doc_id)
            new_doc_ref = self.db.collection(new_collection).document(new_doc_id)
            
            # Get all subcollections (this is a simplified approach)
            # In practice, we know the subcollection names from our structure
            subcollections = ['metadata', 'transcriptions', 'review', 'training', 'analytics', 
                            'profile', 'statistics', 'preferences', 'security', 'contributions']
            
            for subcol_name in subcollections:
                try:
                    subcol_docs = old_doc_ref.collection(subcol_name).get()
                    if subcol_docs:
                        for subdoc in subcol_docs:
                            subdoc_data = subdoc.to_dict()
                            new_doc_ref.collection(subcol_name).document(subdoc.id).set(subdoc_data)
                except:
                    # Subcollection doesn't exist, skip
                    pass
                    
        except Exception as e:
            logger.warning(f"Error copying subcollections for {old_doc_id}: {e}")
    
    def update_code_references(self):
        """Update code to use clean collection names"""
        logger.info("📝 Updating code references...")
        
        # Update firebase_clean.py
        try:
            firebase_clean_path = "services/firebase_clean.py"
            with open(firebase_clean_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Replace collection references
            content = content.replace("'audio_new'", "'audio'")
            content = content.replace("'users_new'", "'users'")
            
            with open(firebase_clean_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.log_action("UPDATE_CODE", "Updated firebase_clean.py collection references")
            print("✅ Updated firebase_clean.py")
            
        except Exception as e:
            logger.error(f"❌ Error updating firebase_clean.py: {e}")
        
        # Update API routes
        api_files = ["api/routes/audio_v2.py", "api/routes/users_v2.py"]
        
        for api_file in api_files:
            try:
                with open(api_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Replace collection references
                content = content.replace("'audio_new'", "'audio'")
                content = content.replace("'users_new'", "'users'")
                
                with open(api_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.log_action("UPDATE_CODE", f"Updated {api_file} collection references")
                print(f"✅ Updated {api_file}")
                
            except Exception as e:
                logger.error(f"❌ Error updating {api_file}: {e}")
    
    def save_cleanup_log(self):
        """Save cleanup log"""
        try:
            log_doc = {
                'cleanup_completed_at': datetime.now().isoformat(),
                'total_actions': len(self.cleanup_log),
                'actions': self.cleanup_log
            }
            
            clean_firebase_service.add_system_log('system', {
                'level': 'INFO',
                'message': 'Collection cleanup and rename completed',
                'source': 'collection_cleanup',
                'details': log_doc
            })
            
            logger.info(f"✅ Cleanup log saved with {len(self.cleanup_log)} actions")
            
        except Exception as e:
            logger.error(f"❌ Error saving cleanup log: {e}")
    
    def run_cleanup(self, create_backup: bool = True):
        """Run the complete cleanup process"""
        try:
            self.initialize()
            
            print("🧹 Collection Cleanup and Rename Process")
            print("=" * 50)
            print("This will:")
            print("1. Create final backup")
            print("2. Delete old redundant collections")
            print("3. Rename audio_new → audio, users_new → users")
            print("4. Update code references")
            print()
            
            if create_backup:
                self.create_final_backup()
            
            # Delete old collections
            self.delete_old_collections()
            
            # Rename to clean names
            self.rename_collections_to_clean_names()
            
            # Update code references
            self.update_code_references()
            
            # Save log
            self.save_cleanup_log()
            
            logger.info("✅ Collection cleanup completed successfully!")
            logger.info(f"📊 Total actions performed: {len(self.cleanup_log)}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Collection cleanup failed: {e}")
            return False

def main():
    """Main cleanup function"""
    cleanup = CollectionCleanup()
    
    print("🧹 Collection Cleanup and Rename")
    print("=" * 40)
    print("This will clean up old collections and rename to clean names.")
    print()
    
    confirm = input("Do you want to proceed? (y/N): ").lower().strip()
    
    if confirm == 'y':
        success = cleanup.run_cleanup()
        if success:
            print("\n✅ Collection cleanup completed successfully!")
            print("📊 Final structure:")
            print("   - audio/ (clean hierarchical structure)")
            print("   - users/ (clean hierarchical structure)")
            print("   - Old collections removed")
        else:
            print("\n❌ Collection cleanup failed. Check logs for details.")
    else:
        print("Collection cleanup cancelled.")

if __name__ == "__main__":
    main()
