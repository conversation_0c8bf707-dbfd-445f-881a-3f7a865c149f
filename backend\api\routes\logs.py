"""
Logs API endpoints for backend log management
"""

import os
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta, timezone
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
import json
import glob
from pathlib import Path
from backend.services.firebase import firebase_service
from backend.services.firebase_clean import clean_firebase_service

router = APIRouter()
logger = logging.getLogger(__name__)

def normalize_timestamp(timestamp_value):
    """Normalize timestamp to timezone-aware datetime"""
    try:
        if isinstance(timestamp_value, str):
            # Handle ISO format strings
            if 'T' in timestamp_value:
                if timestamp_value.endswith('Z'):
                    timestamp_value = timestamp_value.replace('Z', '+00:00')
                elif '+' not in timestamp_value and 'Z' not in timestamp_value:
                    timestamp_value += '+00:00'
                return datetime.fromisoformat(timestamp_value)
            else:
                # Try to parse as datetime string and make timezone aware
                dt = datetime.fromisoformat(timestamp_value)
                if dt.tzinfo is None:
                    dt = dt.replace(tzinfo=timezone.utc)
                return dt
        elif hasattr(timestamp_value, 'timestamp'):
            # Firestore timestamp object
            return datetime.fromtimestamp(timestamp_value.timestamp(), tz=timezone.utc)
        elif isinstance(timestamp_value, datetime):
            # Already a datetime object
            if timestamp_value.tzinfo is None:
                return timestamp_value.replace(tzinfo=timezone.utc)
            return timestamp_value
        else:
            # Try to convert to string first
            return normalize_timestamp(str(timestamp_value))
    except Exception as e:
        logger.warning(f"Failed to normalize timestamp {timestamp_value}: {e}")
        # Return current time as fallback
        return datetime.now(timezone.utc)

class LogEntry(BaseModel):
    id: str
    timestamp: str
    level: str
    message: str
    source: str
    details: Optional[Dict[str, Any]] = None

class LogsResponse(BaseModel):
    logs: List[LogEntry]
    total: int
    page: int
    per_page: int
    has_more: bool

async def get_firestore_logs(hours: int) -> List[LogEntry]:
    """Get real logs from Firestore collections"""
    try:
        firebase_service.initialize()
        logs = []

        # Get system logs from unified structure
        system_logs_collection = firebase_service.db.collection('system').document('logs').collection('events')
        system_logs_docs = system_logs_collection.order_by('timestamp', direction='DESCENDING').limit(100).stream()

        for doc in system_logs_docs:
            data = doc.to_dict()
            if data.get('timestamp') and data.get('message'):
                logs.append(LogEntry(
                    id=data.get('id', doc.id),
                    timestamp=data['timestamp'],
                    level=data.get('level', 'INFO'),
                    message=data['message'],
                    source=data.get('source', 'SYSTEM'),
                    details=data.get('details', {})
                ))

        # Get training status logs (legacy - TODO: Update to use clean structure)
        training_collection = firebase_service.db.collection('training_status')
        training_docs = training_collection.order_by('updated_at', direction='DESCENDING').limit(20).stream()

        for doc in training_docs:
            data = doc.to_dict()
            if data.get('updated_at'):
                # Normalize timestamp
                timestamp = data['updated_at']
                if hasattr(timestamp, 'isoformat'):
                    timestamp = timestamp.isoformat()
                elif hasattr(timestamp, 'timestamp'):
                    timestamp = datetime.fromtimestamp(timestamp.timestamp(), tz=timezone.utc).isoformat()
                elif isinstance(timestamp, str):
                    # Already a string, keep as is
                    pass
                else:
                    timestamp = str(timestamp)

                logs.append(LogEntry(
                    id=f"training-{doc.id}-{int(datetime.now().timestamp())}",
                    timestamp=timestamp,
                    level='ERROR' if data.get('status') == 'error' else
                          'INFO' if data.get('status') in ['training', 'completed'] else 'DEBUG',
                    message=f"{doc.id.upper()} training {data.get('status', 'unknown')}" +
                           (f": {data.get('message', '')}" if data.get('message') else ""),
                    source=f"{doc.id.upper()}_TRAINER",
                    details={
                        'progress': data.get('progress', 0),
                        'current_epoch': data.get('current_epoch', 0),
                        'total_epochs': data.get('total_epochs', 0),
                        'accuracy': data.get('current_accuracy', 0)
                    }
                ))

        # Get validation results logs (TODO: Update to use clean structure)
        validation_collection = firebase_service.db.collection('validation_results')
        validation_docs = validation_collection.order_by('completed_at', direction='DESCENDING').limit(20).stream()

        for doc in validation_docs:
            data = doc.to_dict()
            if data.get('completed_at'):
                # Normalize timestamp
                timestamp = data['completed_at']
                if hasattr(timestamp, 'isoformat'):
                    timestamp = timestamp.isoformat()
                elif hasattr(timestamp, 'timestamp'):
                    timestamp = datetime.fromtimestamp(timestamp.timestamp(), tz=timezone.utc).isoformat()
                elif isinstance(timestamp, str):
                    # Already a string, keep as is
                    pass
                else:
                    timestamp = str(timestamp)

                logs.append(LogEntry(
                    id=f"validation-{doc.id}",
                    timestamp=timestamp,
                    level='INFO' if data.get('status') == 'completed' else 'ERROR',
                    message=f"Model validation {data.get('status', 'unknown')} - " +
                           f"Accuracy: {(data.get('metrics', {}).get('accuracy', 0) * 100):.1f}%",
                    source='VALIDATION',
                    details=data.get('metrics', {})
                ))

        # Get recent audio uploads
        audio_collection = firebase_service.db.collection('audio')
        audio_docs = audio_collection.order_by('created_at', direction='DESCENDING').limit(30).stream()

        for doc in audio_docs:
            data = doc.to_dict()
            if data.get('created_at') and data.get('title') and data.get('action'):
                # Normalize timestamp
                timestamp = data['created_at']
                if hasattr(timestamp, 'isoformat'):
                    timestamp = timestamp.isoformat()
                elif hasattr(timestamp, 'timestamp'):
                    timestamp = datetime.fromtimestamp(timestamp.timestamp(), tz=timezone.utc).isoformat()
                elif isinstance(timestamp, str):
                    # Already a string, keep as is
                    pass
                else:
                    timestamp = str(timestamp)

                logs.append(LogEntry(
                    id=f"audio-{doc.id}",
                    timestamp=timestamp,
                    level='INFO' if data.get('action') == 'approved' else
                          'WARNING' if data.get('action') == 'rejected' else 'DEBUG',
                    message=f"Audio \"{data.get('title', 'Unknown')}\" {data.get('action', 'processed')} - " +
                           f"Duration: {data.get('duration', 0)}s",
                    source='AUDIO_MANAGER',
                    details={
                        'duration': data.get('duration'),
                        'format': data.get('format'),
                        'gender': data.get('gender'),
                        'trained_asr': data.get('trained_asr'),
                        'tts_trained': data.get('tts_trained')
                    }
                ))

        # Sort by timestamp (newest first)
        def safe_timestamp_sort(log_entry):
            try:
                timestamp_str = log_entry.timestamp
                if isinstance(timestamp_str, str):
                    if timestamp_str.endswith('Z'):
                        timestamp_str = timestamp_str.replace('Z', '+00:00')
                    elif '+' not in timestamp_str and 'Z' not in timestamp_str and 'T' in timestamp_str:
                        timestamp_str += '+00:00'
                    return datetime.fromisoformat(timestamp_str)
                else:
                    return datetime.now(timezone.utc)
            except Exception:
                return datetime.now(timezone.utc)

        logs.sort(key=safe_timestamp_sort, reverse=True)

        # Filter by time range
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
        filtered_logs = []

        for log in logs:
            try:
                log_time = normalize_timestamp(log.timestamp)
                if log_time >= cutoff_time:
                    filtered_logs.append(log)
            except Exception as e:
                # Include logs with invalid timestamps but log the error
                logger.warning(f"Invalid timestamp format for log {log.id}: {log.timestamp} - {e}")
                filtered_logs.append(log)

        return filtered_logs

    except Exception as e:
        logger.error(f"Error fetching Firestore logs: {e}")
        return []

@router.get("/logs", response_model=LogsResponse)
async def get_logs(
    level: Optional[str] = Query(None, description="Filter by log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)"),
    source: Optional[str] = Query(None, description="Filter by log source"),
    search: Optional[str] = Query(None, description="Search in log messages"),
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(50, ge=1, le=1000, description="Items per page"),
    hours: int = Query(24, ge=1, le=168, description="Hours of logs to retrieve (max 7 days)")
):
    """Get backend logs with filtering and pagination"""
    try:
        # Get real logs from Firestore
        logs = await get_firestore_logs(hours)

        # Apply filters
        if level:
            logs = [log for log in logs if log.level == level.upper()]

        if source:
            logs = [log for log in logs if source.upper() in log.source.upper()]

        if search:
            logs = [log for log in logs if search.lower() in log.message.lower()]

        # Pagination
        total = len(logs)
        start_idx = (page - 1) * per_page
        end_idx = start_idx + per_page
        paginated_logs = logs[start_idx:end_idx]

        return LogsResponse(
            logs=paginated_logs,
            total=total,
            page=page,
            per_page=per_page,
            has_more=end_idx < total
        )

    except Exception as e:
        logger.error(f"Error retrieving logs: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve logs: {str(e)}")

@router.get("/logs/sources")
async def get_log_sources():
    """Get available log sources from Firestore data"""
    try:
        firebase_service.initialize()
        sources = set()

        # Get sources from training status
        training_collection = firebase_service.db.collection('training_status')
        training_docs = training_collection.limit(10).stream()

        for doc in training_docs:
            sources.add(f"{doc.id.upper()}_TRAINER")

        # Check if validation results exist
        validation_collection = firebase_service.db.collection('validation_results')
        validation_docs = list(validation_collection.limit(1).stream())
        if validation_docs:
            sources.add('VALIDATION')

        # Check if audio collection exists
        audio_collection = firebase_service.db.collection('audio')
        audio_docs = list(audio_collection.limit(1).stream())
        if audio_docs:
            sources.add('AUDIO_MANAGER')

        # Add common system sources
        sources.update(['FIREBASE', 'SYSTEM', 'API_SERVER'])

        return {"sources": sorted(list(sources))}

    except Exception as e:
        logger.error(f"Error getting log sources: {e}")
        # Fallback to basic sources
        return {"sources": ["ASR_TRAINER", "TTS_TRAINER", "VALIDATION", "AUDIO_MANAGER", "FIREBASE", "SYSTEM"]}

@router.get("/logs/levels")
async def get_log_levels():
    """Get available log levels"""
    try:
        levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        return {"levels": levels}
        
    except Exception as e:
        logger.error(f"Error getting log levels: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get log levels: {str(e)}")

@router.delete("/logs")
async def clear_logs():
    """Clear all logs from Firestore (admin only)"""
    try:
        firebase_service.initialize()

        # Get all documents from unified system logs structure
        system_logs_collection = firebase_service.db.collection('system').document('logs').collection('events')
        docs = system_logs_collection.stream()

        # Delete all documents
        deleted_count = 0
        for doc in docs:
            doc.reference.delete()
            deleted_count += 1

        logger.info(f"Cleared {deleted_count} logs from Firestore")
        return {
            "message": f"Successfully cleared {deleted_count} logs from Firestore",
            "timestamp": datetime.now().isoformat(),
            "deleted_count": deleted_count
        }

    except Exception as e:
        logger.error(f"Error clearing logs: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to clear logs: {str(e)}")

@router.get("/logs/settings")
async def get_log_settings():
    """Get current log level settings"""
    try:
        firebase_service.initialize()

        # Get log settings from unified system structure
        settings_doc = firebase_service.db.collection('system').document('settings').collection('config').document('logging').get()

        if settings_doc.exists:
            data = settings_doc.to_dict()
            return {
                "enabled_levels": data.get('enabled_levels', {
                    "DEBUG": True,
                    "INFO": True,
                    "WARNING": True,
                    "ERROR": True,
                    "CRITICAL": True
                }),
                "updated_at": data.get('updated_at'),
                "updated_by": data.get('updated_by')
            }
        else:
            # Return default settings if none exist
            return {
                "enabled_levels": {
                    "DEBUG": True,
                    "INFO": True,
                    "WARNING": True,
                    "ERROR": True,
                    "CRITICAL": True
                },
                "updated_at": None,
                "updated_by": None
            }

    except Exception as e:
        logger.error(f"Error getting log settings: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get log settings: {str(e)}")

# Mock data generation removed - now using real Firestore data

@router.get("/logs/stats")
async def get_log_stats():
    """Get log statistics from real Firestore data"""
    try:
        # Get real logs from Firestore
        logs = await get_firestore_logs(24)  # Last 24 hours

        stats = {
            "total_logs": len(logs),
            "by_level": {},
            "by_source": {},
            "last_24_hours": len(logs),  # All logs are from last 24 hours
            "errors_last_hour": 0
        }

        # Count by level and check for recent errors
        one_hour_ago = datetime.now(timezone.utc) - timedelta(hours=1)
        for log in logs:
            # Count by level
            stats["by_level"][log.level] = stats["by_level"].get(log.level, 0) + 1

            # Count by source
            stats["by_source"][log.source] = stats["by_source"].get(log.source, 0) + 1

            # Count recent errors
            try:
                log_time = normalize_timestamp(log.timestamp)
                if log.level in ["ERROR", "CRITICAL"] and log_time > one_hour_ago:
                    stats["errors_last_hour"] += 1
            except:
                pass  # Skip logs with invalid timestamps

        return stats

    except Exception as e:
        logger.error(f"Error getting log stats: {e}")
        # Return empty stats on error
        return {
            "total_logs": 0,
            "by_level": {},
            "by_source": {},
            "last_24_hours": 0,
            "errors_last_hour": 0
        }
