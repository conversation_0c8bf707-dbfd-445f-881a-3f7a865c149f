"""
Clean Firebase Service

Updated Firebase service that works with the new clean hierarchical collection structure:

IMPLEMENTED STRUCTURE:
- settings/{type}                    # Unified settings
- validation/{validation_id}/*       # Hierarchical validation data
- training/{session_id}/*           # Hierarchical training data
- audio/{audio_id}/*                # Hierarchical audio + transcriptions
- users/{user_id}/*                 # Hierarchical user data
- models/{model_id}                 # Model records
- system/config/settings/*          # System configuration
- logs/{type}/entries/*             # Organized logs
- monitoring/*                      # System monitoring

KEY FEATURES:
- Unified training status for audio + transcription
- Single query for complete audio data
- Hierarchical user management
- Clean collection names (no "_v2" suffixes)
"""

import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
from google.cloud import firestore
from google.cloud.firestore_v1.base_query import FieldFilter

logger = logging.getLogger(__name__)

class CleanFirebaseService:
    def __init__(self):
        self.db = None
        self.initialized = False
    
    def initialize(self):
        """Initialize Firestore client"""
        try:
            if not self.initialized:
                self.db = firestore.Client()
                self.initialized = True
                logger.info("✅ Clean Firebase service initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Firebase: {e}")
            raise
    
    # ==================== SETTINGS MANAGEMENT ====================
    
    def get_settings(self, setting_type: str) -> Dict[str, Any]:
        """
        Get settings by type
        
        Args:
            setting_type: 'asr_training', 'tts_training', 'asr_validation', 
                         'tts_validation', 'asr_schedule', 'tts_schedule', 'system'
        """
        try:
            doc_ref = self.db.collection('settings').document(setting_type)
            doc = doc_ref.get()
            
            if doc.exists:
                return doc.to_dict()
            else:
                # Return defaults based on type
                return self._get_default_settings(setting_type)
                
        except Exception as e:
            logger.error(f"Error getting {setting_type} settings: {e}")
            raise
    
    def update_settings(self, setting_type: str, settings: Dict[str, Any]):
        """Update settings"""
        try:
            doc_ref = self.db.collection('settings').document(setting_type)
            settings['updated_at'] = datetime.now().isoformat()
            doc_ref.set(settings, merge=True)
            
            logger.info(f"✅ Updated {setting_type} settings")
            
        except Exception as e:
            logger.error(f"Error updating {setting_type} settings: {e}")
            raise
    
    def _get_default_settings(self, setting_type: str) -> Dict[str, Any]:
        """Get default settings based on type"""
        defaults = {
            'asr_training': {
                'epochs': 5,
                'learning_rate': 1e-5,
                'batch_size': 4,
                'validation_split': 0.2,
                'model_name': 'small',
                'early_stopping_patience': 3,
                'use_augmentation': False,
                'eval_steps': 100
            },
            'tts_training': {
                'epochs': 10,
                'learning_rate': 1e-4,
                'batch_size': 8,
                'validation_split': 0.2
            },
            'asr_validation': {
                'validation_type': 'quick',
                'max_samples': 20,
                'min_confidence': 0.0
            },
            'tts_validation': {
                'validation_type': 'quick',
                'max_samples': 10
            },
            'asr_schedule': {
                'enabled': False,
                'interval': 'weekly',
                'time': '00:00'
            },
            'tts_schedule': {
                'enabled': False,
                'interval': 'weekly', 
                'time': '00:00'
            },
            'system': {
                'max_file_size_mb': 100,
                'supported_formats': ['wav', 'mp3', 'ogg'],
                'default_language': 'masalit'
            }
        }
        
        return defaults.get(setting_type, {})
    
    # ==================== VALIDATION MANAGEMENT ====================
    
    def create_validation_session(self, validation_id: str, config: Dict[str, Any]) -> str:
        """Create a new validation session"""
        try:
            validation_ref = self.db.collection('validation').document(validation_id)
            
            # Main validation document
            main_data = {
                'validation_id': validation_id,
                'model_id': config.get('model_id'),
                'model_type': config.get('model_type'),
                'status': 'initialized',
                'created_at': datetime.now().isoformat()
            }
            validation_ref.set(main_data)
            
            # Config subcollection
            validation_ref.collection('config').document('settings').set(config)
            
            logger.info(f"✅ Created validation session: {validation_id}")
            return validation_id
            
        except Exception as e:
            logger.error(f"Error creating validation session: {e}")
            raise
    
    def update_validation_progress(self, validation_id: str, progress_data: Dict[str, Any]):
        """Update validation progress"""
        try:
            progress_ref = (self.db.collection('validation')
                          .document(validation_id)
                          .collection('progress')
                          .document('current'))
            
            progress_data['updated_at'] = datetime.now().isoformat()
            progress_ref.set(progress_data, merge=True)
            
        except Exception as e:
            logger.error(f"Error updating validation progress: {e}")
            raise
    
    def save_validation_results(self, validation_id: str, results: Dict[str, Any]):
        """Save validation results"""
        try:
            # Update main document status
            validation_ref = self.db.collection('validation').document(validation_id)
            validation_ref.update({
                'status': results.get('status', 'completed'),
                'completed_at': datetime.now().isoformat(),
                'duration_seconds': results.get('duration_seconds')
            })
            
            # Save detailed results
            results_ref = validation_ref.collection('results').document('final')
            results_data = {
                'metrics': results.get('metrics', {}),
                'summary': results.get('summary', {}),
                'created_at': datetime.now().isoformat()
            }
            results_ref.set(results_data)
            
            # Save error analysis if present
            error_analysis = results.get('error_analysis', [])
            if error_analysis:
                for i, error in enumerate(error_analysis):
                    error_ref = validation_ref.collection('error_analysis').document(f'sample_{i}')
                    error_ref.set(error)
            
            logger.info(f"✅ Saved validation results: {validation_id}")
            
        except Exception as e:
            logger.error(f"Error saving validation results: {e}")
            raise
    
    def get_validation_history(self, model_id: Optional[str] = None, limit: int = 10) -> List[Dict[str, Any]]:
        """Get validation history"""
        try:
            query = self.db.collection('validation')
            
            if model_id:
                query = query.where('model_id', '==', model_id)
            
            query = query.order_by('created_at', direction=firestore.Query.DESCENDING).limit(limit)
            
            docs = query.stream()
            results = []
            
            for doc in docs:
                data = doc.to_dict()
                validation_id = doc.id
                
                # Get results if available
                try:
                    results_doc = (doc.reference.collection('results')
                                 .document('final').get())
                    if results_doc.exists:
                        data['results'] = results_doc.to_dict()
                except:
                    pass
                
                results.append(data)
            
            return results
            
        except Exception as e:
            logger.error(f"Error getting validation history: {e}")
            raise
    
    # ==================== TRAINING MANAGEMENT ====================
    
    def create_training_session(self, session_id: str, config: Dict[str, Any]) -> str:
        """Create a new training session"""
        try:
            training_ref = self.db.collection('training').document(session_id)
            
            # Main training document
            main_data = {
                'session_id': session_id,
                'model_type': config.get('model_type'),
                'status': 'initialized',
                'created_at': datetime.now().isoformat()
            }
            training_ref.set(main_data)
            
            # Config subcollection
            training_ref.collection('config').document('settings').set(config)
            
            logger.info(f"✅ Created training session: {session_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"Error creating training session: {e}")
            raise
    
    def update_training_progress(self, session_id: str, progress_data: Dict[str, Any]):
        """Update training progress"""
        try:
            progress_ref = (self.db.collection('training')
                          .document(session_id)
                          .collection('progress')
                          .document('current'))
            
            progress_data['updated_at'] = datetime.now().isoformat()
            progress_ref.set(progress_data, merge=True)
            
            # Also update main document status
            training_ref = self.db.collection('training').document(session_id)
            training_ref.update({
                'status': progress_data.get('status', 'training'),
                'updated_at': datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Error updating training progress: {e}")
            raise
    
    def save_training_results(self, session_id: str, results: Dict[str, Any]):
        """Save training results"""
        try:
            # Update main document
            training_ref = self.db.collection('training').document(session_id)
            training_ref.update({
                'status': results.get('status', 'completed'),
                'completed_at': datetime.now().isoformat(),
                'model_version': results.get('model_version')
            })
            
            # Save detailed results
            results_ref = training_ref.collection('results').document('final')
            results_ref.set({
                **results,
                'created_at': datetime.now().isoformat()
            })
            
            logger.info(f"✅ Saved training results: {session_id}")
            
        except Exception as e:
            logger.error(f"Error saving training results: {e}")
            raise
    
    def add_training_metric(self, session_id: str, metric_data: Dict[str, Any]):
        """Add a training metric entry"""
        try:
            metrics_ref = (self.db.collection('training')
                         .document(session_id)
                         .collection('metrics')
                         .document())  # Auto-generate ID
            
            metric_data['timestamp'] = datetime.now().isoformat()
            metrics_ref.set(metric_data)
            
        except Exception as e:
            logger.error(f"Error adding training metric: {e}")
            raise
    
    # ==================== SYSTEM MANAGEMENT ====================

    def get_system_config(self, config_type: str) -> Dict[str, Any]:
        """
        Get system configuration by type

        Args:
            config_type: 'logging', 'security', 'performance', 'features'
        """
        try:
            doc_ref = self.db.collection('system').document('config').collection('settings').document(config_type)
            doc = doc_ref.get()

            if doc.exists:
                return doc.to_dict()
            else:
                # Return defaults based on type
                return self._get_default_system_config(config_type)

        except Exception as e:
            logger.error(f"Error getting {config_type} system config: {e}")
            raise

    def update_system_config(self, config_type: str, config: Dict[str, Any]):
        """Update system configuration"""
        try:
            doc_ref = self.db.collection('system').document('config').collection('settings').document(config_type)
            config['updated_at'] = datetime.now().isoformat()
            doc_ref.set(config, merge=True)

            logger.info(f"✅ Updated {config_type} system config")

        except Exception as e:
            logger.error(f"Error updating {config_type} system config: {e}")
            raise

    def add_system_log(self, log_type: str, log_data: Dict[str, Any]):
        """Add a system log entry"""
        try:
            log_ref = (self.db.collection('logs')
                      .document(log_type)
                      .collection('entries')
                      .document())  # Auto-generate ID

            log_data['timestamp'] = datetime.now().isoformat()
            log_data['log_type'] = log_type
            log_ref.set(log_data)

            logger.info(f"✅ Added {log_type} log entry")

        except Exception as e:
            logger.error(f"Error adding {log_type} log: {e}")
            raise

    def get_system_logs(self, log_type: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get system logs by type"""
        try:
            query = (self.db.collection('logs')
                    .document(log_type)
                    .collection('entries')
                    .order_by('timestamp', direction=firestore.Query.DESCENDING)
                    .limit(limit))

            docs = query.stream()
            return [doc.to_dict() for doc in docs]

        except Exception as e:
            logger.error(f"Error getting {log_type} logs: {e}")
            raise

    def update_monitoring_data(self, monitor_type: str, data: Dict[str, Any]):
        """Update monitoring data"""
        try:
            monitor_ref = self.db.collection('monitoring').document(monitor_type)
            data['last_updated'] = datetime.now().isoformat()
            monitor_ref.set(data, merge=True)

            logger.info(f"✅ Updated {monitor_type} monitoring data")

        except Exception as e:
            logger.error(f"Error updating {monitor_type} monitoring: {e}")
            raise

    def _get_default_system_config(self, config_type: str) -> Dict[str, Any]:
        """Get default system configuration"""
        defaults = {
            'logging': {
                'level': 'INFO',
                'max_file_size_mb': 100,
                'retention_days': 30,
                'enabled_sources': ['system', 'training', 'validation', 'api']
            },
            'security': {
                'max_login_attempts': 5,
                'session_timeout_minutes': 60,
                'require_2fa': False,
                'allowed_origins': ['localhost:3000', 'buragatechnologies.com']
            },
            'performance': {
                'max_concurrent_trainings': 1,
                'max_concurrent_validations': 3,
                'cache_ttl_minutes': 15,
                'request_timeout_seconds': 30
            },
            'features': {
                'asr_training_enabled': True,
                'tts_training_enabled': True,
                'validation_enabled': True,
                'analytics_enabled': True,
                'real_time_updates': True
            }
        }

        return defaults.get(config_type, {})

    # ==================== AUDIO MANAGEMENT (V2) ====================

    def create_audio_record(self, audio_data: Dict[str, Any]) -> str:
        """Create hierarchical audio record"""
        try:
            audio_id = audio_data.get('id')
            if not audio_id:
                raise ValueError("audio_id is required")

            audio_ref = self.db.collection('audio').document(audio_id)

            # Main audio document (core metadata only)
            main_data = {
                'id': audio_id,
                'title': audio_data.get('title'),
                'audio_url': audio_data.get('audio_url'),
                'duration': audio_data.get('duration'),
                'format': audio_data.get('format'),
                'created_at': datetime.now().isoformat(),
                'user_id': audio_data.get('user_id'),
                'source': audio_data.get('source', 'direct_upload')
            }
            audio_ref.set(main_data)

            # Metadata subcollection
            if any(key in audio_data for key in ['gender', 'language', 'dialect']):
                metadata_data = {
                    'gender': audio_data.get('gender'),
                    'language': audio_data.get('language', 'masalit'),
                    'dialect': audio_data.get('dialect'),
                    'quality_rating': audio_data.get('quality_rating'),
                    'recording_context': audio_data.get('recording_context')
                }
                audio_ref.collection('metadata').document('details').set(metadata_data)

            # Review subcollection
            review_data = {
                'action': audio_data.get('action', 'pending'),
                'is_flagged': audio_data.get('is_flagged', False),
                'created_at': datetime.now().isoformat()
            }
            audio_ref.collection('review').document('status').set(review_data)

            # Training subcollection - Single source of truth for audio+transcription training
            training_data = {
                'trained_asr': False,
                'tts_trained': False,
                'training_sessions': [],
                'created_at': datetime.now().isoformat(),
                'note': 'Audio and transcription training status are unified - if audio is trained, transcription is also trained'
            }
            audio_ref.collection('training').document('status').set(training_data)

            logger.info(f"✅ Created hierarchical audio record: {audio_id}")
            return audio_id

        except Exception as e:
            logger.error(f"Error creating audio record: {e}")
            raise

    def add_transcription(self, audio_id: str, transcription_data: Dict[str, Any], transcription_type: str = 'primary'):
        """Add transcription to audio record"""
        try:
            audio_ref = self.db.collection('audio').document(audio_id)
            transcription_ref = audio_ref.collection('transcriptions').document(transcription_type)

            transcription_data['created_at'] = datetime.now().isoformat()
            transcription_ref.set(transcription_data)

            logger.info(f"✅ Added {transcription_type} transcription to audio: {audio_id}")

        except Exception as e:
            logger.error(f"Error adding transcription: {e}")
            raise

    def get_audio_with_subcollections(self, audio_id: str, include: List[str] = None) -> Dict[str, Any]:
        """Get audio data with specified subcollections"""
        try:
            if include is None:
                include = ['metadata', 'transcriptions', 'review', 'training']

            audio_ref = self.db.collection('audio').document(audio_id)
            audio_doc = audio_ref.get()

            if not audio_doc.exists:
                return None

            result = audio_doc.to_dict()

            # Get requested subcollections
            for subcollection in include:
                try:
                    subcol_docs = audio_ref.collection(subcollection).get()
                    result[subcollection] = {}
                    for doc in subcol_docs:
                        result[subcollection][doc.id] = doc.to_dict()
                except:
                    result[subcollection] = {}

            return result

        except Exception as e:
            logger.error(f"Error getting audio with subcollections: {e}")
            raise

    def update_audio_training_status(self, audio_id: str, training_updates: Dict[str, Any]):
        """
        Update audio training status - this automatically applies to transcription too
        since audio and transcription are trained together in ASR
        """
        try:
            training_ref = (self.db.collection('audio')
                           .document(audio_id)
                           .collection('training')
                           .document('status'))

            training_updates['updated_at'] = datetime.now().isoformat()

            # Add training session to history if provided
            if 'training_session_id' in training_updates:
                session_id = training_updates.pop('training_session_id')
                # Get current sessions and add new one
                current_doc = training_ref.get()
                current_sessions = []
                if current_doc.exists:
                    current_sessions = current_doc.to_dict().get('training_sessions', [])

                if session_id not in current_sessions:
                    current_sessions.append(session_id)
                    training_updates['training_sessions'] = current_sessions

            training_ref.update(training_updates)

            logger.info(f"✅ Updated audio training status (applies to transcription too): {audio_id}")

        except Exception as e:
            logger.error(f"Error updating audio training status: {e}")
            raise

    def get_training_status(self, audio_id: str) -> Dict[str, Any]:
        """Get unified training status for audio and transcription"""
        try:
            training_ref = (self.db.collection('audio')
                           .document(audio_id)
                           .collection('training')
                           .document('status'))

            training_doc = training_ref.get()
            if training_doc.exists:
                return training_doc.to_dict()
            else:
                return {
                    'trained_asr': False,
                    'tts_trained': False,
                    'training_sessions': [],
                    'note': 'No training history found'
                }

        except Exception as e:
            logger.error(f"Error getting training status: {e}")
            raise

    def mark_audio_as_trained(self, audio_id: str, training_session_id: str, model_type: str = 'asr'):
        """Mark audio (and its transcription) as trained"""
        try:
            training_updates = {
                'training_session_id': training_session_id,
                'last_trained_at': datetime.now().isoformat()
            }

            if model_type == 'asr':
                training_updates['trained_asr'] = True
            elif model_type == 'tts':
                training_updates['tts_trained'] = True

            self.update_audio_training_status(audio_id, training_updates)

            logger.info(f"✅ Marked audio as trained for {model_type}: {audio_id}")

        except Exception as e:
            logger.error(f"Error marking audio as trained: {e}")
            raise

    def get_training_data(self, model_type: str = 'asr', max_samples: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get approved audio and transcription data for training from hierarchical structure

        Args:
            model_type: 'asr' or 'tts'
            max_samples: Maximum number of samples to return
        """
        try:
            # Get approved audio files that haven't been trained yet for the specified model type
            audio_query = self.db.collection('audio')
            audio_docs = list(audio_query.stream())

            logger.info(f"Found {len(audio_docs)} total audio documents")

            training_data = []

            for audio_doc in audio_docs:
                try:
                    audio_data = audio_doc.to_dict()
                    audio_id = audio_doc.id

                    # Check review status
                    review_ref = audio_doc.reference.collection('review').document('status')
                    review_doc = review_ref.get()

                    if not review_doc.exists:
                        continue

                    review_data = review_doc.to_dict()
                    if review_data.get('action') != 'approved':
                        continue

                    # Check training status
                    training_ref = audio_doc.reference.collection('training').document('status')
                    training_doc = training_ref.get()

                    if training_doc.exists:
                        training_status = training_doc.to_dict()
                        # Skip if already trained for this model type
                        if model_type == 'asr' and training_status.get('trained_asr', False):
                            continue
                        elif model_type == 'tts' and training_status.get('tts_trained', False):
                            continue

                    # Get transcription
                    transcription_ref = audio_doc.reference.collection('transcriptions').document('primary')
                    transcription_doc = transcription_ref.get()

                    if not transcription_doc.exists:
                        logger.warning(f"No primary transcription found for audio {audio_id}")
                        continue

                    transcription_data = transcription_doc.to_dict()
                    transcription_content = transcription_data.get('content', '').strip()

                    if not transcription_content:
                        logger.warning(f"Empty transcription for audio {audio_id}")
                        continue

                    # Get metadata
                    metadata_ref = audio_doc.reference.collection('metadata').document('details')
                    metadata_doc = metadata_ref.get()
                    metadata_data = metadata_doc.to_dict() if metadata_doc.exists else {}

                    # Build training item
                    training_item = {
                        'audio_id': audio_id,
                        'audio_url': audio_data.get('audio_url'),
                        'transcription': transcription_content,
                        'language': transcription_data.get('language', 'masalit'),
                        'dialect': metadata_data.get('dialect'),
                        'duration': audio_data.get('duration', 0),
                        'gender': metadata_data.get('gender'),
                        'format': audio_data.get('format', 'audio/wav'),
                        'recording_context': metadata_data.get('recording_context')
                    }

                    # Validate required fields
                    if training_item['audio_url'] and training_item['transcription']:
                        training_data.append(training_item)
                        logger.debug(f"Added training sample: {audio_id}")

                        # Check if we've reached the max samples limit
                        if max_samples and len(training_data) >= max_samples:
                            break
                    else:
                        logger.warning(f"Skipping {audio_id}: missing audio_url or transcription")

                except Exception as e:
                    logger.warning(f"Error processing audio {audio_doc.id}: {e}")
                    continue

            logger.info(f"Retrieved {len(training_data)} valid training samples for {model_type}")
            return training_data

        except Exception as e:
            logger.error(f"Error getting training data: {e}")
            raise

    def mark_data_as_trained(self, audio_ids: List[str], training_session_id: str, model_type: str = 'asr'):
        """Mark multiple audio samples as trained"""
        try:
            batch = self.db.batch()

            for audio_id in audio_ids:
                training_ref = (self.db.collection('audio')
                               .document(audio_id)
                               .collection('training')
                               .document('status'))

                training_updates = {
                    'last_trained_at': datetime.now().isoformat(),
                    'updated_at': datetime.now().isoformat()
                }

                if model_type == 'asr':
                    training_updates['trained_asr'] = True
                elif model_type == 'tts':
                    training_updates['tts_trained'] = True

                # Add training session to history
                # Note: We'll need to get current sessions first, but for batch operations
                # we'll just update the basic status here
                batch.update(training_ref, training_updates)

            batch.commit()
            logger.info(f"Marked {len(audio_ids)} audio samples as trained for {model_type}")

        except Exception as e:
            logger.error(f"Error marking data as trained: {e}")
            raise

    def update_monitoring_data(self, monitor_type: str, data: Dict[str, Any]):
        """Update monitoring data in the system collection"""
        try:
            # Store monitoring data in system collection
            monitor_ref = (self.db.collection('system')
                          .document('monitoring')
                          .collection(monitor_type)
                          .document(datetime.now().strftime('%Y%m%d_%H%M%S')))

            monitor_ref.set(data)
            logger.info(f"Updated monitoring data for {monitor_type}")

        except Exception as e:
            logger.error(f"Error updating monitoring data: {e}")
            raise

    # ==================== USER MANAGEMENT (V2) ====================

    def create_user_record(self, user_data: Dict[str, Any]) -> str:
        """Create hierarchical user record"""
        try:
            user_id = user_data.get('user_id')
            if not user_id:
                raise ValueError("user_id is required")

            user_ref = self.db.collection('users').document(user_id)

            # Main user document (core info only)
            main_data = {
                'email': user_data.get('email'),
                'name': user_data.get('name'),
                'username': user_data.get('username'),
                'role': user_data.get('role', 'user'),
                'created_at': datetime.now().isoformat()
            }
            user_ref.set(main_data)

            # Profile subcollection
            profile_data = {
                'language_preferences': user_data.get('language_preferences', ['masalit']),
                'email_verified': user_data.get('email_verified', False),
                'phone_verified': False,
                'created_at': datetime.now().isoformat()
            }
            user_ref.collection('profile').document('details').set(profile_data)

            # Statistics subcollection
            stats_data = {
                'contribution_count': 0,
                'audio_uploads': 0,
                'transcriptions_created': 0,
                'reviews_completed': 0,
                'training_sessions': 0,
                'total_audio_duration': 0,
                'created_at': datetime.now().isoformat()
            }
            user_ref.collection('statistics').document('summary').set(stats_data)

            # Preferences subcollection
            preferences_data = {
                'theme': 'light',
                'language': 'en',
                'notifications': {
                    'email': True,
                    'push': True,
                    'training_updates': True,
                    'review_notifications': True
                },
                'privacy': {
                    'profile_public': False,
                    'stats_public': False,
                    'allow_contact': True
                },
                'created_at': datetime.now().isoformat()
            }
            user_ref.collection('preferences').document('settings').set(preferences_data)

            # Security subcollection
            security_data = {
                'login_count': 0,
                'failed_login_attempts': 0,
                'account_locked': False,
                'two_factor_enabled': False,
                'is_disabled': False,
                'created_at': datetime.now().isoformat()
            }
            user_ref.collection('security').document('status').set(security_data)

            logger.info(f"✅ Created hierarchical user record: {user_id}")
            return user_id

        except Exception as e:
            logger.error(f"Error creating user record: {e}")
            raise

    def update_user_statistics(self, user_id: str, stat_updates: Dict[str, Any]):
        """Update user statistics"""
        try:
            stats_ref = (self.db.collection('users')
                        .document(user_id)
                        .collection('statistics')
                        .document('summary'))

            stat_updates['updated_at'] = datetime.now().isoformat()
            stats_ref.update(stat_updates)

            logger.info(f"✅ Updated user statistics: {user_id}")

        except Exception as e:
            logger.error(f"Error updating user statistics: {e}")
            raise

    # ==================== MODEL MANAGEMENT ====================

    def save_model_record(self, model_data: Dict[str, Any]):
        """Save model record (unchanged from original)"""
        try:
            model_id = model_data.get('model_id')
            if not model_id:
                raise ValueError("model_id is required")

            doc_ref = self.db.collection('models').document(model_id)
            model_data['updated_at'] = datetime.now().isoformat()
            doc_ref.set(model_data, merge=True)

            logger.info(f"✅ Saved model record: {model_id}")
            return model_id

        except Exception as e:
            logger.error(f"Error saving model record: {e}")
            raise
    
    def list_models(self, model_type: Optional[str] = None, status: Optional[str] = None, limit: int = 50) -> List[Dict[str, Any]]:
        """List models with optional filters"""
        try:
            query = self.db.collection('models')
            
            if model_type:
                query = query.where('model_type', '==', model_type)
            if status:
                query = query.where('status', '==', status)
            
            query = query.order_by('updated_at', direction=firestore.Query.DESCENDING).limit(limit)
            
            docs = query.stream()
            return [doc.to_dict() for doc in docs]
            
        except Exception as e:
            logger.error(f"Error listing models: {e}")
            raise

# Global instance
clean_firebase_service = CleanFirebaseService()
