#!/bin/bash

# Production Deployment Script for Ubuntu Server
# Run this script on your Ubuntu server after pulling from GitHub

set -e  # Exit on any error

echo "🚀 Deploying Masalit AI Platform to Production"
echo "=============================================="

# Configuration
APP_DIR="/var/www/masalit-ai"
SERVICE_USER="www-data"
NODE_VERSION="18"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root for security reasons"
   exit 1
fi

# Check if we're in the right directory
if [[ ! -f "package.json" ]]; then
    print_error "package.json not found. Please run this script from the project root directory."
    exit 1
fi

print_status "Starting deployment process..."

# Step 1: Pull latest changes from GitHub
print_status "Pulling latest changes from GitHub..."
git pull origin main || {
    print_error "Failed to pull from GitHub"
    exit 1
}

# Step 2: Install/Update Node.js dependencies
print_status "Installing frontend dependencies..."
if command -v pnpm &> /dev/null; then
    pnpm install --frozen-lockfile
else
    npm ci
fi

# Step 3: Install/Update Python dependencies
print_status "Installing backend dependencies..."
cd backend
python3 -m pip install -r requirements.txt --user

# Step 3.1: Setup backend environment if needed
if [[ ! -f ".env" ]]; then
    if [[ -f ".env.example" ]]; then
        print_warning "Backend .env file not found. Creating from template..."
        cp .env.example .env
        print_warning "Please edit backend/.env with your actual credentials before running again."
        exit 1
    else
        print_warning "Backend .env file not found and no template available."
        print_warning "Backend will use environment variables from root .env file."
    fi
else
    print_status "Backend .env file found"
fi

cd ..

# Step 4: Build the frontend
print_status "Building frontend for production..."
if command -v pnpm &> /dev/null; then
    pnpm build
else
    npm run build
fi

# Step 5: Stop existing services
print_status "Stopping existing services..."
pkill -f "python.*start-backend.py" || true
pkill -f "node.*next" || true
pkill -f "npm.*start" || true
sleep 2

# Step 6: Start backend service
print_status "Starting backend service..."
nohup python3 start-backend.py > logs/backend.log 2>&1 &
BACKEND_PID=$!
echo $BACKEND_PID > backend.pid

# Wait for backend to start
sleep 5

# Check if backend is running
if ! curl -f http://127.0.0.1:8000/health > /dev/null 2>&1; then
    print_warning "Backend health check failed, but continuing..."
fi

# Step 7: Start frontend service
print_status "Starting frontend service..."
if command -v pnpm &> /dev/null; then
    nohup pnpm start > logs/frontend.log 2>&1 &
else
    nohup npm start > logs/frontend.log 2>&1 &
fi
FRONTEND_PID=$!
echo $FRONTEND_PID > frontend.pid

# Wait for frontend to start
sleep 5

# Step 8: Reload Nginx
print_status "Reloading Nginx configuration..."
sudo nginx -t && sudo systemctl reload nginx || {
    print_warning "Nginx reload failed. Please check configuration manually."
}

# Step 9: Verify deployment
print_status "Verifying deployment..."

# Check frontend
if curl -f http://127.0.0.1:3000 > /dev/null 2>&1; then
    print_status "✅ Frontend is running on port 3000"
else
    print_error "❌ Frontend is not responding"
fi

# Check backend
if curl -f http://127.0.0.1:8000/health > /dev/null 2>&1; then
    print_status "✅ Backend is running on port 8000"
else
    print_error "❌ Backend is not responding"
fi

# Check public access
if curl -f https://buragatechnologies.com > /dev/null 2>&1; then
    print_status "✅ Public site is accessible"
else
    print_warning "⚠️  Public site check failed (might be normal if DNS/Cloudflare not configured)"
fi

print_status "Deployment completed!"
echo ""
echo "📊 Service Status:"
echo "  Frontend: http://127.0.0.1:3000 (Internal)"
echo "  Backend:  http://127.0.0.1:8000 (Internal)"
echo "  Public:   https://buragatechnologies.com"
echo ""
echo "📝 Logs:"
echo "  Frontend: logs/frontend.log"
echo "  Backend:  logs/backend.log"
echo "  Nginx:    /var/log/nginx/buragatechnologies.com.*.log"
echo ""
echo "🔧 Management Commands:"
echo "  Stop services: ./stop-services.sh"
echo "  View logs:     tail -f logs/*.log"
echo "  Restart:       ./deploy-production.sh"
