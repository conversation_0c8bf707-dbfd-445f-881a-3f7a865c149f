"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Shield, History, Settings, RefreshCw } from "lucide-react"
import Link from "next/link"

interface QuickActionsBarProps {
  onAdvancedSettingsClick?: () => void
  onResetTrainingClick?: () => void
}

export function QuickActionsBar({
  onAdvancedSettingsClick,
  onResetTrainingClick
}: QuickActionsBarProps) {
  return (
    <div className="flex items-center justify-center space-x-4 p-6 bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg border-0">
      <Link href="/dashboard/ai/asr/validation">
        <Button variant="outline" className="h-12 px-6">
          <Shield className="mr-2 h-5 w-5" />
          Validate Model
        </Button>
      </Link>
      
      <Link href="/dashboard/ai/asr/history">
        <Button variant="outline" className="h-12 px-6">
          <History className="mr-2 h-5 w-5" />
          Training History
        </Button>
      </Link>
      
      <Button
        variant="outline"
        className="h-12 px-6"
        onClick={onAdvancedSettingsClick}
      >
        <Settings className="mr-2 h-5 w-5" />
        Advanced Settings
      </Button>

      <Button
        variant="outline"
        className="h-12 px-6"
        onClick={onResetTrainingClick}
      >
        <RefreshCw className="mr-2 h-5 w-5" />
        Reset Training
      </Button>
    </div>
  )
}
