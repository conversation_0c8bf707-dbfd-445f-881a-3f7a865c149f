"use client"

import type React from "react"

import { useState, useRef } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import Link from "next/link"
import { useAuth } from "@/components/auth-provider"
import { useFocusedLanguage } from "@/components/focused-language-provider"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertCircle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { PasswordInput } from "@/components/ui/password-input"
import { Recaptcha, RecaptchaRef } from "@/components/ui/recaptcha"

export function LoginForm() {
  const [identifier, setIdentifier] = useState("")
  const [password, setPassword] = useState("")
  const [error, setError] = useState("")
  const [resetEmail, setResetEmail] = useState("")
  const [resetError, setResetError] = useState("")
  const [resetSuccess, setResetSuccess] = useState(false)
  const [isResetting, setIsResetting] = useState(false)
  const [recaptchaToken, setRecaptchaToken] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const recaptchaRef = useRef<RecaptchaRef>(null)
  const { login, resetPassword } = useAuth()
  const { t, isRTL } = useFocusedLanguage()
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setIsLoading(true)

    // Check if reCAPTCHA is verified
    if (!recaptchaToken) {
      setError(isRTL ? "يرجى التحقق من أنك لست روبوت" : "Please verify that you are not a robot")
      setIsLoading(false)
      return
    }

    try {
      await login(identifier, password, recaptchaToken)
      router.push("/dashboard")
    } catch (err: any) {
      // Reset reCAPTCHA on error
      recaptchaRef.current?.reset()
      setRecaptchaToken(null)

      // Handle specific error codes
      switch (err.code) {
        case 'auth/account-disabled':
          setError(err.message)
          break
        case 'auth/invalid-credentials':
          setError(err.message)
          break
        default:
          setError(err.message || 'An error occurred during login. Please try again.')
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handlePasswordReset = async (e: React.FormEvent) => {
    e.preventDefault()
    setResetError("")
    setResetSuccess(false)
    setIsResetting(true)

    try {
      await resetPassword(resetEmail)
      setResetSuccess(true)
    } catch (err: any) {
      setResetError(err.message || "Failed to send password reset email")
    } finally {
      setIsResetting(false)
    }
  }

  return (
    <Card className={`w-full ${isRTL ? 'rtl' : 'ltr'}`}>
      <CardHeader>
        <CardTitle>{t('signIn')}</CardTitle>
        <CardDescription>{t('welcomeToMasalit')}</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="identifier">{t('usernameOrEmail')}</Label>
            <Input
              id="identifier"
              type="text"
              placeholder={isRTL ? "اسم المستخدم أو البريد الإلكتروني" : "<NAME_EMAIL>"}
              value={identifier}
              onChange={(e) => setIdentifier(e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">{t('password')}</Label>
            <PasswordInput
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Recaptcha
              ref={recaptchaRef}
              onVerify={setRecaptchaToken}
              onExpired={() => setRecaptchaToken(null)}
              onError={() => setRecaptchaToken(null)}
              className="flex justify-center"
            />
          </div>

          <Button type="submit" className="w-full" disabled={isLoading || !recaptchaToken}>
            {isLoading ? (isRTL ? "جاري تسجيل الدخول..." : "Signing in...") : t('signIn')}
          </Button>
        </form>
      </CardContent>
      <CardFooter className="flex flex-col gap-2">
        <p className="text-sm text-muted-foreground">
          {t('dontHaveAccount')}{" "}
          <Link href="/signup" className="text-primary hover:underline">
            {t('signUp')}
          </Link>
        </p>
        <Dialog>
          <DialogTrigger asChild>
            <Button variant="link" className="text-sm text-muted-foreground">
              {t('forgotPassword')}
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{t('resetPassword')}</DialogTitle>
              <DialogDescription>
                {t('resetPasswordDesc')}
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handlePasswordReset} className="space-y-4">
              {resetError && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{resetError}</AlertDescription>
                </Alert>
              )}
              {resetSuccess && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    {isRTL ? "تم إرسال رابط إعادة تعيين كلمة المرور! يرجى التحقق من بريدك الإلكتروني." : "Password reset email sent! Please check your inbox."}
                  </AlertDescription>
                </Alert>
              )}
              <div className="space-y-2">
                <Label htmlFor="resetEmail">{t('email')}</Label>
                <Input
                  id="resetEmail"
                  type="email"
                  placeholder={isRTL ? "بريدك.الإلكتروني@example.com" : "<EMAIL>"}
                  value={resetEmail}
                  onChange={(e) => setResetEmail(e.target.value)}
                  required
                  disabled={isResetting}
                />
              </div>
              <Button type="submit" className="w-full" disabled={isResetting}>
                {isResetting ? (isRTL ? "جاري الإرسال..." : "Sending...") : t('sendResetLink')}
              </Button>
            </form>
          </DialogContent>
        </Dialog>
      </CardFooter>
    </Card>
  )
}
