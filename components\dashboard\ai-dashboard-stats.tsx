"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Activity,
  BarChart3,
  Database,
  HardDrive,
  CheckCircle2,
  XCircle,
  Clock,
  Server,
  TrendingUp,
  Zap
} from "lucide-react"
import { cn } from "@/lib/utils"

interface SystemStats {
  cpu: number
  memory: number
  storage: number
  isOnline: boolean
  uptime?: number
  backendUrl?: string
  memoryUsed?: string
  memoryTotal?: string
  storageUsed?: string
  storageTotal?: string
}

interface AIStatsProps {
  stats: SystemStats | null
  loading: boolean
}

export function AISystemStats({ stats, loading }: AIStatsProps) {
  const getResourceColor = (value: number) => {
    if (value >= 80) return "text-red-500"
    if (value >= 60) return "text-orange-500"
    if (value >= 40) return "text-yellow-500"
    return "text-green-500"
  }

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400)
    const hours = Math.floor((seconds % 86400) / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)

    if (days > 0) return `${days}d ${hours}h`
    if (hours > 0) return `${hours}h ${minutes}m`
    return `${minutes}m`
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Server Status</CardTitle>
          <Activity className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          {loading ? (
            <Skeleton className="h-8 w-20" />
          ) : (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                {stats?.isOnline ? (
                  <CheckCircle2 className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
                <Badge
                  className={cn(
                    stats?.isOnline
                      ? "bg-green-500 hover:bg-green-600 text-white"
                      : "bg-red-500 hover:bg-red-600 text-white"
                  )}
                >
                  {stats?.isOnline ? 'Online' : 'Offline'}
                </Badge>
              </div>
              <div className="space-y-1">
                <div className="text-sm font-medium">
                  Uptime: {stats?.uptime ? formatUptime(stats.uptime) : 'N/A'}
                </div>
                {stats?.backendUrl && (
                  <div className="text-xs text-muted-foreground">
                    {stats.backendUrl}
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">CPU Usage</CardTitle>
          <BarChart3 className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          {loading ? (
            <Skeleton className="h-8 w-16" />
          ) : (
            <div className="space-y-2">
              <div className={cn("text-2xl font-bold", getResourceColor(stats?.cpu || 0))}>
                {stats?.cpu || 0}%
              </div>
              <Progress 
                value={stats?.cpu || 0} 
                className="h-2"
                showDynamicColors={true}
              />
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
          <Database className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          {loading ? (
            <Skeleton className="h-8 w-16" />
          ) : (
            <div className="space-y-2">
              <div className={cn("text-2xl font-bold", getResourceColor(stats?.memory || 0))}>
                {stats?.memory || 0}%
              </div>
              {stats?.memoryUsed && stats?.memoryTotal && (
                <div className="text-xs text-muted-foreground">
                  {stats.memoryUsed} / {stats.memoryTotal}
                </div>
              )}
              <Progress
                value={stats?.memory || 0}
                className="h-2"
                showDynamicColors={true}
              />
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Storage Usage</CardTitle>
          <HardDrive className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          {loading ? (
            <Skeleton className="h-8 w-16" />
          ) : (
            <div className="space-y-2">
              <div className={cn("text-2xl font-bold", getResourceColor(stats?.storage || 0))}>
                {stats?.storage || 0}%
              </div>
              {stats?.storageUsed && stats?.storageTotal && (
                <div className="text-xs text-muted-foreground">
                  {stats.storageUsed} / {stats.storageTotal}
                </div>
              )}
              <Progress
                value={stats?.storage || 0}
                className="h-2"
                showDynamicColors={true}
              />
            </div>
          )}
        </CardContent>
      </Card>


    </div>
  )
}

interface ModelCardProps {
  title: string
  description: string
  icon: React.ReactNode
  status: string
  accuracy?: number
  lastTraining?: string
  version?: string
  href: string
  loading: boolean
  isServerOnline: boolean
}

export function ModelCard({
  title,
  description,
  icon,
  status,
  accuracy,
  lastTraining,
  version,
  href,
  loading,
  isServerOnline
}: ModelCardProps) {
  const getStatusBadge = () => {
    if (status === 'active' && isServerOnline) {
      return <Badge className="bg-green-500 hover:bg-green-600 text-white">Active</Badge>
    } else if (status === 'training') {
      return <Badge className="bg-blue-500 hover:bg-blue-600 text-white">Training</Badge>
    } else if (status === 'inactive' || !isServerOnline) {
      return <Badge variant="secondary">Inactive</Badge>
    } else if (status === 'error') {
      return <Badge variant="destructive">Error</Badge>
    }
    return <Badge variant="outline">Unknown</Badge>
  }

  const getAccuracyColor = (acc: number) => {
    if (acc >= 90) return "text-green-500"
    if (acc >= 80) return "text-yellow-500"
    if (acc >= 70) return "text-orange-500"
    return "text-red-500"
  }

  return (
    <Card className="hover:bg-accent/50 transition-colors cursor-pointer h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            {icon}
            {title}
          </CardTitle>
          {loading ? (
            <Skeleton className="h-6 w-16" />
          ) : (
            getStatusBadge()
          )}
        </div>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {loading ? (
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-2 w-full" />
              <Skeleton className="h-8 w-24" />
            </div>
          ) : (
            <>
              {/* Model Accuracy/Confidence */}
              {accuracy !== undefined && accuracy !== null && accuracy > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">
                      {title.includes('TTS') ? 'Quality Score' : 'Model Confidence'}
                    </span>
                    <span className={cn("text-sm font-bold", getAccuracyColor(accuracy))}>
                      {accuracy}%
                    </span>
                  </div>
                  <Progress
                    value={accuracy}
                    className="h-2"
                    showDynamicColors={true}
                    colorScheme="inverse"
                  />
                </div>
              )}

              {/* Training Information */}
              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Last training:</span>
                  <span>{lastTraining || 'Never'}</span>
                </div>
                {version && (
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">Version:</span>
                    <span>{version}</span>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

interface ActivityFeedProps {
  activities: Array<{
    id: string
    type: 'info' | 'success' | 'warning' | 'error'
    message: string
    timestamp: string
  }>
  loading: boolean
}

export function ActivityFeed({ activities, loading }: ActivityFeedProps) {
  const getActivityColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'bg-green-500'
      case 'warning':
        return 'bg-yellow-500'
      case 'error':
        return 'bg-red-500'
      default:
        return 'bg-blue-500'
    }
  }

  // Ensure activities is always an array
  const safeActivities = Array.isArray(activities) ? activities : []

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          Recent Activity
        </CardTitle>
        <CardDescription>Latest system events and training updates</CardDescription>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-3">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        ) : safeActivities.length === 0 ? (
          <p className="text-sm text-muted-foreground">No recent activity</p>
        ) : (
          <div className="space-y-3">
            {safeActivities.map((activity) => (
              <div key={activity.id} className="flex items-center gap-3 text-sm">
                <div className={cn("h-2 w-2 rounded-full", getActivityColor(activity.type))}></div>
                <span className="text-muted-foreground flex-1">
                  {activity.message}
                </span>
                <span className="text-xs text-muted-foreground">
                  {new Date(activity.timestamp).toLocaleTimeString()}
                </span>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
