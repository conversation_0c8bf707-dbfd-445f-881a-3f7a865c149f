# Production Setup Guide

## 🏗️ Architecture Overview

```
Internet → Cloudflare → Nginx (Ubuntu Server) → Next.js (Port 3000)
                                              → FastAPI (Port 8000, Internal)
```

## 🖥️ Development Workflow

1. **Develop locally** on your Windows laptop
2. **Push to GitHub** when ready
3. **Pull on Ubuntu server** and deploy
4. **Nginx + Cloudflare** handle SSL and public access

## 📋 Ubuntu Server Setup

### Prerequisites

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Python 3.11+
sudo apt install python3 python3-pip python3-venv -y

# Install pnpm (optional but recommended)
npm install -g pnpm

# Install Nginx (if not already installed)
sudo apt install nginx -y

# Install Git
sudo apt install git -y
```

### Initial Setup

```bash
# 1. Clone repository
cd /var/www
sudo git clone https://github.com/iabakar/masalit-ai.git
sudo chown -R $USER:$USER masalit-ai
cd masalit-ai

# 2. Create logs directory
mkdir -p logs

# 3. Install dependencies
pnpm install
cd backend && pip3 install -r requirements.txt --user && cd ..

# 4. Setup backend environment
cd backend
cp .env.example .env
# Edit backend/.env with your actual Firebase and GCS credentials
nano .env
cd ..

# 5. Configure Nginx
sudo cp nginx.conf /etc/nginx/sites-available/buragatechnologies.com
sudo ln -s /etc/nginx/sites-available/buragatechnologies.com /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx

# 6. Make scripts executable
chmod +x *.sh
```

## 🔧 Environment Configuration

### Backend Environment Setup

The backend requires its own environment file with sensitive credentials:

```bash
# On Ubuntu server, after cloning the repository
cd /var/www/masalit-ai/backend

# Copy the template
cp .env.example .env

# Edit with your actual credentials
nano .env
```

**Required credentials in `backend/.env`:**
- Firebase service account private key
- Google Cloud Storage service account private key
- Project IDs and client information

### Environment File Priority

The backend looks for environment files in this order:
1. `backend/.env` (backend-specific, highest priority)
2. `.env` (root level, production settings)
3. `.env.local` (development only, not used in production)

## 🚀 Deployment Process

### From Your Laptop (Development)

```bash
# 1. Develop and test locally
./start-development.sh

# 2. When ready, commit and push
git add .
git commit -m "Your changes"
git push origin main
```

### On Ubuntu Server (Production)

```bash
# 1. Navigate to project directory
cd /var/www/masalit-ai

# 2. Deploy latest changes
./deploy-production.sh
```

## 🔧 Service Management

### Start Services
```bash
./deploy-production.sh
```

### Stop Services
```bash
./stop-services.sh
```

### View Logs
```bash
# Real-time logs
tail -f logs/frontend.log
tail -f logs/backend.log

# Nginx logs
sudo tail -f /var/log/nginx/buragatechnologies.com.access.log
sudo tail -f /var/log/nginx/buragatechnologies.com.error.log
```

### Check Status
```bash
# Check if services are running
curl http://127.0.0.1:3000  # Frontend
curl http://127.0.0.1:8000/health  # Backend
curl https://buragatechnologies.com  # Public access
```

## 🔒 Security Configuration

### Nginx Security Features
- ✅ Rate limiting (login, API, general)
- ✅ Real IP detection from Cloudflare
- ✅ Security headers
- ✅ SSL termination
- ✅ Static file caching

### Application Security
- ✅ Backend internal only (127.0.0.1:8000)
- ✅ Frontend proxied through Nginx
- ✅ Google reCAPTCHA protection
- ✅ Firebase authentication
- ✅ Input validation

### Cloudflare Benefits
- ✅ DDoS protection
- ✅ SSL/TLS encryption
- ✅ CDN caching
- ✅ Bot protection
- ✅ Analytics

## 📊 Monitoring

### Health Checks
```bash
# Create a monitoring script
cat > monitor.sh << 'EOF'
#!/bin/bash
echo "=== Service Health Check ==="
echo "Frontend: $(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:3000)"
echo "Backend:  $(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:8000/health)"
echo "Public:   $(curl -s -o /dev/null -w "%{http_code}" https://buragatechnologies.com)"
EOF
chmod +x monitor.sh
```

### Log Rotation
```bash
# Add to /etc/logrotate.d/masalit-ai
sudo tee /etc/logrotate.d/masalit-ai << 'EOF'
/var/www/masalit-ai/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    copytruncate
}
EOF
```

## 🔄 Auto-Deployment (Optional)

### GitHub Webhook Setup
```bash
# Install webhook listener
sudo npm install -g github-webhook-handler

# Create webhook script
cat > webhook.js << 'EOF'
const http = require('http');
const createHandler = require('github-webhook-handler');
const { exec } = require('child_process');

const handler = createHandler({ path: '/webhook', secret: 'your-webhook-secret' });

http.createServer((req, res) => {
  handler(req, res, (err) => {
    res.statusCode = 404;
    res.end('no such location');
  });
}).listen(7777);

handler.on('push', (event) => {
  if (event.payload.ref === 'refs/heads/main') {
    exec('cd /var/www/masalit-ai && ./deploy-production.sh', (error, stdout, stderr) => {
      console.log('Deployment triggered:', stdout);
      if (error) console.error('Deployment error:', error);
    });
  }
});
EOF
```

## 🐛 Troubleshooting

### Common Issues

**Services not starting:**
```bash
# Check logs
tail -f logs/*.log

# Check ports
sudo netstat -tlnp | grep -E ':(3000|8000)'

# Restart services
./stop-services.sh && ./deploy-production.sh
```

**Nginx errors:**
```bash
# Test configuration
sudo nginx -t

# Check Nginx logs
sudo tail -f /var/log/nginx/error.log

# Restart Nginx
sudo systemctl restart nginx
```

**Permission issues:**
```bash
# Fix ownership
sudo chown -R $USER:$USER /var/www/masalit-ai

# Fix permissions
chmod +x *.sh
```

## 📈 Performance Optimization

### PM2 Process Manager (Recommended)
```bash
# Install PM2
npm install -g pm2

# Create ecosystem file
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [
    {
      name: 'masalit-frontend',
      script: 'npm',
      args: 'start',
      cwd: '/var/www/masalit-ai',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production'
      }
    },
    {
      name: 'masalit-backend',
      script: 'start-backend.py',
      interpreter: 'python3',
      cwd: '/var/www/masalit-ai',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '2G'
    }
  ]
};
EOF

# Start with PM2
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

This setup provides a robust, secure, and scalable production environment!
