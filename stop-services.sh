#!/bin/bash

# Stop all Masalit AI services

echo "🛑 Stopping Masalit AI Platform Services"
echo "========================================"

# Stop processes by PID files
if [[ -f "backend.pid" ]]; then
    BACKEND_PID=$(cat backend.pid)
    if kill -0 $BACKEND_PID 2>/dev/null; then
        echo "Stopping backend (PID: $BACKEND_PID)..."
        kill $BACKEND_PID
        rm backend.pid
    fi
fi

if [[ -f "frontend.pid" ]]; then
    FRONTEND_PID=$(cat frontend.pid)
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        echo "Stopping frontend (PID: $FRONTEND_PID)..."
        kill $FRONTEND_PID
        rm frontend.pid
    fi
fi

# Fallback: kill by process name
echo "Stopping any remaining processes..."
pkill -f "python.*start-backend.py" || true
pkill -f "node.*next" || true
pkill -f "npm.*start" || true
pkill -f "pnpm.*start" || true

echo "✅ All services stopped"
