#!/usr/bin/env python3
"""
Verify Improved System Structure

This script verifies that the improved system collections structure is working correctly.
"""

import sys
import os
from datetime import datetime

# Add backend to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.firebase_clean import clean_firebase_service
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def verify_improved_structure():
    """Verify the improved system structure"""
    try:
        clean_firebase_service.initialize()
        
        print("🔍 Improved System Structure Verification")
        print("=" * 60)
        
        # 1. Test System Configuration
        print("\n📁 Testing System Configuration:")
        
        config_types = ['logging', 'security', 'performance', 'features']
        for config_type in config_types:
            try:
                config = clean_firebase_service.get_system_config(config_type)
                print(f"  ✅ {config_type}: {len(config)} settings")
                
                # Show a few key settings
                if config_type == 'logging':
                    print(f"     - Level: {config.get('level', 'N/A')}")
                    print(f"     - Max File Size: {config.get('max_file_size_mb', 'N/A')} MB")
                elif config_type == 'security':
                    print(f"     - Max Login Attempts: {config.get('max_login_attempts', 'N/A')}")
                    print(f"     - Session Timeout: {config.get('session_timeout_minutes', 'N/A')} min")
                elif config_type == 'performance':
                    print(f"     - Max Concurrent Trainings: {config.get('max_concurrent_trainings', 'N/A')}")
                    print(f"     - Cache TTL: {config.get('cache_ttl_minutes', 'N/A')} min")
                elif config_type == 'features':
                    print(f"     - ASR Training: {config.get('asr_training_enabled', 'N/A')}")
                    print(f"     - Real-time Updates: {config.get('real_time_updates', 'N/A')}")
                    
            except Exception as e:
                print(f"  ❌ {config_type}: Error - {e}")
        
        # 2. Test System Configuration Structure
        print("\n🏗️ Testing System Configuration Structure:")
        try:
            # Check if system/config/settings exists
            settings_docs = clean_firebase_service.db.collection('system').document('config').collection('settings').get()
            print(f"  ✅ system/config/settings: {len(settings_docs)} documents")
            
            for doc in settings_docs:
                data = doc.to_dict()
                print(f"     - {doc.id}: Created {data.get('created_at', 'N/A')}")
                
        except Exception as e:
            print(f"  ❌ System config structure: Error - {e}")
        
        # 3. Test Logs Structure
        print("\n📝 Testing Logs Structure:")
        try:
            # Check logs collection structure
            logs_docs = clean_firebase_service.db.collection('logs').get()
            print(f"  ✅ logs/: {len(logs_docs)} log types")
            
            for doc in logs_docs:
                log_type = doc.id
                # Check entries subcollection
                try:
                    entries = doc.reference.collection('entries').limit(1).get()
                    entry_count = len(entries)
                    print(f"     - {log_type}/entries: {entry_count} entries")
                except:
                    print(f"     - {log_type}/entries: 0 entries")
                    
        except Exception as e:
            print(f"  ❌ Logs structure: Error - {e}")
        
        # 4. Test Monitoring Structure
        print("\n📊 Testing Monitoring Structure:")
        try:
            monitoring_docs = clean_firebase_service.db.collection('monitoring').get()
            print(f"  ✅ monitoring/: {len(monitoring_docs)} monitoring types")
            
            for doc in monitoring_docs:
                data = doc.to_dict()
                monitor_type = doc.id
                print(f"     - {monitor_type}: Last updated {data.get('last_updated', 'N/A')}")
                
                if monitor_type == 'health':
                    print(f"       Status: {data.get('status', 'N/A')}")
                elif monitor_type == 'metrics':
                    print(f"       Total Users: {data.get('total_users', 0)}")
                    print(f"       Total Training Sessions: {data.get('total_training_sessions', 0)}")
                elif monitor_type == 'alerts':
                    print(f"       Active Alerts: {len(data.get('active_alerts', []))}")
                    
        except Exception as e:
            print(f"  ❌ Monitoring structure: Error - {e}")
        
        # 5. Test System Configuration Methods
        print("\n🧪 Testing System Configuration Methods:")
        try:
            # Test updating a system config
            test_config = {
                'test_setting': 'test_value',
                'test_timestamp': datetime.now().isoformat()
            }
            
            clean_firebase_service.update_system_config('logging', test_config)
            print("  ✅ update_system_config: Working")
            
            # Test getting the updated config
            updated_config = clean_firebase_service.get_system_config('logging')
            if updated_config.get('test_setting') == 'test_value':
                print("  ✅ get_system_config: Working")
            else:
                print("  ⚠️ get_system_config: Update not reflected")
                
        except Exception as e:
            print(f"  ❌ System config methods: Error - {e}")
        
        # 6. Test System Logging Methods
        print("\n📋 Testing System Logging Methods:")
        try:
            # Test adding a system log
            test_log = {
                'level': 'INFO',
                'message': 'System structure verification test',
                'source': 'verification_script'
            }
            
            clean_firebase_service.add_system_log('system', test_log)
            print("  ✅ add_system_log: Working")
            
            # Test getting system logs
            logs = clean_firebase_service.get_system_logs('system', limit=1)
            if logs and len(logs) > 0:
                print(f"  ✅ get_system_logs: Retrieved {len(logs)} logs")
                latest_log = logs[0]
                print(f"     Latest: {latest_log.get('message', 'N/A')}")
            else:
                print("  ⚠️ get_system_logs: No logs found")
                
        except Exception as e:
            print(f"  ❌ System logging methods: Error - {e}")
        
        # 7. Test Monitoring Methods
        print("\n📈 Testing Monitoring Methods:")
        try:
            # Test updating monitoring data
            test_metrics = {
                'total_users': 5,
                'total_training_sessions': 3,
                'total_validations': 8,
                'system_uptime_hours': 24
            }
            
            clean_firebase_service.update_monitoring_data('metrics', test_metrics)
            print("  ✅ update_monitoring_data: Working")
            
        except Exception as e:
            print(f"  ❌ Monitoring methods: Error - {e}")
        
        # 8. Structure Comparison
        print("\n📊 Structure Comparison:")
        print("  BEFORE (Old Structure):")
        print("    ❌ system_logs/ (scattered)")
        print("    ❌ system_settings/ (scattered)")
        print("    ❌ _migration_logs/ (temporary)")
        print("    ❌ _migration_backup/ (temporary)")
        print()
        print("  AFTER (New Structure):")
        print("    ✅ system/config/settings/* (unified)")
        print("    ✅ logs/*/entries/* (organized)")
        print("    ✅ monitoring/* (centralized)")
        
        print("\n" + "=" * 60)
        print("✅ Improved System Structure Verification COMPLETED!")
        print("🎉 System collections are now properly organized!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Verification failed: {e}")
        return False

def main():
    """Main verification function"""
    print("🚀 Starting Improved System Structure Verification...")
    
    success = verify_improved_structure()
    
    if success:
        print("\n✅ VERIFICATION SUCCESSFUL!")
        print("💡 System collections are now properly organized.")
        print("🎯 Benefits achieved:")
        print("   - Unified system configuration")
        print("   - Organized log structure")
        print("   - Centralized monitoring")
        print("   - Better performance and maintainability")
    else:
        print("\n❌ VERIFICATION FAILED!")
        print("🔧 Check the logs above for details.")

if __name__ == "__main__":
    main()
