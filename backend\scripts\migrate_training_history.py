#!/usr/bin/env python3
"""
Migration script to populate training_history collection from existing training_status data
"""

import sys
import os

# Add the backend directory to Python path
backend_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(backend_dir)
sys.path.insert(0, parent_dir)
sys.path.insert(0, backend_dir)

from services.firebase import firebase_service
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_training_history():
    """Migrate existing training status data to training history collection"""
    try:
        firebase_service.initialize()
        
        # Get all training status documents
        status_docs = firebase_service.db.collection('training_status').get()
        
        migrated_count = 0
        
        for doc in status_docs:
            try:
                model_type = doc.id  # asr, tts, etc.
                status_data = doc.to_dict()
                
                # Only migrate completed training sessions that have a model version
                if (status_data.get('status') == 'completed' and 
                    status_data.get('model_version') and 
                    status_data.get('model_version') != ''):
                    
                    # Check if history record already exists
                    model_version = status_data.get('model_version')
                    existing_history = firebase_service.db.collection('training_history').document(model_version).get()
                    
                    if existing_history.exists:
                        logger.info(f"History record already exists for {model_version}, skipping")
                        continue
                    
                    # Create training history record from status data
                    history_data = {
                        "model_type": model_type,
                        "model_version": model_version,
                        "status": "completed",
                        "samples_trained": status_data.get('samples_trained', 0),
                        "model_path": status_data.get('model_path', ''),
                        "gcs_url": status_data.get('gcs_url', ''),
                        "upload_success": status_data.get('upload_success', False),
                        "start_time": status_data.get('start_time', ''),
                        "end_time": status_data.get('end_time', ''),
                        "epochs": status_data.get('total_epochs', 0),
                        "accuracy": status_data.get('current_accuracy', 0.0),
                        "confidence": status_data.get('current_confidence', 0.0),
                        "wer": status_data.get('current_wer', 0.0),
                        "cer": status_data.get('current_cer', 0.0),
                        "ser": status_data.get('current_ser', 0.0),
                        "timestamp": status_data.get('end_time', datetime.now().isoformat()),
                        "base_model": f"openai/whisper-small",  # Default assumption
                        "training_settings": {
                            "epochs": status_data.get('total_epochs', 5),
                            "model_name": "small"
                        }
                    }
                    
                    # Save to training history
                    firebase_service.save_training_history(model_type, history_data)
                    migrated_count += 1
                    logger.info(f"Migrated {model_type} training: {model_version}")
                    
            except Exception as e:
                logger.error(f"Error migrating {doc.id}: {e}")
                continue
        
        logger.info(f"Migration completed. Migrated {migrated_count} training records.")
        return migrated_count
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        raise

if __name__ == "__main__":
    migrate_training_history()
