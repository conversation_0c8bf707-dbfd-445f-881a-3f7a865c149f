"""
Model Validation API Routes

Unified API endpoints for validating all AI model types
"""

import logging
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel

from backend.core.validation.validator import model_validator
from backend.core.validation.types import (
    ValidationConfig, ValidationResult, ValidationType,
    MetricType, ValidationStatus
)
from backend.core.models.types import ModelType
from backend.services.firebase_clean import clean_firebase_service

logger = logging.getLogger(__name__)

router = APIRouter()


class ValidationRequest(BaseModel):
    model_config = {"protected_namespaces": ()}

    model_id: str
    validation_type: ValidationType = ValidationType.QUICK
    model_type: ModelType
    max_samples: Optional[int] = None
    min_confidence: Optional[float] = None
    test_dataset: Optional[str] = None
    metrics_to_compute: List[MetricType] = []
    batch_size: int = 8
    save_predictions: bool = True
    save_error_analysis: bool = True


class ValidationStatusResponse(BaseModel):
    model_config = {"protected_namespaces": ()}

    validation_id: str
    status: ValidationStatus
    progress_percentage: Optional[float] = None
    message: Optional[str] = None
    result: Optional[ValidationResult] = None


@router.post("/validate", response_model=ValidationStatusResponse)
async def start_validation(request: ValidationRequest, background_tasks: BackgroundTasks):
    """Start model validation"""
    try:
        # Create validation configuration
        config = ValidationConfig(
            validation_type=request.validation_type,
            model_type=request.model_type,
            max_samples=request.max_samples,
            min_confidence=request.min_confidence,
            test_dataset=request.test_dataset,
            metrics_to_compute=request.metrics_to_compute,
            batch_size=request.batch_size,
            save_predictions=request.save_predictions,
            save_error_analysis=request.save_error_analysis
        )
        
        # Start validation in background
        async def run_validation():
            try:
                await model_validator.validate_model(request.model_id, config)
            except Exception as e:
                logger.error(f"Background validation failed: {e}")
        
        background_tasks.add_task(run_validation)
        
        return ValidationStatusResponse(
            validation_id="temp_id",  # Will be replaced with actual ID
            status=ValidationStatus.PENDING,
            message="Validation started"
        )
        
    except Exception as e:
        logger.error(f"Error starting validation: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/validate/sync", response_model=ValidationResult)
async def validate_model_sync(request: ValidationRequest):
    """Run model validation synchronously (for quick validations)"""
    try:
        # Create validation configuration
        config = ValidationConfig(
            validation_type=request.validation_type,
            model_type=request.model_type,
            max_samples=request.max_samples,
            min_confidence=request.min_confidence,
            test_dataset=request.test_dataset,
            metrics_to_compute=request.metrics_to_compute,
            batch_size=request.batch_size,
            save_predictions=request.save_predictions,
            save_error_analysis=request.save_error_analysis
        )
        
        # Run validation
        result = await model_validator.validate_model(request.model_id, config)
        return result
        
    except Exception as e:
        logger.error(f"Error in synchronous validation: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/validate/{validation_id}", response_model=ValidationStatusResponse)
async def get_validation_status(validation_id: str):
    """Get validation status and results"""
    try:
        result = await model_validator.get_validation_status(validation_id)
        
        if not result:
            raise HTTPException(status_code=404, detail="Validation not found")
        
        return ValidationStatusResponse(
            validation_id=validation_id,
            status=result.status,
            result=result
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting validation status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/validate/{validation_id}/cancel")
async def cancel_validation(validation_id: str):
    """Cancel an active validation"""
    try:
        success = await model_validator.cancel_validation(validation_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Validation not found or already completed")
        
        return {"message": "Validation cancelled successfully", "validation_id": validation_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling validation: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/results/{model_id}")
async def get_model_validation_history(model_id: str, limit: Optional[int] = 10):
    """Get validation history for a model"""
    try:
        # Query clean Firebase structure for validation results
        clean_firebase_service.initialize()

        # Get validation history from clean structure
        validation_history = clean_firebase_service.get_validation_history(model_id=model_id, limit=limit or 10)

        return {
            "model_id": model_id,
            "total_validations": len(validation_history),
            "results": validation_history
        }
        
    except Exception as e:
        logger.error(f"Error getting validation history: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/metrics/types")
async def get_supported_metrics():
    """Get list of supported validation metrics"""
    return {
        "metrics": [
            {
                "type": metric.value,
                "name": metric.value.upper(),
                "description": f"{metric.value.upper()} metric"
            }
            for metric in MetricType
        ]
    }


@router.get("/types/supported")
async def get_supported_validation_types():
    """Get list of supported validation types"""
    return {
        "validation_types": [
            {
                "type": val_type.value,
                "name": val_type.value.title(),
                "description": f"{val_type.value.title()} validation"
            }
            for val_type in ValidationType
        ]
    }


@router.post("/benchmark/{model_id}")
async def run_benchmark(
    model_id: str, 
    benchmark_name: str = "standard",
    background_tasks: BackgroundTasks = None
):
    """Run benchmark validation on a model"""
    try:
        # Get model to determine type
        from backend.core.models.manager import ModelManager
        model_manager = ModelManager()
        
        model = await model_manager.get_model(model_id)
        if not model:
            raise HTTPException(status_code=404, detail="Model not found")
        
        # Create benchmark configuration
        config = ValidationConfig(
            validation_type=ValidationType.BENCHMARK,
            model_type=model.type,
            max_samples=None,  # Use all available data for benchmark
            save_predictions=True,
            save_error_analysis=True,
            generate_report=True
        )
        
        if background_tasks:
            # Run in background
            async def run_benchmark_validation():
                try:
                    await model_validator.validate_model(model_id, config)
                except Exception as e:
                    logger.error(f"Benchmark validation failed: {e}")
            
            background_tasks.add_task(run_benchmark_validation)
            
            return {
                "message": "Benchmark validation started",
                "model_id": model_id,
                "benchmark_name": benchmark_name
            }
        else:
            # Run synchronously
            result = await model_validator.validate_model(model_id, config)
            return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error running benchmark: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stats/summary")
async def get_validation_stats():
    """Get summary statistics for all validations"""
    try:
        # Get validation statistics from clean structure
        clean_firebase_service.initialize()

        # Get all validation results from clean structure
        validation_history = clean_firebase_service.get_validation_history(limit=100)  # Get more for stats

        # Calculate statistics
        stats = {
            "total_validations": len(validation_history),
            "by_status": {},
            "by_model_type": {},
            "average_metrics": {},
            "recent_validations": []
        }

        for result in validation_history:
            # Count by status
            status = result.get('status', 'unknown')
            if status not in stats["by_status"]:
                stats["by_status"][status] = 0
            stats["by_status"][status] += 1

            # Count by model type
            model_type = result.get('model_type', 'unknown')
            if model_type not in stats["by_model_type"]:
                stats["by_model_type"][model_type] = 0
            stats["by_model_type"][model_type] += 1

        # Get recent validations (first 5 from the list)
        stats["recent_validations"] = [
            {
                "validation_id": result.get('validation_id'),
                "model_id": result.get('model_id'),
                "status": result.get('status'),
                "started_at": result.get('created_at')
            }
            for result in validation_history[:5]
        ]

        return stats
        
    except Exception as e:
        logger.error(f"Error getting validation stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))
