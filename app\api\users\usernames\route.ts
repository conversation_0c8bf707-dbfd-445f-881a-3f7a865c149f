import { NextRequest, NextResponse } from 'next/server'
import { collection, query, where, getDocs } from 'firebase/firestore'
import { db } from '@/lib/firebase'

export async function POST(request: NextRequest) {
  try {
    const { userIds } = await request.json()
    
    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json({ usernames: {} })
    }

    // Limit to 30 user IDs to avoid Firestore query limits
    const limitedUserIds = userIds.slice(0, 30)
    
    // Query users collection for the provided user IDs
    const usersRef = collection(db, 'users')
    const q = query(usersRef, where('__name__', 'in', limitedUserIds))
    const querySnapshot = await getDocs(q)
    
    const usernames: Record<string, string> = {}
    
    querySnapshot.forEach((doc) => {
      const userData = doc.data()
      const userId = doc.id
      
      // Prefer username, fallback to name, then email, then user ID
      const displayName = userData.username || 
                         userData.name || 
                         userData.email?.split('@')[0] || 
                         userId
      
      usernames[userId] = displayName
    })
    
    // For any user IDs not found, use the user ID as fallback
    limitedUserIds.forEach(userId => {
      if (!usernames[userId]) {
        usernames[userId] = userId
      }
    })
    
    return NextResponse.json({ usernames })
    
  } catch (error) {
    console.error('Error fetching usernames:', error)
    return NextResponse.json(
      { error: 'Failed to fetch usernames' },
      { status: 500 }
    )
  }
}
