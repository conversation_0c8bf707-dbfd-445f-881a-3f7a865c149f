# Masalit AI Platform - Project Structure

## 📁 Clean Project Structure

```
masalit-ai/
├── 📱 Frontend (Next.js)
│   ├── app/                    # Next.js 13+ App Router
│   │   ├── api/               # API routes
│   │   ├── auth/              # Authentication pages
│   │   ├── dashboard/         # Dashboard pages
│   │   ├── signup/            # Signup page
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Home page
│   ├── components/            # React components
│   │   ├── ui/               # UI components
│   │   ├── dashboard/        # Dashboard components
│   │   └── *.tsx             # Other components
│   ├── hooks/                # Custom React hooks
│   ├── lib/                  # Utility libraries
│   ├── types/                # TypeScript type definitions
│   └── public/               # Static assets
│
├── 🐍 Backend (FastAPI)
│   ├── api/                  # API routes
│   │   ├── routes/          # Route handlers
│   │   └── schemas/         # Pydantic schemas
│   ├── core/                # Core functionality
│   │   ├── asr/            # ASR training & inference
│   │   ├── models/         # Model management
│   │   └── validation/     # Model validation
│   ├── services/           # External services
│   │   ├── firebase.py     # Firebase integration
│   │   └── gcs.py          # Google Cloud Storage
│   ├── config/             # Configuration
│   ├── utils/              # Utility functions
│   ├── main.py             # FastAPI application
│   ├── gunicorn.conf.py    # Production server config
│   └── requirements.txt    # Python dependencies
│
├── 🔧 Configuration
│   ├── .env                # Production environment
│   ├── .env.local          # Development environment
│   ├── .gitignore          # Git ignore rules
│   ├── next.config.mjs     # Next.js configuration
│   ├── tailwind.config.ts  # Tailwind CSS config
│   ├── tsconfig.json       # TypeScript config
│   ├── package.json        # Node.js dependencies
│   └── pnpm-lock.yaml      # Package lock file
│
├── 🚀 Deployment
│   ├── nginx.conf          # Nginx configuration
│   ├── deploy-production.sh # Production deployment
│   ├── start-development.sh # Development startup
│   ├── start-backend.py    # Backend startup script
│   └── stop-services.sh    # Service management
│
├── 📚 Documentation
│   ├── README.md           # Main documentation
│   ├── PRODUCTION_SETUP.md # Production setup guide
│   ├── PROJECT_STRUCTURE.md # This file
│   └── firestore_collections.txt # Database schema
│
└── 🗂️ Data & Models
    ├── backend/ai_models/  # AI model storage (gitignored)
    │   ├── checkpoints/   # Training checkpoints
    │   └── trained/       # Final trained models
    └── logs/              # Application logs (gitignored)
```

## 🧹 Cleaned Up Files

### ❌ Removed Files
- `tests/` - Test files (not needed in production)
- `docs/` - Old documentation files
- `frontend/` - Empty directory
- `temp/` - Temporary directories
- `generate-ssl.py` - SSL generation (not needed with Nginx)
- `setup-local-hosting.py` - Local setup scripts
- `*test*.py` - Test scripts
- `requirements-asr.txt` - Redundant requirements
- `SECURITY_SETUP.md` - Consolidated into PRODUCTION_SETUP.md
- `OPTIMIZED_ARCHITECTURE.md` - Consolidated documentation

### ✅ Kept Essential Files
- Core application code (app/, backend/, components/)
- Configuration files (.env, next.config.mjs, etc.)
- Production deployment scripts
- Main documentation (README.md, PRODUCTION_SETUP.md)
- Package management files (package.json, requirements.txt)

## 🎯 File Purposes

### Frontend Files
- **app/**: Next.js 13+ App Router structure
- **components/**: Reusable React components
- **lib/**: Utility functions and API clients
- **types/**: TypeScript type definitions
- **hooks/**: Custom React hooks

### Backend Files
- **api/**: FastAPI route handlers and schemas
- **core/**: Business logic (ASR, models, validation)
- **services/**: External service integrations
- **config/**: Application configuration
- **main.py**: FastAPI application entry point

### Configuration Files
- **.env**: Production environment variables
- **.env.local**: Development environment variables
- **next.config.mjs**: Next.js configuration with security headers
- **tailwind.config.ts**: Tailwind CSS configuration
- **tsconfig.json**: TypeScript compiler configuration

### Deployment Files
- **nginx.conf**: Nginx reverse proxy configuration
- **deploy-production.sh**: Automated production deployment
- **start-backend.py**: Backend server startup script
- **gunicorn.conf.py**: Production WSGI server configuration

## 🔒 Security Features

### Environment Separation
- Development: `localhost:3000` → `127.0.0.1:8000`
- Production: `buragatechnologies.com` → `127.0.0.1:8000` (internal)

### Security Layers
1. **Nginx**: SSL termination, rate limiting, security headers
2. **Cloudflare**: DDoS protection, CDN, bot protection
3. **Application**: reCAPTCHA, input validation, authentication
4. **Network**: Internal backend (127.0.0.1 only)

## 📊 Dependencies

### Frontend Dependencies
- **Next.js**: React framework with App Router
- **TypeScript**: Type safety
- **Tailwind CSS**: Styling framework
- **Firebase**: Authentication and database
- **React Hook Form**: Form handling
- **Lucide React**: Icons

### Backend Dependencies
- **FastAPI**: Modern Python web framework
- **Uvicorn**: ASGI server
- **Firebase Admin**: Server-side Firebase
- **Transformers**: Hugging Face ML models
- **PyTorch**: Machine learning framework
- **Google Cloud Storage**: Model storage

## 🚀 Development Workflow

1. **Local Development**: Use `start-development.sh`
2. **Code Changes**: Edit files, test locally
3. **Commit & Push**: Git workflow to GitHub
4. **Production Deploy**: Run `deploy-production.sh` on server
5. **Monitor**: Check logs and service status

This clean structure focuses on production-ready code while maintaining development efficiency!
