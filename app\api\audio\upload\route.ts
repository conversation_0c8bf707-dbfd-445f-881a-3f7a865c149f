import { NextRequest, NextResponse } from 'next/server';
import { uploadAudio } from '@/lib/audio-upload';

export async function POST(request: NextRequest) {
  try {
    console.log('[Upload Route] Starting audio upload process...');
    
    // Get the form data from the request
    const formData = await request.formData();
    console.log('[Upload Route] Form data received:', {
      hasFile: formData.has('file'),
      hasMetadata: formData.has('metadata'),
      hasUserId: formData.has('user_id')
    });

    const file = formData.get('file') as File;
    const metadata = JSON.parse(formData.get('metadata') as string);
    const userId = formData.get('user_id') as string;

    console.log('[Upload Route] Parsed data:', {
      fileName: file?.name,
      fileSize: file?.size,
      fileType: file?.type,
      metadata,
      userId
    });

    if (!file || !metadata || !userId) {
      console.error('[Upload Route] Missing required fields:', { file: !!file, metadata: !!metadata, userId: !!userId });
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Upload the audio using the existing uploadAudio function
    console.log('[Upload Route] Calling uploadAudio function...');
    const audioId = await uploadAudio(file, metadata, userId);
    console.log('[Upload Route] Upload successful, audioId:', audioId);

    return NextResponse.json({ 
      success: true,
      audioId 
    });
  } catch (error: any) {
    console.error('[Upload Route] Error in upload route:', error);
    console.error('[Upload Route] Error details:', {
      message: error.message,
      code: error.code,
      stack: error.stack
    });
    return NextResponse.json(
      { error: error.message || 'Failed to upload audio' },
      { status: 500 }
    );
  }
} 