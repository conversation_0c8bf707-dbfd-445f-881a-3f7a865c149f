"""
Masalit AI Platform Backend
FastAPI application entry point
"""

import os
import sys
from pathlib import Path
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from dotenv import load_dotenv

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables
load_dotenv(project_root / ".env")

from backend.config.settings import settings
from backend.api.routes import asr, health, system, models, validation, logs
from backend.services.firebase import firebase_service
from backend.services.logging_service import logging_service, log_system_event, log_api_request
import time

# Create FastAPI application
app = FastAPI(
    title="Masalit AI Platform API",
    description="Backend API for Masalit language ASR and TTS services",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add logging middleware
@app.middleware("http")
async def log_requests(request, call_next):
    """Log all API requests"""
    start_time = time.time()

    # Process the request
    response = await call_next(request)

    # Calculate duration
    duration_ms = (time.time() - start_time) * 1000

    # Log the request (async, don't wait)
    try:
        log_api_request(
            endpoint=str(request.url.path),
            method=request.method,
            status_code=response.status_code,
            duration_ms=duration_ms,
            details={
                'query_params': dict(request.query_params),
                'user_agent': request.headers.get('user-agent', ''),
                'client_ip': request.client.host if request.client else 'unknown'
            }
        )
    except Exception as e:
        # Don't let logging errors affect the response
        print(f"Failed to log request: {e}")

    return response

# Include routers
app.include_router(health.router, prefix="/api", tags=["health"])
app.include_router(system.router, prefix="/api", tags=["system"])
app.include_router(asr.router, prefix="/api/asr", tags=["asr"])
app.include_router(models.router, prefix="/api/models", tags=["models"])
app.include_router(validation.router, prefix="/api/validation", tags=["validation"])
app.include_router(logs.router, prefix="/api", tags=["logs"])

# Include V2 routers (improved hierarchical structure)
# TODO: Add audio_v2 and users_v2 routers when implemented
# app.include_router(audio_v2.router, prefix="/api/audio", tags=["audio-improved"])
# app.include_router(users_v2.router, prefix="/api/users", tags=["users-improved"])

@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    try:
        # Initialize Firebase
        firebase_service.initialize()
        print("✓ Firebase initialized successfully")
        log_system_event("Firebase initialized successfully", "INFO", "BACKEND")

        # Initialize logging service
        logging_service.setup_root_logger()
        print("✓ Logging service initialized")
        log_system_event("Logging service initialized", "INFO", "BACKEND")

        # Create necessary directories
        os.makedirs(settings.CHECKPOINT_DIR, exist_ok=True)
        os.makedirs(settings.FINAL_MODEL_DIR, exist_ok=True)
        os.makedirs(settings.TEMP_DIR, exist_ok=True)
        print("✓ Model directories created")
        log_system_event("Model directories created", "INFO", "BACKEND")

        # Check ASR dependencies
        try:
            import torch
            from transformers import WhisperProcessor
            print(f"✓ ASR dependencies available (PyTorch {torch.__version__})")
            log_system_event(f"ASR dependencies available (PyTorch {torch.__version__})", "INFO", "BACKEND")
            if torch.cuda.is_available():
                gpu_name = torch.cuda.get_device_name()
                print(f"✓ CUDA available: {gpu_name}")
                log_system_event(f"CUDA available: {gpu_name}", "INFO", "BACKEND")
            else:
                print("ℹ️ CUDA not available - using CPU for training")
                log_system_event("CUDA not available - using CPU for training", "WARNING", "BACKEND")
        except ImportError as e:
            print(f"⚠️ ASR dependencies missing: {e}")
            print("💡 Run: python backend/install_asr_deps.py")
            log_system_event(f"ASR dependencies missing: {e}", "WARNING", "BACKEND")

        print("🚀 Masalit AI Platform Backend started successfully")
        log_system_event("Masalit AI Platform Backend started successfully", "INFO", "BACKEND")

    except Exception as e:
        print(f"❌ Startup failed: {e}")
        log_system_event(f"Backend startup failed: {e}", "ERROR", "BACKEND")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    print("🛑 Masalit AI Platform Backend shutting down")

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Masalit AI Platform Backend",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs"
    }

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "detail": str(exc) if settings.DEBUG else "An error occurred"
        }
    )

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.API_HOST,
        port=settings.API_PORT,
        reload=settings.DEBUG,
        log_level="info" if settings.DEBUG else "warning"
    )
