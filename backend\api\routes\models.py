"""
Model Management API Routes

Unified API endpoints for managing all AI model types
"""

import logging
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel

from backend.core.models.manager import ModelManager
from backend.core.models.types import (
    ModelType, ModelInfo, ModelComparison, CleanupConfig, CleanupResult,
    ModelStatus
)

logger = logging.getLogger(__name__)

router = APIRouter()
model_manager = ModelManager()


class ModelListRequest(BaseModel):
    model_config = {"protected_namespaces": ()}

    model_type: Optional[ModelType] = None
    status: Optional[ModelStatus] = None
    tags: Optional[List[str]] = None
    limit: Optional[int] = None


class ModelCompareRequest(BaseModel):
    model_config = {"protected_namespaces": ()}

    model_id1: str
    model_id2: str


class ModelTagRequest(BaseModel):
    model_config = {"protected_namespaces": ()}

    model_id: str
    tags: List[str]


class ModelCleanupRequest(BaseModel):
    model_config = {"protected_namespaces": ()}

    config: CleanupConfig


@router.get("/", response_model=List[ModelInfo])
async def list_models(
    model_type: Optional[str] = Query(None, description="Filter by model type"),
    status: Optional[str] = Query(None, description="Filter by status"),
    tags: Optional[str] = Query(None, description="Filter by tags (comma-separated)"),
    limit: Optional[int] = Query(None, description="Limit number of results")
):
    """List all models with optional filtering"""
    try:
        # Parse parameters
        model_type_enum = ModelType(model_type) if model_type else None
        status_enum = ModelStatus(status) if status else None
        tags_list = tags.split(",") if tags else None
        
        models = await model_manager.list_models(
            model_type=model_type_enum,
            status=status_enum,
            tags=tags_list,
            limit=limit
        )
        
        return models
        
    except Exception as e:
        logger.error(f"Error listing models: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{model_id}")
async def get_model(model_id: str):
    """Get detailed information about a specific model"""
    try:
        model = await model_manager.get_model(model_id)
        
        if not model:
            raise HTTPException(status_code=404, detail="Model not found")
        
        return model
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting model {model_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/compare", response_model=ModelComparison)
async def compare_models(request: ModelCompareRequest):
    """Compare two models"""
    try:
        comparison = await model_manager.compare_models(
            request.model_id1, 
            request.model_id2
        )
        
        return comparison
        
    except Exception as e:
        logger.error(f"Error comparing models: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tag")
async def tag_model(request: ModelTagRequest):
    """Add tags to a model"""
    try:
        success = await model_manager.tag_model(request.model_id, request.tags)
        
        if not success:
            raise HTTPException(status_code=404, detail="Model not found")
        
        return {"message": "Tags added successfully", "model_id": request.model_id, "tags": request.tags}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error tagging model: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/cleanup", response_model=CleanupResult)
async def cleanup_models(request: ModelCleanupRequest):
    """Clean up old models based on configuration"""
    try:
        result = await model_manager.cleanup_models(request.config)
        return result
        
    except Exception as e:
        logger.error(f"Error cleaning up models: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{model_id}")
async def delete_model(model_id: str):
    """Delete a specific model"""
    try:
        # Extract model type from ID
        model_type_str = model_id.split('_')[0]
        model_type = ModelType(model_type_str)
        
        success = await model_manager.storage.delete_model(model_type, model_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Model not found")
        
        return {"message": "Model deleted successfully", "model_id": model_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting model {model_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{model_id}/versions")
async def get_model_versions(model_id: str):
    """Get version history for a model"""
    try:
        # Extract model info from ID
        parts = model_id.split('_')
        if len(parts) < 2:
            raise HTTPException(status_code=400, detail="Invalid model ID format")
        
        model_type = ModelType(parts[0])
        model_name = parts[1]
        
        versions = await model_manager.versioning.list_versions(model_type, model_name)
        
        return {
            "model_id": model_id,
            "model_type": model_type.value,
            "model_name": model_name,
            "versions": versions
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting model versions: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{model_id}/rollback/{version}")
async def rollback_model(model_id: str, version: str):
    """Rollback model to a specific version"""
    try:
        # Extract model info from ID
        parts = model_id.split('_')
        if len(parts) < 2:
            raise HTTPException(status_code=400, detail="Invalid model ID format")
        
        model_type = ModelType(parts[0])
        model_name = parts[1]
        
        rollback_model_id = await model_manager.versioning.rollback_to_version(
            model_type, model_name, version
        )
        
        if not rollback_model_id:
            raise HTTPException(status_code=404, detail="Version not found")
        
        return {
            "message": "Model rolled back successfully",
            "model_id": model_id,
            "version": version,
            "active_model_id": rollback_model_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error rolling back model: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/types/supported")
async def get_supported_model_types():
    """Get list of supported model types"""
    return {
        "model_types": [
            {
                "type": model_type.value,
                "name": model_type.value.upper(),
                "description": f"{model_type.value.upper()} model type"
            }
            for model_type in ModelType
        ]
    }


@router.get("/stats/summary")
async def get_model_stats():
    """Get summary statistics for all models"""
    try:
        all_models = await model_manager.list_models()
        
        # Calculate statistics
        stats = {
            "total_models": len(all_models),
            "by_type": {},
            "by_status": {},
            "total_size_mb": 0,
            "latest_model": None
        }
        
        for model in all_models:
            # Count by type
            model_type = model.type.value
            if model_type not in stats["by_type"]:
                stats["by_type"][model_type] = 0
            stats["by_type"][model_type] += 1
            
            # Count by status
            status = model.status.value
            if status not in stats["by_status"]:
                stats["by_status"][status] = 0
            stats["by_status"][status] += 1
            
            # Sum size
            if model.size_mb:
                stats["total_size_mb"] += model.size_mb
            
            # Track latest model
            if not stats["latest_model"] or model.created_at > stats["latest_model"]["created_at"]:
                stats["latest_model"] = {
                    "id": model.id,
                    "name": model.name,
                    "type": model.type.value,
                    "created_at": model.created_at.isoformat()
                }
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting model stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))
