"use client"

import React, { useState, useR<PERSON>, use<PERSON><PERSON>back, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/components/auth-provider"
import { bulkUploadAudio } from "@/lib/audio-api-v2"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Upload, X, Loader2, CheckCircle2, AlertCircle, FileAudio,
  Menu, ArrowLeft, Home, Trash2, Edit3, Download, Play, Pause,
  Smartphone, Monitor, Cloud, Zap, Shield, Star, Eye, Settings
} from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { useFocusedLanguage } from "@/components/focused-language-provider"
import { FocusedLanguageProvider } from "@/components/focused-language-provider"
import Link from "next/link"

const MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB
const MAX_FILES = 50 // Maximum files per batch
const SUPPORTED_AUDIO_FORMATS = ['audio/wav', 'audio/mp3', 'audio/mpeg', 'audio/ogg', 'audio/webm']

interface FileWithMetadata {
  file: File
  id: string
  title: string
  transcription: string
  gender: string
  duration: number
  status: 'pending' | 'processing' | 'completed' | 'error'
  error?: string
  progress: number
}

// Device detection hook
const useDeviceDetection = () => {
  const [isMobile, setIsMobile] = useState(false)
  const [isTablet, setIsTablet] = useState(false)

  useEffect(() => {
    const checkDevice = () => {
      const width = window.innerWidth
      setIsMobile(width < 768)
      setIsTablet(width >= 768 && width < 1024)
    }

    checkDevice()
    window.addEventListener('resize', checkDevice)
    return () => window.removeEventListener('resize', checkDevice)
  }, [])

  return { isMobile, isTablet, isDesktop: !isMobile && !isTablet }
}

// Drag and drop hook
const useDragAndDrop = (onDrop: (files: FileList) => void) => {
  const [isDragging, setIsDragging] = useState(false)
  const dragCounter = useRef(0)

  const handleDragEnter = useCallback((e: DragEvent) => {
    e.preventDefault()
    dragCounter.current++
    if (e.dataTransfer?.items && e.dataTransfer.items.length > 0) {
      setIsDragging(true)
    }
  }, [])

  const handleDragLeave = useCallback((e: DragEvent) => {
    e.preventDefault()
    dragCounter.current--
    if (dragCounter.current === 0) {
      setIsDragging(false)
    }
  }, [])

  const handleDragOver = useCallback((e: DragEvent) => {
    e.preventDefault()
  }, [])

  const handleDrop = useCallback((e: DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
    dragCounter.current = 0

    if (e.dataTransfer?.files && e.dataTransfer.files.length > 0) {
      onDrop(e.dataTransfer.files)
    }
  }, [onDrop])

  useEffect(() => {
    document.addEventListener('dragenter', handleDragEnter)
    document.addEventListener('dragleave', handleDragLeave)
    document.addEventListener('dragover', handleDragOver)
    document.addEventListener('drop', handleDrop)

    return () => {
      document.removeEventListener('dragenter', handleDragEnter)
      document.removeEventListener('dragleave', handleDragLeave)
      document.removeEventListener('dragover', handleDragOver)
      document.removeEventListener('drop', handleDrop)
    }
  }, [handleDragEnter, handleDragLeave, handleDragOver, handleDrop])

  return { isDragging }
}

function BulkUploadPageContent() {
  const { user } = useAuth()
  const { toast } = useToast()
  const router = useRouter()
  const { t, isRTL } = useFocusedLanguage()
  const { isMobile, isTablet, isDesktop } = useDeviceDetection()

  // State
  const [files, setFiles] = useState<FileWithMetadata[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [overallProgress, setOverallProgress] = useState(0)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)


  const [showResults, setShowResults] = useState(false)
  const [uploadResults, setUploadResults] = useState<any[]>([])
  const [csvMetadata, setCsvMetadata] = useState<Record<string, any>>({})
  const [csvFile, setCsvFile] = useState<File | null>(null)
  const [csvUploaded, setCsvUploaded] = useState(false)

  // Refs
  const fileInputRef = useRef<HTMLInputElement | null>(null)
  const csvInputRef = useRef<HTMLInputElement | null>(null)
  const audioRef = useRef<HTMLAudioElement | null>(null)

  // Drag and drop functionality
  const { isDragging } = useDragAndDrop((files) => {
    handleFileSelection(Array.from(files))
  })

  const calculateDuration = async (file: File): Promise<number> => {
    return new Promise((resolve) => {
      const audio = new Audio()
      const objectUrl = URL.createObjectURL(file)

      audio.addEventListener('loadedmetadata', () => {
        URL.revokeObjectURL(objectUrl)
        resolve(Math.round(audio.duration))
      })

      audio.addEventListener('error', () => {
        URL.revokeObjectURL(objectUrl)
        resolve(0)
      })

      audio.src = objectUrl
    })
  }

  const validateFile = (file: File): string | null => {
    if (file.size > MAX_FILE_SIZE) {
      return `File size must be less than ${MAX_FILE_SIZE / (1024 * 1024)}MB`
    }

    if (!SUPPORTED_AUDIO_FORMATS.includes(file.type)) {
      return 'Unsupported audio format. Please upload WAV, MP3, or OGG files.'
    }

    return null
  }

  const generateTitle = (filename: string): string => {
    // Remove extension and clean up filename
    const nameWithoutExt = filename.replace(/\.[^/.]+$/, "")
    return nameWithoutExt
      .replace(/[_-]/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase())
      .trim()
  }

  const handleFileSelection = async (selectedFiles: File[]) => {
    if (files.length + selectedFiles.length > MAX_FILES) {
      toast({
        title: "Too many files",
        description: `Maximum ${MAX_FILES} files allowed per batch`,
        variant: "destructive",
      })
      return
    }

    const validFiles: FileWithMetadata[] = []
    const errors: string[] = []

    for (const file of selectedFiles) {
      const error = validateFile(file)
      if (error) {
        errors.push(`${file.name}: ${error}`)
        continue
      }

      const duration = await calculateDuration(file)

      // Check if we have CSV metadata for this file
      const csvData = csvMetadata[file.name]

      const fileWithMetadata: FileWithMetadata = {
        file,
        id: `${Date.now()}-${Math.random()}`,
        title: csvData?.title || generateTitle(file.name),
        transcription: csvData?.transcription || "",
        gender: csvData?.gender || "",
        duration,
        status: 'pending',
        progress: 0
      }

      validFiles.push(fileWithMetadata)
    }

    if (errors.length > 0) {
      toast({
        title: "Some files were skipped",
        description: errors.join(', '),
        variant: "destructive",
      })
    }

    setFiles(prev => [...prev, ...validFiles])

    if (validFiles.length > 0) {
      toast({
        title: "Files added",
        description: `${validFiles.length} files ready for upload`,
      })
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files
    if (selectedFiles && selectedFiles.length > 0) {
      handleFileSelection(Array.from(selectedFiles))
    }
  }

  const removeFile = (id: string) => {
    setFiles(prev => prev.filter(f => f.id !== id))
  }

  const updateFileMetadata = (id: string, updates: Partial<FileWithMetadata>) => {
    setFiles(prev => prev.map(f => f.id === id ? { ...f, ...updates } : f))
  }

  const parseCSV = (csvText: string): Record<string, any> => {
    const lines = csvText.trim().split('\n')
    if (lines.length < 2) return {}

    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
    const metadata: Record<string, any> = {}

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''))
      if (values.length >= headers.length) {
        const filename = values[0]
        if (filename) {
          metadata[filename] = {}
          headers.forEach((header, index) => {
            metadata[filename][header] = values[index] || ''
          })
        }
      }
    }

    return metadata
  }

  const handleCSVUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    if (!file.name.toLowerCase().endsWith('.csv') && !file.name.toLowerCase().endsWith('.xlsx')) {
      toast({
        title: "Invalid file type",
        description: "Please select a CSV or Excel file",
        variant: "destructive",
      })
      return
    }

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const csvText = e.target?.result as string
        const parsedMetadata = parseCSV(csvText)
        setCsvMetadata(parsedMetadata)
        setCsvFile(file)
        setCsvUploaded(true)

        toast({
          title: "Spreadsheet uploaded",
          description: `Metadata loaded for ${Object.keys(parsedMetadata).length} files`,
        })

        // Auto-apply metadata to existing files
        applyCSVMetadataToFiles(parsedMetadata)
      } catch (error) {
        toast({
          title: "File parsing failed",
          description: "Please check your file format and try again",
          variant: "destructive",
        })
      }
    }
    reader.readAsText(file)
  }

  const applyCSVMetadataToFiles = (metadata: Record<string, any>) => {
    setFiles(prev => prev.map(f => {
      const csvData = metadata[f.file.name]
      if (csvData) {
        return {
          ...f,
          title: csvData.title || f.title,
          transcription: csvData.transcription || f.transcription,
          gender: csvData.gender || f.gender
        }
      }
      return f
    }))
  }



  const handleUpload = async () => {
    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to upload files",
        variant: "destructive",
      })
      return
    }

    // Use the correct user ID property from auth provider
    const userId = user.id
    console.log('User ID:', userId)

    if (!userId || userId === 'undefined') {
      toast({
        title: "Authentication Error",
        description: "Invalid user session. Please log out and log back in.",
        variant: "destructive",
      })
      return
    }

    const incompleteFiles = files.filter(f => !f.title || !f.transcription || !f.gender)
    if (incompleteFiles.length > 0) {
      toast({
        title: "Incomplete metadata",
        description: `${incompleteFiles.length} files are missing required information`,
        variant: "destructive",
      })
      return
    }

    setIsUploading(true)
    setOverallProgress(0)

    try {
      const filesToUpload = files.map(f => f.file)
      const metadata = files.map(f => ({
        filename: f.file.name,
        title: f.title,
        transcription: f.transcription,
        speaker: f.gender,
        duration: f.duration
      }))

      const result = await bulkUploadAudio(filesToUpload, metadata, userId)

      setUploadResults(result.results || [])
      setShowResults(true)

      const successCount = result.results?.filter(r => r.status === 'success').length || 0
      const errorCount = result.results?.filter(r => r.status === 'error').length || 0

      if (successCount > 0) {
        toast({
          title: "Upload completed",
          description: `Successfully uploaded ${successCount} files${errorCount > 0 ? ` (${errorCount} failed)` : ''}`,
        })

        // Clear files on success
        setFiles([])
      } else {
        toast({
          title: "Upload failed",
          description: `All ${errorCount} files failed to upload. Check the results for details.`,
          variant: "destructive",
        })
      }
    } catch (error: any) {
      toast({
        title: "Upload failed",
        description: error.message || "There was an error uploading your files. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
      setOverallProgress(0)
    }
  }

  if (!user) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Please log in to access bulk upload</div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Mobile Navigation */}
      {isMobile && (
        <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
          <SheetContent side="left" className="w-[300px] sm:w-[400px]">
            <SheetHeader>
              <SheetTitle>Navigation</SheetTitle>
              <SheetDescription>Access your dashboard features</SheetDescription>
            </SheetHeader>
            <div className="grid gap-4 py-4">
              <Link href="/dashboard" className="flex items-center space-x-2 p-2 rounded hover:bg-muted">
                <Home className="h-4 w-4" />
                <span>Dashboard</span>
              </Link>
              <Link href="/dashboard/upload" className="flex items-center space-x-2 p-2 rounded hover:bg-muted">
                <Upload className="h-4 w-4" />
                <span>Upload Audio</span>
              </Link>
              <Link href="/dashboard/history" className="flex items-center space-x-2 p-2 rounded hover:bg-muted">
                <FileAudio className="h-4 w-4" />
                <span>My Recordings</span>
              </Link>
            </div>
          </SheetContent>
        </Sheet>
      )}

      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center space-x-4">
              {isMobile && (
                <SheetTrigger asChild>
                  <Button variant="ghost" size="sm" onClick={() => setIsMobileMenuOpen(true)}>
                    <Menu className="h-5 w-5" />
                  </Button>
                </SheetTrigger>
              )}

              <Link href="/dashboard" className="flex items-center space-x-2">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Dashboard
                </Button>
              </Link>
            </div>

            <div className="flex items-center space-x-4">
              <Badge variant="secondary" className="hidden sm:inline-flex">
                {files.length} files ready
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto p-4 sm:p-6 lg:p-8 space-y-6">
        {/* Page Header */}
        <div className="text-center space-y-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 p-8 rounded-xl border border-blue-200 dark:border-blue-800">
          <h1 className="text-3xl sm:text-4xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Bulk Audio Upload
          </h1>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Upload multiple audio files with spreadsheet metadata matching
          </p>
          <div className="flex flex-wrap justify-center gap-2 mt-4">
            <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
              1. Download Template
            </Badge>
            <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
              2. Upload Spreadsheet
            </Badge>
            <Badge className="bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
              3. Upload Audio Files
            </Badge>
          </div>
        </div>

        {/* Template Download Section */}
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border-blue-200 dark:border-blue-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
                  📋 Step 1: Download Template
                </h3>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  Get the spreadsheet template with columns: filename, title, transcription, gender
                </p>
              </div>
              <Button
                onClick={() => {
                  const link = document.createElement('a')
                  link.href = '/bulk_upload_template.csv'
                  link.download = 'bulk_upload_template.csv'
                  document.body.appendChild(link)
                  link.click()
                  document.body.removeChild(link)
                  toast({
                    title: "Template Downloaded",
                    description: "Fill it out and upload it back!",
                  })
                }}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Download className="h-4 w-4 mr-2" />
                Download Template
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* CSV Upload Section */}
        <Card className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 border-green-200 dark:border-green-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-green-900 dark:text-green-100 mb-2">
                  📊 Step 2: Upload Spreadsheet
                </h3>
                {csvUploaded ? (
                  <div className="flex items-center space-x-2 text-green-700 dark:text-green-300">
                    <CheckCircle2 className="h-4 w-4" />
                    <span className="text-sm font-medium">{csvFile?.name}</span>
                    <span className="text-xs">
                      ({Object.keys(csvMetadata).length} entries loaded)
                    </span>
                  </div>
                ) : (
                  <p className="text-sm text-green-700 dark:text-green-300">
                    Upload your filled spreadsheet
                  </p>
                )}
              </div>
              <div className="flex space-x-2">
                <input
                  ref={csvInputRef}
                  type="file"
                  accept=".csv,.xlsx,.xls"
                  onChange={handleCSVUpload}
                  className="hidden"
                />
                <Button
                  onClick={() => csvInputRef.current?.click()}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  {csvUploaded ? 'Replace' : 'Upload'} Spreadsheet
                </Button>
                {csvUploaded && (
                  <Button
                    onClick={() => {
                      setCsvUploaded(false)
                      setCsvFile(null)
                      setCsvMetadata({})
                      toast({
                        title: "Spreadsheet removed",
                        description: "Upload a new one when ready",
                      })
                    }}
                    variant="outline"
                  >
                    <X className="h-4 w-4 mr-2" />
                    Remove
                  </Button>
                )}
              </div>
            </div>


            {csvUploaded && Object.keys(csvMetadata).length > 0 && (
              <div className="mt-4 p-3 bg-white dark:bg-gray-900 rounded-lg border border-green-200 dark:border-green-800">
                <p className="text-xs font-medium mb-2 text-green-900 dark:text-green-100">Loaded metadata for:</p>
                <div className="flex flex-wrap gap-1">
                  {Object.keys(csvMetadata).slice(0, 5).map((filename) => (
                    <Badge key={filename} variant="secondary" className="text-xs">
                      {filename}
                    </Badge>
                  ))}
                  {Object.keys(csvMetadata).length > 5 && (
                    <Badge variant="secondary" className="text-xs">
                      +{Object.keys(csvMetadata).length - 5} more
                    </Badge>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Upload Section */}
        <Card className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950/20 dark:to-pink-950/20 border-purple-200 dark:border-purple-800">
          <CardContent className="p-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-purple-900 dark:text-purple-100 mb-2">
                🎵 Step 3: Upload Audio Files
              </h3>
              <p className="text-sm text-purple-700 dark:text-purple-300">
                Upload your audio files (WAV, MP3, OGG) - they'll be automatically matched with your spreadsheet
              </p>
            </div>
            {/* Drag and Drop Zone */}
            <div
              className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                isDragging
                  ? 'border-primary bg-primary/5'
                  : 'border-muted-foreground/25 hover:border-muted-foreground/50'
              }`}
            >
              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept="audio/*"
                onChange={handleFileChange}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />

              <div className="space-y-4">
                <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center">
                  <Upload className="h-6 w-6 text-muted-foreground" />
                </div>

                <div className="space-y-2">
                  <p className="text-lg font-medium">
                    {isDragging ? 'Drop files here' : 'Drag and drop audio files'}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    or click to browse your files
                  </p>
                </div>

                <div className="flex flex-wrap justify-center gap-2 text-xs text-muted-foreground">
                  <Badge variant="outline">WAV</Badge>
                  <Badge variant="outline">MP3</Badge>
                  <Badge variant="outline">OGG</Badge>
                  <Badge variant="outline">Max 50MB</Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Files List */}
        {files.length > 0 && (
          <Card className="bg-gradient-to-r from-orange-50 to-yellow-50 dark:from-orange-950/20 dark:to-yellow-950/20 border-orange-200 dark:border-orange-800">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-orange-900 dark:text-orange-100">
                  📁 Ready to Upload ({files.length} files)
                </h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setFiles([])}
                  disabled={isUploading}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear All
                </Button>
              </div>

              <div className="space-y-3 max-h-60 overflow-y-auto">
                {files.map((fileData) => (
                  <div key={fileData.id} className="flex items-center justify-between p-3 bg-white dark:bg-gray-900 rounded-lg border">
                    <div className="flex items-center space-x-3">
                      <FileAudio className="h-5 w-5 text-orange-600" />
                      <div>
                        <p className="font-medium text-sm">{fileData.file.name}</p>
                        <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                          <span>{(fileData.file.size / (1024 * 1024)).toFixed(1)} MB</span>
                          <span>•</span>
                          <span>{fileData.duration}s</span>
                          {csvMetadata[fileData.file.name] ? (
                            <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 text-xs">
                              ✓ Matched
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="text-xs">
                              No metadata
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeFile(fileData.id)}
                      disabled={isUploading}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Upload Button */}
        {files.length > 0 && (
          <Card className="bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-950/20 dark:to-teal-950/20 border-emerald-200 dark:border-emerald-800">
            <CardContent className="p-6">
              <div className="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0">
                <div>
                  <h3 className="text-lg font-semibold text-emerald-900 dark:text-emerald-100">
                    🚀 Ready to Upload
                  </h3>
                  <p className="text-sm text-emerald-700 dark:text-emerald-300">
                    {files.length} files • {files.filter(f => csvMetadata[f.file.name]).length} matched with spreadsheet
                  </p>
                </div>

                <Button
                  onClick={handleUpload}
                  disabled={isUploading || !csvUploaded || files.filter(f => csvMetadata[f.file.name]).length === 0}
                  size="lg"
                  className="bg-emerald-600 hover:bg-emerald-700 text-white"
                >
                  {isUploading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload className="h-4 w-4 mr-2" />
                      Upload All Files
                    </>
                  )}
                </Button>
              </div>

              {/* Overall Progress */}
              {isUploading && (
                <div className="mt-4 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Overall Progress</span>
                    <span>{overallProgress}%</span>
                  </div>
                  <Progress value={overallProgress} className="w-full" />
                </div>
              )}

              {!csvUploaded && files.length > 0 && (
                <div className="mt-4 p-3 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                  <p className="text-sm text-yellow-800 dark:text-yellow-200">
                    ⚠️ Please upload your spreadsheet first to match files with metadata
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )}


      </div>



      {/* Results Dialog */}
      <Dialog open={showResults} onOpenChange={setShowResults}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Upload Results</DialogTitle>
            <DialogDescription>
              Summary of your bulk upload operation
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 max-h-96 overflow-y-auto">
            {uploadResults.map((result, index) => (
              <div key={index} className={`p-3 border rounded ${
                result.status === 'error' ? 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/20' :
                'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950/20'
              }`}>
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <p className="font-medium">{result.filename}</p>
                    <div className="text-sm text-muted-foreground">
                      {result.status === 'success' ? (
                        <span>Duration: {result.duration ? `${result.duration}s` : 'Unknown'}</span>
                      ) : (
                        <span className="text-red-600 dark:text-red-400">
                          Error: {result.error || 'Unknown error occurred'}
                        </span>
                      )}
                    </div>
                  </div>
                  <Badge variant={result.status === 'success' ? 'default' : 'destructive'}>
                    {result.status === 'success' ? '✓ Success' : '✗ Failed'}
                  </Badge>
                </div>
              </div>
            ))}
          </div>

          {uploadResults.length > 0 && (
            <div className="mt-4 p-3 bg-muted rounded-lg">
              <div className="flex justify-between text-sm">
                <span>Total files: {uploadResults.length}</span>
                <div className="space-x-4">
                  <span className="text-green-600">✓ Success: {uploadResults.filter(r => r.status === 'success').length}</span>
                  <span className="text-red-600">✗ Failed: {uploadResults.filter(r => r.status === 'error').length}</span>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button onClick={() => setShowResults(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default function BulkUploadPage() {
  return (
    <FocusedLanguageProvider>
      <BulkUploadPageContent />
    </FocusedLanguageProvider>
  )
}