"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/components/auth-provider"
import { usePreferencesStore, UserPreferencesService } from "@/lib/user-preferences"
import { 
  Settings, 
  Palette, 
  Volume2, 
  Mic, 
  BarChart3, 
  Bell, 
  Shield, 
  Accessibility,
  Monitor,
  Globe,
  Save,
  RotateCcw
} from "lucide-react"

export default function PreferencesPage() {
  const { toast } = useToast()
  const { user } = useAuth()
  const { updatePreferences, preferences, setPreferences } = usePreferencesStore()
  const [saving, setSaving] = useState(false)

  // Load user preferences
  useEffect(() => {
    if (user?.uid && !preferences) {
      const unsubscribe = UserPreferencesService.subscribeToPreferences(
        user.uid,
        setPreferences
      )
      return unsubscribe
    }
  }, [user?.uid, preferences, setPreferences])

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Login Required</CardTitle>
            <CardDescription>
              Please login to access your preferences
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  if (!preferences) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading preferences...</p>
        </div>
      </div>
    )
  }

  const handleSave = async (updates: any) => {
    setSaving(true)
    try {
      await updatePreferences(updates)
      toast({
        title: "Preferences Saved",
        description: "Your preferences have been updated successfully.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save preferences. Please try again.",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-2xl font-bold tracking-tight">User Preferences</h3>
        <p className="text-muted-foreground">
          Customize your experience and manage your settings
        </p>
      </div>

      <Tabs defaultValue="ui" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="ui" className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            UI
          </TabsTrigger>
          <TabsTrigger value="asr" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            ASR
          </TabsTrigger>
          <TabsTrigger value="tts" className="flex items-center gap-2">
            <Volume2 className="h-4 w-4" />
            TTS
          </TabsTrigger>
          <TabsTrigger value="recording" className="flex items-center gap-2">
            <Mic className="h-4 w-4" />
            Recording
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            Notifications
          </TabsTrigger>
          <TabsTrigger value="privacy" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Privacy
          </TabsTrigger>
        </TabsList>

        {/* UI Preferences */}
        <TabsContent value="ui">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                User Interface
              </CardTitle>
              <CardDescription>
                Customize the look and feel of the application
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label>Theme</Label>
                  <Select 
                    value={preferences.ui_preferences.theme} 
                    onValueChange={(value: any) => handleSave({
                      ui_preferences: { ...preferences.ui_preferences, theme: value }
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="light">Light</SelectItem>
                      <SelectItem value="dark">Dark</SelectItem>
                      <SelectItem value="system">System</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Language</Label>
                  <Select 
                    value={preferences.ui_preferences.language} 
                    onValueChange={(value: any) => handleSave({
                      ui_preferences: { ...preferences.ui_preferences, language: value }
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="ar">العربية</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Dashboard Layout</Label>
                  <Select 
                    value={preferences.ui_preferences.dashboard_layout} 
                    onValueChange={(value: any) => handleSave({
                      ui_preferences: { ...preferences.ui_preferences, dashboard_layout: value }
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="compact">Compact</SelectItem>
                      <SelectItem value="comfortable">Comfortable</SelectItem>
                      <SelectItem value="spacious">Spacious</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Font Size</Label>
                  <Select 
                    value={preferences.ui_preferences.font_size} 
                    onValueChange={(value: any) => handleSave({
                      ui_preferences: { ...preferences.ui_preferences, font_size: value }
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="small">Small</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="large">Large</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Sidebar Collapsed</Label>
                    <p className="text-sm text-muted-foreground">
                      Keep the sidebar collapsed by default
                    </p>
                  </div>
                  <Switch
                    checked={preferences.ui_preferences.sidebar_collapsed}
                    onCheckedChange={(checked) => handleSave({
                      ui_preferences: { ...preferences.ui_preferences, sidebar_collapsed: checked }
                    })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Animations</Label>
                    <p className="text-sm text-muted-foreground">
                      Enable smooth animations and transitions
                    </p>
                  </div>
                  <Switch
                    checked={preferences.ui_preferences.animations_enabled}
                    onCheckedChange={(checked) => handleSave({
                      ui_preferences: { ...preferences.ui_preferences, animations_enabled: checked }
                    })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>High Contrast</Label>
                    <p className="text-sm text-muted-foreground">
                      Use high contrast colors for better visibility
                    </p>
                  </div>
                  <Switch
                    checked={preferences.ui_preferences.high_contrast}
                    onCheckedChange={(checked) => handleSave({
                      ui_preferences: { ...preferences.ui_preferences, high_contrast: checked }
                    })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* ASR Preferences */}
        <TabsContent value="asr">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                ASR Settings
              </CardTitle>
              <CardDescription>
                Configure your Automatic Speech Recognition preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label>Default View Mode</Label>
                  <Select 
                    value={preferences.asr_preferences.default_view_mode} 
                    onValueChange={(value: any) => handleSave({
                      asr_preferences: { ...preferences.asr_preferences, default_view_mode: value }
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="table">Table View</SelectItem>
                      <SelectItem value="cards">Cards View</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Default Sort By</Label>
                  <Select 
                    value={preferences.asr_preferences.default_sort_by} 
                    onValueChange={(value: any) => handleSave({
                      asr_preferences: { ...preferences.asr_preferences, default_sort_by: value }
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="date">Date</SelectItem>
                      <SelectItem value="accuracy">Accuracy</SelectItem>
                      <SelectItem value="wer">WER</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Preferred Model Size</Label>
                  <Select 
                    value={preferences.asr_preferences.preferred_model_size} 
                    onValueChange={(value: any) => handleSave({
                      asr_preferences: { ...preferences.asr_preferences, preferred_model_size: value }
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="tiny">Tiny (Fast)</SelectItem>
                      <SelectItem value="small">Small (Balanced)</SelectItem>
                      <SelectItem value="medium">Medium (Better Quality)</SelectItem>
                      <SelectItem value="large">Large (Best Quality)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Auto Refresh Interval (seconds)</Label>
                  <Slider
                    value={[preferences.asr_preferences.auto_refresh_interval]}
                    onValueChange={([value]) => handleSave({
                      asr_preferences: { ...preferences.asr_preferences, auto_refresh_interval: value }
                    })}
                    max={300}
                    min={10}
                    step={10}
                    className="w-full"
                  />
                  <p className="text-sm text-muted-foreground">
                    Current: {preferences.asr_preferences.auto_refresh_interval} seconds
                  </p>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Show Advanced Metrics</Label>
                    <p className="text-sm text-muted-foreground">
                      Display detailed performance metrics
                    </p>
                  </div>
                  <Switch
                    checked={preferences.asr_preferences.show_advanced_metrics}
                    onCheckedChange={(checked) => handleSave({
                      asr_preferences: { ...preferences.asr_preferences, show_advanced_metrics: checked }
                    })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Add other tabs content here... */}
        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notifications
              </CardTitle>
              <CardDescription>
                Manage your notification preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Training Complete</Label>
                  <p className="text-sm text-muted-foreground">
                    Get notified when training finishes
                  </p>
                </div>
                <Switch
                  checked={preferences.asr_preferences.notification_preferences.training_complete}
                  onCheckedChange={(checked) => handleSave({
                    asr_preferences: {
                      ...preferences.asr_preferences,
                      notification_preferences: {
                        ...preferences.asr_preferences.notification_preferences,
                        training_complete: checked
                      }
                    }
                  })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Training Failed</Label>
                  <p className="text-sm text-muted-foreground">
                    Get notified when training fails
                  </p>
                </div>
                <Switch
                  checked={preferences.asr_preferences.notification_preferences.training_failed}
                  onCheckedChange={(checked) => handleSave({
                    asr_preferences: {
                      ...preferences.asr_preferences,
                      notification_preferences: {
                        ...preferences.asr_preferences.notification_preferences,
                        training_failed: checked
                      }
                    }
                  })}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
