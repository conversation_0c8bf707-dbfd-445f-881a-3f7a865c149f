import { getStorage, ref, deleteObject } from 'firebase/storage'
import { promises as fs } from 'fs'
import path from 'path'

export interface DeletionResult {
  success: boolean
  message: string
  error?: string
}

export class FileDeletionService {
  /**
   * Delete an audio file from storage (GCS or local)
   */
  static async deleteAudioFile(audioUrl: string): Promise<DeletionResult> {
    if (!audioUrl) {
      return { success: false, message: 'No audio URL provided' }
    }

    try {
      // Handle Google Cloud Storage / Firebase Storage URLs
      if (audioUrl.includes('storage.googleapis.com') || audioUrl.includes('firebasestorage.googleapis.com')) {
        return await this.deleteFromGCS(audioUrl)
      }
      
      // Handle local file URLs
      if (audioUrl.startsWith('/uploads/') || audioUrl.includes('localhost')) {
        return await this.deleteLocalFile(audioUrl)
      }

      // Handle relative paths
      if (audioUrl.startsWith('uploads/')) {
        return await this.deleteLocalFile(`/${audioUrl}`)
      }

      return { 
        success: false, 
        message: 'Unsupported file URL format',
        error: `Cannot determine storage type for URL: ${audioUrl}`
      }

    } catch (error) {
      console.error('Error in deleteAudioFile:', error)
      return { 
        success: false, 
        message: 'Failed to delete audio file',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Delete file from Google Cloud Storage
   */
  private static async deleteFromGCS(audioUrl: string): Promise<DeletionResult> {
    try {
      const storage = getStorage()
      
      // Extract file path from URL
      let filePath: string
      
      if (audioUrl.includes('/o/')) {
        // Firebase Storage URL format: https://firebasestorage.googleapis.com/v0/b/bucket/o/path%2Fto%2Ffile.mp3
        const urlParts = audioUrl.split('/o/')[1]
        filePath = decodeURIComponent(urlParts.split('?')[0])
      } else {
        // Direct GCS URL format: https://storage.googleapis.com/bucket/path/to/file.mp3
        const urlParts = audioUrl.split('/')
        const bucketIndex = urlParts.findIndex(part => part.includes('.googleapis.com')) + 1
        filePath = urlParts.slice(bucketIndex + 1).join('/')
      }

      const fileRef = ref(storage, filePath)
      await deleteObject(fileRef)
      
      return { 
        success: true, 
        message: `Successfully deleted file from GCS: ${filePath}` 
      }

    } catch (error) {
      console.error('Error deleting from GCS:', error)
      return { 
        success: false, 
        message: 'Failed to delete file from Google Cloud Storage',
        error: error instanceof Error ? error.message : 'Unknown GCS error'
      }
    }
  }

  /**
   * Delete local file
   */
  private static async deleteLocalFile(audioUrl: string): Promise<DeletionResult> {
    try {
      // Extract file path from URL
      let filePath = audioUrl
      
      // Remove localhost and port if present
      if (filePath.includes('localhost')) {
        const urlObj = new URL(filePath)
        filePath = urlObj.pathname
      }
      
      // Convert to absolute path
      const absolutePath = path.join(process.cwd(), 'public', filePath)
      
      // Check if file exists
      try {
        await fs.access(absolutePath)
      } catch {
        return { 
          success: false, 
          message: 'File not found',
          error: `File does not exist: ${absolutePath}`
        }
      }
      
      // Delete the file
      await fs.unlink(absolutePath)
      
      return { 
        success: true, 
        message: `Successfully deleted local file: ${filePath}` 
      }

    } catch (error) {
      console.error('Error deleting local file:', error)
      return { 
        success: false, 
        message: 'Failed to delete local file',
        error: error instanceof Error ? error.message : 'Unknown local file error'
      }
    }
  }

  /**
   * Delete multiple audio files
   */
  static async deleteMultipleAudioFiles(audioUrls: string[]): Promise<DeletionResult[]> {
    const results: DeletionResult[] = []
    
    for (const url of audioUrls) {
      const result = await this.deleteAudioFile(url)
      results.push(result)
    }
    
    return results
  }
}
