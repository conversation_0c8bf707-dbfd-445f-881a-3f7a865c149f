"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Mic, Square, Play, Pause } from "lucide-react"
import { cn } from "@/lib/utils"
import { useToast } from "@/components/ui/use-toast"
import { useAuth } from "@/components/auth-provider"

interface AudioRecording {
  id: string;
  audio_url: string;
  duration: number;
  language: string;
  dialect: string;
  quality_score: number;
  verified: boolean;
  metadata: {
    sample_rate: number;
    format: string;
    channels: number;
  };
  source: {
    type: "direct_upload";
    direct_upload: {
      context: string;
      recording_date: string;
    };
  };
  training: {
    status: string;
    preprocessing_status: {
      normalized: boolean;
      noise_removed: boolean;
      segmented: boolean;
    };
    transcription_status: {
      auto_transcribed: boolean;
      human_verified: boolean;
      verified_by: string;
      verification_date: string;
    };
    model_training: {
      used_in_training: boolean;
      training_round: number;
      split: string;
    };
  };
  created_at: string;
  updated_at: string;
  user_id: string;
}

interface AudioRecorderProps {
  onRecordingComplete: (blob: Blob) => void
  onUploadSuccess?: (recording: AudioRecording) => void
  recordingContext: string
}

export function AudioRecorder({ onRecordingComplete, onUploadSuccess, recordingContext }: AudioRecorderProps) {
  const { user } = useAuth()
  const [isRecording, setIsRecording] = useState(false)
  const [isPaused, setIsPaused] = useState(false)
  const [recordingTime, setRecordingTime] = useState(0)
  const [audioURL, setAudioURL] = useState<string | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [mediaError, setMediaError] = useState<string | null>(null)
  const [isUploading, setUploading] = useState(false)
  const { toast } = useToast()

  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const audioChunksRef = useRef<Blob[]>([])
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const streamRef = useRef<MediaStream | null>(null)
  const lastSavedTimeRef = useRef<number>(0)

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
      if (audioURL) {
        URL.revokeObjectURL(audioURL)
      }
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop())
      }
    }
  }, [audioURL])

  const startRecording = async () => {
    try {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        setMediaError('Your browser does not support audio recording. Please use a modern browser like Chrome, Firefox, or Edge.')
        return
      }

      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      setMediaError(null)
      streamRef.current = stream
      mediaRecorderRef.current = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus',
        audioBitsPerSecond: 128000
      })
      audioChunksRef.current = []

      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data)
          // Save the current time when we get new data
          lastSavedTimeRef.current = recordingTime
        }
      }

      mediaRecorderRef.current.onstop = async () => {
        // Convert the recorded chunks to a WAV file
        const audioBlob = new Blob(audioChunksRef.current, { type: "audio/webm;codecs=opus" })
        
        // Convert to WAV format using AudioContext
        const audioContext = new AudioContext()
        const arrayBuffer = await audioBlob.arrayBuffer()
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer)
        
        // Create WAV file
        const wavBlob = await convertToWav(audioBuffer)
        const url = URL.createObjectURL(wavBlob)
        setAudioURL(url)
        onRecordingComplete(wavBlob)

        // Stop all tracks from the stream
        stream.getTracks().forEach((track) => track.stop())
        streamRef.current = null
      }

      // Request data every 1 second for autosave
      mediaRecorderRef.current.start(1000)
      setIsRecording(true)
      setIsPaused(false)
      setRecordingTime(0)
      lastSavedTimeRef.current = 0

      timerRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1)
      }, 1000)
    } catch (error) {
      console.error("Error accessing microphone:", error)
      setMediaError('Please allow microphone access in your browser settings to record audio.')
      toast({
        variant: "destructive",
        title: "Microphone Access Required",
        description: "Please allow microphone access in your browser settings to record audio."
      })
    }
  }

  const pauseRecording = () => {
    if (mediaRecorderRef.current && isRecording && !isPaused) {
      mediaRecorderRef.current.pause()
      setIsPaused(true)
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
    }
  }

  const resumeRecording = () => {
    if (mediaRecorderRef.current && isRecording && isPaused) {
      mediaRecorderRef.current.resume()
      setIsPaused(false)
      timerRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1)
      }, 1000)
    }
  }

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
      setIsRecording(false)
      setIsPaused(false)
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
    }
  }

  const togglePlayback = () => {
    if (!audioRef.current || !audioURL) return

    if (isPlaying) {
      audioRef.current.pause()
    } else {
      audioRef.current.play()
    }

    setIsPlaying(!isPlaying)
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  const handleUpload = async (audioBlob: Blob) => {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "Please sign in to upload recordings",
        variant: "destructive"
      })
      return
    }

    try {
      setUploading(true);
      
      const formData = new FormData();
      formData.append('file', audioBlob, 'recording.wav');
      formData.append('user_id', user.id);
      formData.append('recording_context', recordingContext);
      
      const response = await fetch('/api/audio/upload', {
        method: 'POST',
        body: formData,
      });
      
      const data = await response.json();
      
      if (data.success) {
        toast({
          title: "Success",
          description: "Recording uploaded successfully"
        })
        onUploadSuccess?.(data.data as AudioRecording);
      } else {
        throw new Error(data.message || 'Failed to upload recording');
      }
    } catch (error) {
      console.error('Upload error:', error);
      toast({
        title: "Error",
        description: "Failed to upload recording",
        variant: "destructive"
      })
    } finally {
      setUploading(false);
    }
  };

  if (mediaError) {
    return (
      <div className="flex flex-col items-center gap-4 p-4 border rounded-lg bg-muted">
        <p className="text-sm text-muted-foreground text-center">
          {mediaError}
        </p>
        <Button onClick={startRecording} variant="outline">
          <Mic className="mr-2 h-4 w-4" />
          Try Again
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col items-center justify-center p-4 border rounded-md">
        <div className="text-2xl font-mono mb-4">{formatTime(recordingTime)}</div>

        <div className="flex gap-4">
          {!isRecording && !audioURL && (
            <Button type="button" onClick={startRecording} className="bg-red-500 hover:bg-red-600 text-white">
              <Mic className="mr-2 h-4 w-4" />
              Start Recording
            </Button>
          )}

          {isRecording && !isPaused && (
            <>
              <Button type="button" onClick={pauseRecording} variant="outline">
                <Pause className="mr-2 h-4 w-4" />
                Pause
              </Button>
              <Button type="button" onClick={stopRecording} variant="destructive">
                <Square className="mr-2 h-4 w-4" />
                Stop Recording
              </Button>
            </>
          )}

          {isRecording && isPaused && (
            <>
              <Button type="button" onClick={resumeRecording} variant="outline">
                <Play className="mr-2 h-4 w-4" />
                Resume
              </Button>
              <Button type="button" onClick={stopRecording} variant="destructive">
                <Square className="mr-2 h-4 w-4" />
                Stop Recording
              </Button>
            </>
          )}

          {audioURL && (
            <Button type="button" onClick={togglePlayback} variant="outline">
              {isPlaying ? (
                <>
                  <Pause className="mr-2 h-4 w-4" />
                  Pause
                </>
              ) : (
                <>
                  <Play className="mr-2 h-4 w-4" />
                  Play
                </>
              )}
            </Button>
          )}

          {audioURL && (
            <Button type="button" onClick={startRecording} variant="outline">
              <Mic className="mr-2 h-4 w-4" />
              Record Again
            </Button>
          )}
        </div>
      </div>

      {audioURL && <audio ref={audioRef} src={audioURL} className="hidden" onEnded={() => setIsPlaying(false)} />}

      <div
        className={cn(
          "w-full h-12 relative rounded-md overflow-hidden",
          isRecording ? "bg-red-100 dark:bg-red-900/20" : "bg-gray-100 dark:bg-gray-800",
        )}
      >
        <div
          className={cn(
            "absolute left-0 top-0 bottom-0 transition-all",
            isRecording ? "bg-red-500/20" : "bg-primary/20",
          )}
          style={{
            width: isRecording ? `${Math.min((recordingTime / 180) * 100, 100)}%` : "100%",
          }}
        />
        <div className="absolute inset-0 flex items-center justify-center text-sm text-muted-foreground">
          {isRecording 
            ? isPaused 
              ? "Recording paused..." 
              : "Recording in progress..."
            : audioURL 
              ? "Recording complete" 
              : "Ready to record"}
        </div>
      </div>
    </div>
  )
}

async function convertToWav(audioBuffer: AudioBuffer): Promise<Blob> {
  const numChannels = audioBuffer.numberOfChannels
  const sampleRate = audioBuffer.sampleRate
  const format = 1 // PCM
  const bitDepth = 16

  const bytesPerSample = bitDepth / 8
  const blockAlign = numChannels * bytesPerSample
  const byteRate = sampleRate * blockAlign
  const dataSize = audioBuffer.length * blockAlign
  const buffer = new ArrayBuffer(44 + dataSize)
  const view = new DataView(buffer)

  // RIFF identifier
  writeString(view, 0, 'RIFF')
  // RIFF chunk length
  view.setUint32(4, 36 + dataSize, true)
  // RIFF type
  writeString(view, 8, 'WAVE')
  // format chunk identifier
  writeString(view, 12, 'fmt ')
  // format chunk length
  view.setUint32(16, 16, true)
  // sample format (raw)
  view.setUint16(20, format, true)
  // channel count
  view.setUint16(22, numChannels, true)
  // sample rate
  view.setUint32(24, sampleRate, true)
  // byte rate (sample rate * block align)
  view.setUint32(28, byteRate, true)
  // block align (channel count * bytes per sample)
  view.setUint16(32, blockAlign, true)
  // bits per sample
  view.setUint16(34, bitDepth, true)
  // data chunk identifier
  writeString(view, 36, 'data')
  // data chunk length
  view.setUint32(40, dataSize, true)

  // Write the PCM samples
  const offset = 44
  const channelData = []
  for (let i = 0; i < numChannels; i++) {
    channelData.push(audioBuffer.getChannelData(i))
  }

  let pos = 0
  while (pos < audioBuffer.length) {
    for (let i = 0; i < numChannels; i++) {
      const sample = Math.max(-1, Math.min(1, channelData[i][pos]))
      const value = sample < 0 ? sample * 0x8000 : sample * 0x7FFF
      view.setInt16(offset + pos * blockAlign + i * bytesPerSample, value, true)
    }
    pos++
  }

  return new Blob([buffer], { type: 'audio/wav' })
}

function writeString(view: DataView, offset: number, string: string) {
  for (let i = 0; i < string.length; i++) {
    view.setUint8(offset + i, string.charCodeAt(i))
  }
}
