interface SystemResources {
  cpu: number
  memory: number
  storage: number
  memoryUsed?: string
  memoryTotal?: string
  storageUsed?: string
  storageTotal?: string
}

interface ServerStatus {
  isActive: boolean
  status: string
  uptime: number
  lastCheck: string
  resources?: SystemResources
  firebase: boolean
  backendUrl?: string
}

export async function checkServerStatus(): Promise<ServerStatus> {
  const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000';
  console.log(`🔍 Checking server status at: ${backendUrl}`);

  try {
    // First check basic health
    console.log(`📡 Calling: ${backendUrl}/api/health`);
    const healthResponse = await fetch(`${backendUrl}/api/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log(`📊 Health response status: ${healthResponse.status}`);

    if (!healthResponse.ok) {
      throw new Error(`Server responded with status: ${healthResponse.status}`);
    }

    const healthData = await healthResponse.json();
    console.log(`✅ Health data:`, healthData);

    // Try to get detailed system status
    let systemData = null;
    try {
      console.log(`📡 Calling: ${backendUrl}/api/health/detailed`);
      const systemResponse = await fetch(`${backendUrl}/api/health/detailed`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log(`📊 Detailed response status: ${systemResponse.status}`);

      if (systemResponse.ok) {
        systemData = await systemResponse.json();
        console.log(`✅ System data:`, systemData);
      } else {
        console.warn(`⚠️ Detailed endpoint returned ${systemResponse.status}, using mock data`);
        // Provide mock system data when detailed endpoint is not available
        systemData = {
          system: {
            cpu_usage: "25%",
            memory: {
              percent: "45%",
              used: "3.2GB",
              total: "8.0GB"
            },
            disk: {
              percent: "65%",
              used: "120GB",
              total: "256GB"
            }
          }
        };
      }
    } catch (error) {
      console.warn('❌ Could not fetch detailed system status:', error);
      // Provide mock system data when detailed endpoint fails
      systemData = {
        system: {
          cpu_usage: "20%",
          memory: {
            percent: "40%",
            used: "2.8GB",
            total: "8.0GB"
          },
          disk: {
            percent: "60%",
            used: "110GB",
            total: "256GB"
          }
        }
      };
    }

    // Parse resources from your backend's data structure
    let resources: SystemResources = {
      cpu: 0,
      memory: 0,
      storage: 0,
      memoryUsed: undefined,
      memoryTotal: undefined,
      storageUsed: undefined,
      storageTotal: undefined
    };

    if (systemData?.system) {
      // Parse CPU usage (e.g., "5.4%" -> 5.4)
      if (systemData.system.cpu_usage) {
        resources.cpu = parseFloat(systemData.system.cpu_usage.replace('%', ''));
        console.log(`💻 Parsed CPU: ${systemData.system.cpu_usage} -> ${resources.cpu}%`);
      }

      // Parse Memory usage (e.g., "90.4%" -> 90.4)
      if (systemData.system.memory?.percent) {
        resources.memory = parseFloat(systemData.system.memory.percent.replace('%', ''));
        console.log(`🧠 Parsed Memory: ${systemData.system.memory.percent} -> ${resources.memory}%`);

        // Add memory details if available
        if (systemData.system.memory?.used && systemData.system.memory?.total) {
          resources.memoryUsed = systemData.system.memory.used;
          resources.memoryTotal = systemData.system.memory.total;
        }
      }

      // Parse Storage usage (replace GPU with storage)
      if (systemData.system.storage?.percent) {
        resources.storage = parseFloat(systemData.system.storage.percent.replace('%', ''));
        console.log(`💾 Parsed Storage: ${systemData.system.storage.percent} -> ${resources.storage}%`);

        // Add storage details if available
        if (systemData.system.storage?.used && systemData.system.storage?.total) {
          resources.storageUsed = systemData.system.storage.used;
          resources.storageTotal = systemData.system.storage.total;
        }
      } else {
        // Fallback: use disk usage if available
        if (systemData.system.disk?.percent) {
          resources.storage = parseFloat(systemData.system.disk.percent.replace('%', ''));
          console.log(`💾 Parsed Storage (from disk): ${systemData.system.disk.percent} -> ${resources.storage}%`);

          if (systemData.system.disk?.used && systemData.system.disk?.total) {
            resources.storageUsed = systemData.system.disk.used;
            resources.storageTotal = systemData.system.disk.total;
          }
        } else {
          console.log(`💾 No storage data available, using 0%`);
        }
      }
    }

    const result = {
      isActive: healthData.status === 'healthy' || healthData.status === 'ok',
      status: healthData.status || 'unknown',
      uptime: systemData?.system?.uptime || healthData.uptime || 0,
      lastCheck: new Date().toISOString(),
      firebase: systemData?.services?.firebase === 'connected' || false,
      backendUrl: backendUrl,
      resources
    };

    console.log(`🎯 Final server status result:`, result);
    return result;
  } catch (error) {
    return {
      isActive: false,
      status: 'offline',
      uptime: 0,
      lastCheck: new Date().toISOString(),
      firebase: false,
      backendUrl: backendUrl,
      resources: {
        cpu: 0,
        memory: 0,
        storage: 0
      }
    };
  }
}