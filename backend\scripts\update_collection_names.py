#!/usr/bin/env python3
"""
Update Collection Names

This script updates all references from _v2 collections to clean names
and runs the complete migration for ALL records.
"""

import os
import re

def update_file_references(file_path, replacements):
    """Update collection name references in a file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        for old_name, new_name in replacements.items():
            content = content.replace(old_name, new_name)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Updated {file_path}")
            return True
        else:
            print(f"ℹ️ No changes needed in {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ Error updating {file_path}: {e}")
        return False

def main():
    """Main function to update all collection references"""
    print("🔄 Updating Collection Names from _v2 to Clean Names")
    print("=" * 60)
    
    # Define replacements
    replacements = {
        "users_v2": "users_new",
        "audio_v2": "audio_new"
    }
    
    # Files to update
    files_to_update = [
        "api/routes/users_v2.py",
        "api/routes/audio_v2.py",
        "scripts/test_unified_training_logic.py"
    ]
    
    updated_files = 0
    
    for file_path in files_to_update:
        if os.path.exists(file_path):
            if update_file_references(file_path, replacements):
                updated_files += 1
        else:
            print(f"⚠️ File not found: {file_path}")
    
    print(f"\n✅ Updated {updated_files} files")
    print("📝 Collection name changes:")
    for old_name, new_name in replacements.items():
        print(f"   - {old_name} → {new_name}")

if __name__ == "__main__":
    main()
