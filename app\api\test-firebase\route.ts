import { NextRequest, NextResponse } from 'next/server'
import { doc, setDoc, getDoc } from 'firebase/firestore'
import { db } from '@/lib/firebase'

export async function GET() {
  try {
    console.log('Testing Firebase connection...')
    
    // Test 1: Try to read a document
    const testDoc = await getDoc(doc(db, 'users', 'test'))
    console.log('Read test completed, exists:', testDoc.exists())
    
    return NextResponse.json({
      success: true,
      message: 'Firebase read test successful',
      docExists: testDoc.exists()
    })
  } catch (error) {
    console.error('Firebase read test failed:', error)
    return NextResponse.json({
      success: false,
      error: error.message,
      step: 'read_test'
    }, { status: 500 })
  }
}

export async function POST() {
  try {
    console.log('Testing Firebase write...')
    
    // Test 2: Try to write a document
    const testId = `test_${Date.now()}`
    await setDoc(doc(db, 'test_collection', testId), {
      message: 'Test write operation',
      timestamp: new Date().toISOString()
    })
    
    console.log('Write test completed successfully')
    
    return NextResponse.json({
      success: true,
      message: 'Firebase write test successful',
      testId: testId
    })
  } catch (error) {
    console.error('Firebase write test failed:', error)
    return NextResponse.json({
      success: false,
      error: error.message,
      step: 'write_test'
    }, { status: 500 })
  }
}
