"use client"

import React, { useState } from 'react'
import { useAuth } from "@/components/auth-provider"
import { 
  createAudioWithTranscription, 
  listUserAudio, 
  getPendingAudioRecords,
  getUserWithSubcollections 
} from "@/lib/firebase-service"
import { But<PERSON> } from "@/components/ui/button"

export default function TestFirebasePage() {
  const { user } = useAuth()
  const [results, setResults] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const testCreateAudio = async () => {
    if (!user) return
    
    setLoading(true)
    try {
      const result = await createAudioWithTranscription({
        title: "Test Audio",
        duration: 30,
        format: "audio/wav",
        user_id: user.id,
        source: "test",
        audio_url: "https://example.com/test.wav",
        transcription: {
          content: "Test transcription",
          language: "masalit",
          transcription_source: "manual",
          type: "txt"
        },
        metadata: {
          gender: "male",
          language: "masalit",
          recording_context: "test"
        }
      })
      setResults({ type: 'create', data: result })
    } catch (error) {
      console.error('Test create error:', error)
      setResults({ type: 'error', error: error.message })
    }
    setLoading(false)
  }

  const testListAudio = async () => {
    if (!user) return
    
    setLoading(true)
    try {
      const result = await listUserAudio(user.id, { limit: 10 })
      setResults({ type: 'list', data: result })
    } catch (error) {
      console.error('Test list error:', error)
      setResults({ type: 'error', error: error.message })
    }
    setLoading(false)
  }

  const testPendingAudio = async () => {
    setLoading(true)
    try {
      const result = await getPendingAudioRecords({ limit: 10 })
      setResults({ type: 'pending', data: result })
    } catch (error) {
      console.error('Test pending error:', error)
      setResults({ type: 'error', error: error.message })
    }
    setLoading(false)
  }

  const testGetUser = async () => {
    if (!user) return
    
    setLoading(true)
    try {
      const result = await getUserWithSubcollections(user.id)
      setResults({ type: 'user', data: result })
    } catch (error) {
      console.error('Test user error:', error)
      setResults({ type: 'error', error: error.message })
    }
    setLoading(false)
  }

  if (!user) {
    return <div>Please log in to test Firebase functions</div>
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">Firebase Service Test</h1>
      
      <div className="space-y-4 mb-8">
        <Button onClick={testCreateAudio} disabled={loading}>
          Test Create Audio
        </Button>
        <Button onClick={testListAudio} disabled={loading}>
          Test List User Audio
        </Button>
        <Button onClick={testPendingAudio} disabled={loading}>
          Test Get Pending Audio
        </Button>
        <Button onClick={testGetUser} disabled={loading}>
          Test Get User
        </Button>
      </div>

      {loading && <div>Loading...</div>}

      {results && (
        <div className="mt-8">
          <h2 className="text-xl font-semibold mb-4">Results:</h2>
          <pre className="bg-gray-100 p-4 rounded overflow-auto">
            {JSON.stringify(results, null, 2)}
          </pre>
        </div>
      )}
    </div>
  )
}
