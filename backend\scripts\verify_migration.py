#!/usr/bin/env python3
"""
Verify Firebase Migration Results

This script checks the new clean Firebase structure to verify the migration was successful.
"""

import sys
import os
from datetime import datetime

# Add backend to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.firebase import firebase_service
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def verify_migration():
    """Verify the migration results"""
    try:
        firebase_service.initialize()
        db = firebase_service.db
        
        print("🔍 Firebase Migration Verification")
        print("=" * 50)
        
        # 1. Check new settings structure
        print("\n📁 Settings Collection:")
        settings_docs = db.collection('settings').get()
        for doc in settings_docs:
            print(f"  ✅ settings/{doc.id}")
            data = doc.to_dict()
            print(f"     - Created: {data.get('migrated_at', data.get('created_at', 'Unknown'))}")
            if 'migrated_from' in data:
                print(f"     - Migrated from: {data['migrated_from']}")
        
        # 2. Check new validation structure
        print("\n📁 Validation Collection:")
        validation_docs = db.collection('validation').get()
        for doc in validation_docs:
            print(f"  ✅ validation/{doc.id}")
            data = doc.to_dict()
            print(f"     - Model: {data.get('model_id')}")
            print(f"     - Status: {data.get('status')}")
            print(f"     - Type: {data.get('model_type')}")
            
            # Check subcollections
            subcollections = ['config', 'results', 'error_analysis']
            for subcol in subcollections:
                subdocs = doc.reference.collection(subcol).get()
                if subdocs:
                    print(f"     - {subcol}/: {len(subdocs)} documents")
        
        # 3. Check new training structure
        print("\n📁 Training Collection:")
        training_docs = db.collection('training').get()
        for doc in training_docs:
            print(f"  ✅ training/{doc.id}")
            data = doc.to_dict()
            print(f"     - Model Type: {data.get('model_type')}")
            print(f"     - Status: {data.get('status')}")
            print(f"     - Session: {data.get('session_id')}")
            
            # Check subcollections
            subcollections = ['config', 'results', 'metrics']
            for subcol in subcollections:
                subdocs = doc.reference.collection(subcol).get()
                if subdocs:
                    print(f"     - {subcol}/: {len(subdocs)} documents")
        
        # 4. Check ASR analytics
        print("\n📁 Analytics Collection:")
        analytics_docs = db.collection('analytics').get()
        for doc in analytics_docs:
            print(f"  ✅ analytics/{doc.id}")
            data = doc.to_dict()
            print(f"     - Last Updated: {data.get('last_updated')}")
            print(f"     - Training Sessions: {data.get('total_training_sessions', 0)}")
            print(f"     - Validation Runs: {data.get('total_validation_runs', 0)}")
        
        # 5. Check migration backup
        print("\n📁 Migration Backup:")
        backup_docs = db.collection('_migration_backup').get()
        for doc in backup_docs:
            print(f"  ✅ _migration_backup/{doc.id}")
            data = doc.to_dict()
            print(f"     - Created: {data.get('backup_created_at')}")
            print(f"     - Collections backed up: {len(data.get('collections_backed_up', []))}")
        
        # 6. Check migration logs
        print("\n📁 Migration Logs:")
        log_docs = db.collection('_migration_logs').get()
        for doc in log_docs:
            print(f"  ✅ _migration_logs/{doc.id}")
            data = doc.to_dict()
            print(f"     - Completed: {data.get('migration_completed_at')}")
            print(f"     - Total Actions: {data.get('total_actions', 0)}")
        
        # 7. Verify old collections still exist (safety check)
        print("\n🛡️ Original Collections (Safety Check):")
        old_collections = [
            'training_settings', 'validation_settings', 'training_schedules',
            'validation_results', 'training_status', 'training_history', 'training_metrics'
        ]
        
        for collection_name in old_collections:
            try:
                docs = db.collection(collection_name).limit(1).get()
                if docs:
                    print(f"  ✅ {collection_name} (preserved)")
                else:
                    print(f"  ⚠️ {collection_name} (empty)")
            except Exception as e:
                print(f"  ❌ {collection_name} (error: {e})")
        
        # 8. Verify core collections untouched
        print("\n🔒 Core Collections (Should be Untouched):")
        core_collections = ['audio', 'transcription', 'models', 'users']
        
        for collection_name in core_collections:
            try:
                docs = db.collection(collection_name).limit(1).get()
                if docs:
                    print(f"  ✅ {collection_name} (safe)")
                else:
                    print(f"  ℹ️ {collection_name} (empty)")
            except Exception as e:
                print(f"  ❌ {collection_name} (error: {e})")
        
        print("\n" + "=" * 50)
        print("✅ Migration verification completed!")
        print("🎉 New clean structure is ready to use!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Verification failed: {e}")
        return False

def show_api_improvements():
    """Show the API improvements enabled by the clean structure"""
    print("\n🚀 API Improvements Enabled:")
    print("=" * 50)
    
    print("\n📊 Unified Settings API:")
    print("  GET /api/settings/asr_training")
    print("  GET /api/settings/asr_validation") 
    print("  PUT /api/settings/asr_training")
    print("  PUT /api/settings/asr_validation")
    
    print("\n🔍 Hierarchical Validation API:")
    print("  GET /api/validation/{validation_id}")
    print("  GET /api/validation/{validation_id}/progress")
    print("  GET /api/validation/{validation_id}/results")
    print("  GET /api/validation/{validation_id}/error_analysis")
    
    print("\n🏋️ Hierarchical Training API:")
    print("  GET /api/training/{session_id}")
    print("  GET /api/training/{session_id}/progress")
    print("  GET /api/training/{session_id}/results")
    print("  GET /api/training/{session_id}/metrics")
    
    print("\n📈 ASR Analytics API:")
    print("  GET /api/analytics/asr")
    print("  GET /api/analytics/asr/dashboard")

def main():
    """Main verification function"""
    print("🔍 Verifying Firebase Migration Results...")
    
    success = verify_migration()
    
    if success:
        show_api_improvements()
        print("\n✅ Migration verification successful!")
        print("💡 You can now start updating your code to use the new clean structure.")
    else:
        print("\n❌ Migration verification failed!")
        print("🔧 Check the logs above for details.")

if __name__ == "__main__":
    main()
