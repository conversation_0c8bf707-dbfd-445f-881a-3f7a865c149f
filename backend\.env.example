# Backend Configuration Template
# Copy this to backend/.env and fill in your actual values

# API Configuration
API_HOST=127.0.0.1
API_PORT=8000
DEBUG=false

# Firebase Configuration (Backend Service Account)
FIREBASE_PROJECT_ID=kana-masark-255aa
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_FIREBASE_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n"
FIREBASE_STORAGE_BUCKET=kana-masark-255aa.firebasestorage.app
FIREBASE_PRIVATE_KEY_ID=your_firebase_private_key_id
FIREBASE_CLIENT_ID=your_firebase_client_id
FIREBASE_CLIENT_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40kana-masark-255aa.iam.gserviceaccount.com

# Google Cloud Storage Configuration
GCS_PROJECT_ID=kana-masark-255aa
GCS_PRIVATE_KEY_ID=your_gcs_private_key_id
GCS_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_GCS_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n"
GCS_CLIENT_EMAIL=<EMAIL>
GCS_CLIENT_ID=your_gcs_client_id
GCS_CLIENT_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/model-uploader%40kana-masark-255aa.iam.gserviceaccount.com
GCS_BUCKET_NAME=masalit-asr-models

# Model Storage Paths
CHECKPOINT_DIR=./ai_models/checkpoints
FINAL_MODEL_DIR=./ai_models/trained
TEMP_DIR=./temp

# Audio Processing Settings
MAX_AUDIO_FILE_SIZE=********
MAX_AUDIO_DURATION=300
SUPPORTED_AUDIO_FORMATS=wav,mp3,m4a,ogg,webm,flac
TARGET_SAMPLE_RATE=16000
TARGET_CHANNELS=1

# Training Configuration
DEFAULT_MODEL_NAME=openai/whisper-small
DEFAULT_EPOCHS=5
DEFAULT_LEARNING_RATE=0.001
DEFAULT_BATCH_SIZE=8
DEFAULT_VALIDATION_SPLIT=0.2

# Model Upload Configuration
UPLOAD_TO_GCS=true

# Training Timeout Configuration
DEFAULT_TRAINING_TIMEOUT=7200
MAX_TRAINING_TIMEOUT=14400
