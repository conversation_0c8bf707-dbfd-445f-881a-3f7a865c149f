# Masalit Platform - Backend

## Overview
The backend of the Masalit Platform provides the core functionality for Automatic Speech Recognition (ASR) and Text-to-Speech (TTS) services. The current implementation is a monolithic Python application (`asr.py`) that integrates with FastAPI for API endpoints, Firebase for data storage, and Google Cloud Storage for model management.

## Current Project Structure

### Current Implementation Structure
```
masalit-ai/
├── backend/
│   ├── main.py              # FastAPI application entry point
│   ├── gunicorn.conf.py     # Production server configuration
│   ├── requirements.txt     # Backend dependencies
│   ├── README.md           # This documentation
│   │
│   ├── api/                # API Layer
│   │   ├── __init__.py
│   │   ├── routes/
│   │   │   ├── __init__.py
│   │   │   ├── asr.py      # ASR API endpoints
│   │   │   ├── tts.py      # TTS API endpoints (future)
│   │   │   ├── health.py   # Health check endpoints
│   │   │   └── system.py   # System monitoring endpoints
│   │   └── schemas/
│   │       ├── __init__.py
│   │       ├── asr.py      # ASR Pydantic models
│   │       ├── tts.py      # TTS Pydantic models (future)
│   │       ├── audio.py    # Audio & transcription models
│   │       └── system.py   # System monitoring models
│   │
│   ├── core/               # Core Business Logic
│   │   ├── __init__.py
│   │   ├── asr/           # ASR Core Module
│   │   │   ├── __init__.py
│   │   │   ├── engine.py   # Main ASR engine (WhisperASR class)
│   │   │   ├── trainer.py  # Training pipeline
│   │   │   └── processor.py # Audio processing
│   │   └── tts/           # TTS Core Module (future)
│   │       ├── __init__.py
│   │       ├── engine.py   # TTS engine
│   │       └── trainer.py  # TTS training
│   │
│   ├── models/            # Model Management
│   │   ├── __init__.py
│   │   ├── manager.py     # Model lifecycle management
│   │   ├── validator.py   # Model validation
│   │   ├── versioning.py  # Model versioning
│   │   └── storage.py     # Model storage (local + GCS)
│   │
│   ├── services/          # External Service Integrations
│   │   ├── __init__.py
│   │   ├── firebase.py    # Firebase/Firestore service
│   │   ├── gcs.py        # Google Cloud Storage service
│   │   └── audio.py      # Audio processing service
│   │
│   ├── config/           # Configuration Management
│   │   ├── __init__.py
│   │   └── settings.py   # Application settings
│   │
│   └── utils/            # Utility Functions
│       ├── __init__.py
│       ├── helpers.py    # General helpers
│       ├── audio.py     # Audio utilities
│       └── validation.py # Validation utilities
│
├── requirements.txt      # Root Python dependencies (legacy)
├── render.yaml          # Render.com deployment configuration
└── Procfile            # Process configuration for deployment
```

### Component Organization

#### **1. ASR (Automatic Speech Recognition)**
- **Location**: `backend/core/asr/`
- **Main Engine**: `engine.py` - Contains the WhisperASR class with full ASR functionality
- **Training**: `trainer.py` - Handles model training pipeline and background training
- **Processing**: `processor.py` - Audio preprocessing, format conversion, and validation
- **API**: `backend/api/routes/asr.py` - REST endpoints for training, transcription, and model management
- **Features**:
  - Real-time training with progress tracking
  - Audio preprocessing and normalization
  - Model saving to local storage and GCS
  - Training status monitoring via Firestore

#### **2. TTS (Text-to-Speech)** *(Future Implementation)*
- **Location**: `backend/core/tts/`
- **Main Engine**: `engine.py` - TTS engine implementation
- **Training**: `trainer.py` - TTS model training
- **API**: `backend/api/routes/tts.py` - REST endpoints

#### **3. Model Management**
- **Location**: `backend/models/`
- **Manager**: `manager.py` - Complete model lifecycle management
  - Model creation, loading, saving, deletion
  - Local and GCS storage integration
  - Model comparison and cleanup
- **Validator**: `validator.py` - Comprehensive model validation
  - Model structure validation
  - Performance testing and benchmarking
  - Quality assurance checks
- **Versioning**: `versioning.py` - Advanced version control
  - Semantic versioning (major.minor.patch)
  - Version history and metadata tracking
  - Model tagging and rollback capabilities
- **Storage**: `storage.py` - Unified storage management
  - Local file operations
  - GCS upload/download with compression
  - Backup and sync operations

#### **4. Model Validation**
- **Location**: `backend/models/validator.py`
- **Validation Types**:
  - **Structure Validation**: Model architecture and configuration checks
  - **Loading Validation**: File integrity and loading capability tests
  - **Inference Validation**: End-to-end inference testing with dummy data
  - **Performance Validation**: Accuracy, WER, CER metrics on test datasets
  - **Benchmark Testing**: Standardized performance comparisons
- **Quality Metrics**:
  - Word Error Rate (WER)
  - Character Error Rate (CER)
  - Inference speed and resource usage
  - Model confidence scores

## Features

### ASR (Automatic Speech Recognition)
- Fine-tuned Whisper model (v3) for Masalit language support
- Real-time speech-to-text conversion
- Batch processing capabilities
- Training and fine-tuning support
- Progress tracking and status monitoring
- Model versioning and checkpointing
- Integration with Firebase for data storage
- Google Cloud Storage support for model management
- Model training and validation
- Model management and comparison
- Real-time transcription status updates
- Local model training and fine-tuning
- Continuous training support with model versioning
- Support for multiple audio formats (WAV, MP3, M4A)
- Automatic audio preprocessing and normalization
- Language-specific fine-tuning capabilities

### TTS (Text-to-Speech)
- High-quality speech synthesis using VITS architecture
- Support for Masalit language
- Batch processing capabilities
- Customizable voice parameters
- Progress tracking
- Integration with Firebase
- Google Cloud Storage support
- Voice model management
- Training and validation capabilities
- Real-time synthesis status updates
- Local model training and fine-tuning
- Continuous training support with model versioning
- Voice cloning capabilities
- Audio quality: 24kHz, 16-bit PCM
- Support for SSML markup

## Prerequisites

### System Requirements
- Python 3.8 or higher
- CUDA-capable GPU with at least 8GB VRAM (recommended for training)
- Minimum 16GB RAM (32GB recommended for training)
- FFmpeg installed and available in PATH
- Sufficient disk space for model storage (minimum 50GB)
- Internet connection for API access
- Firebase account
- Google Cloud Storage account

### Required Environment Variables
```env
# Firebase Configuration
FIREBASE_API_KEY=your_api_key
FIREBASE_AUTH_DOMAIN=your_auth_domain
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_PRIVATE_KEY=your_private_key
FIREBASE_CLIENT_EMAIL=your_client_email
FIREBASE_STORAGE_BUCKET=your_storage_bucket
FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
FIREBASE_APP_ID=your_app_id
FIREBASE_MEASUREMENT_ID=your_measurement_id

# Google Cloud Storage Configuration
GCS_PROJECT_ID=your_gcs_project_id
GCS_PRIVATE_KEY=your_gcs_private_key
GCS_CLIENT_EMAIL=your_gcs_client_email
GCS_BUCKET_NAME=your_bucket_name
GCS_CREDENTIALS_PATH=path/to/credentials.json

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4
API_TIMEOUT=300
```

## Installation

### Current Setup (Monolithic)

1. Navigate to the project root:
```bash
cd masalit-ai
```

2. Create and activate a virtual environment:
```bash
# Create virtual environment in backend directory
python -m venv backend/venv

# Activate virtual environment
# On Windows:
backend\venv\Scripts\activate
# On macOS/Linux:
source backend/venv/bin/activate
```

3. Install dependencies:
```bash
# Install from root requirements.txt (contains core dependencies)
pip install -r requirements.txt

# Or install from backend-specific requirements.txt (more comprehensive)
pip install -r backend/requirements.txt
```

4. Set up environment variables:
Create a `.env` file in the project root with the following variables:
```env
# Firebase Configuration
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_PRIVATE_KEY=your_private_key
FIREBASE_CLIENT_EMAIL=your_client_email
FIREBASE_PRIVATE_KEY_ID=your_private_key_id
FIREBASE_CLIENT_ID=your_client_id
FIREBASE_CLIENT_CERT_URL=your_client_cert_url

# Google Cloud Storage Configuration
GCS_PROJECT_ID=your_gcs_project_id
GCS_PRIVATE_KEY=your_gcs_private_key
GCS_CLIENT_EMAIL=your_gcs_client_email
GCS_PRIVATE_KEY_ID=your_gcs_private_key_id
GCS_CLIENT_ID=your_gcs_client_id
GCS_CLIENT_CERT_URL=your_gcs_client_cert_url
GCS_BUCKET_NAME=your_bucket_name
```

5. Place Firebase/GCS credentials:
- Place your service account key as `model-uploader.json` in the project root
- Or configure all credentials via environment variables

## Running the Server

### Development Mode
Start the ASR server directly:
```bash
# Ensure virtual environment is activated
python asr.py
```

### Production Mode (Future)
When the modular structure is implemented:
```bash
cd backend
uvicorn main:app --host 0.0.0.0 --port 8000
```

### With Gunicorn (Production)
```bash
cd backend
gunicorn main:app -c gunicorn.conf.py
```

The API will be available at:
- API: http://localhost:8000
- Health Check: http://localhost:8000/api/health
- Frontend Dashboard: http://localhost:3000/dashboard/ai

## API Endpoints

### Current Implementation (asr.py)
The current monolithic implementation provides the following functionality through the WhisperASR class:

#### Core ASR Methods
- `transcribe(audio_path)`: Transcribe audio using the trained model
- `transcribe_audio(audio_path)`: Async transcription method
- `train_model(training_data)`: Start model training with custom data
- `get_training_status()`: Get current training status and progress
- `stop_training()`: Stop the training process

#### Model Management Methods
- `list_models(model_type, limit)`: List available models in GCS
- `save_and_upload_model(model_dir, gcs_path)`: Save and upload model to GCS
- `download_from_gcs(gcs_path, local_path)`: Download model from GCS
- `compare_models(model1_path, model2_path)`: Compare two model versions
- `cleanup_old_models(days_to_keep, keep_tagged)`: Clean up old models
- `tag_model(model_path, tags)`: Tag a model version

#### System Monitoring Methods
- `get_system_status()`: Get system health and resource metrics
- `get_gpu_info()`: Get GPU utilization information
- `get_training_metrics()`: Get training progress and metrics

### Future API Endpoints (Planned)
When the FastAPI integration is complete, the following REST endpoints will be available:

#### ASR Endpoints
- `POST /api/asr/train`: Start model training
- `POST /api/asr/transcribe`: Transcribe audio file
- `GET /api/asr/status`: Get training status and progress
- `POST /api/asr/stop`: Stop training process
- `GET /api/asr/models`: List available models
- `GET /api/asr/models/{model_id}`: Get specific model details
- `POST /api/asr/models/compare`: Compare two models
- `POST /api/asr/models/tag`: Tag a model version
- `POST /api/asr/models/cleanup`: Clean up old models
- `POST /api/asr/models/upload`: Upload model to GCS
- `POST /api/asr/models/download`: Download model from GCS

#### System Endpoints
- `GET /api/health`: Get system health status and resource metrics
  - Returns server status, uptime, and resource usage (CPU, memory, GPU)
  - Used by the frontend dashboard for monitoring
- `GET /api/system/status`: Detailed system information
- `GET /api/system/metrics`: Performance metrics and statistics

#### TTS Endpoints (Future)
- `POST /api/tts/generate`: Generate speech from text
- `GET /api/tts/status`: Get generation status
- `GET /api/tts/models`: List available TTS models
- `GET /api/tts/models/{model_id}`: Get TTS model details
- `POST /api/tts/models/compare`: Compare two TTS models
- `POST /api/tts/models/tag`: Tag a TTS model
- `POST /api/tts/models/cleanup`: Clean up old TTS models

## Model Management

### ASR Models
- Based on OpenAI's Whisper v3 architecture
- Models are stored in the `models` directory
- Checkpoints are saved after each training epoch
- Final models are stored in the `final_models` directory
- Models can be uploaded to and downloaded from Google Cloud Storage
- Continuous training support with automatic model versioning
- Model checkpoints are automatically backed up to GCS
- Training metrics and logs are stored in Firebase
- Local training with GPU acceleration
- Automatic model evaluation and validation
- Memory-efficient training with gradient checkpointing
- Support for mixed precision training (FP16)

### TTS Models
- Based on VITS (Variational Inference with adversarial learning for end-to-end Text-to-Speech)
- Voice models are stored in the `models/tts` directory
- Custom voice parameters can be saved and loaded
- Models can be uploaded to and downloaded from Google Cloud Storage
- Continuous training support with automatic model versioning
- Model checkpoints are automatically backed up to GCS
- Training metrics and logs are stored in Firebase
- Local training with GPU acceleration
- Automatic model evaluation and validation
- Multi-speaker voice cloning
- Phoneme-based text processing
- Support for prosody control

### Model Versioning
- Each model version is tagged with:
  - Version number (semantic versioning)
  - Training date
  - Performance metrics
  - Training configuration
  - Dataset version used
- Version history is maintained in Firebase
- Old versions can be archived in GCS
- Easy rollback to previous versions

### Continuous Training
- Automatic model retraining based on:
  - New data availability
  - Performance degradation
  - Scheduled intervals
- Training progress and metrics are tracked in Firebase
- Model checkpoints are saved to GCS
- Training logs and metrics are stored for analysis
- Automatic model evaluation and comparison
- Easy deployment of new model versions
- Local GPU-accelerated training
- Automatic resource management for training

## Error Handling

The backend includes comprehensive error handling:
- Detailed error logging
- Status updates during long-running operations
- Automatic cleanup of temporary files
- Graceful handling of API failures
- Automatic retry for failed operations
- Error reporting to monitoring systems

## Monitoring and Logging

- All operations are logged with appropriate severity levels
- Training progress is tracked and stored in Firebase
- System metrics (CPU, memory, GPU) are monitored
- Performance metrics are collected during training
- Model performance metrics are tracked
- API usage and performance metrics
- Error rates and types
- Resource utilization
- GPU utilization and memory usage
- Training progress and metrics
- Audio quality metrics
- Transcription accuracy metrics
- Voice similarity metrics (for TTS)
- Inference latency tracking

## Development

### Code Style
- Follow PEP 8 guidelines
- Use type hints
- Document all functions and classes
- Write unit tests for new features

### Testing
```bash
# Run tests
pytest

# Run tests with coverage
pytest --cov=.
```

### Deployment

#### Render.com Deployment (Current)
The backend is configured for deployment on Render.com:

1. **Service Configuration** (in `render.yaml`):
   ```yaml
   - type: web
     name: masalit-platform-backend
     env: python
     buildCommand: pip install -r backend/requirements.txt
     startCommand: cd backend && gunicorn main:app -c gunicorn.conf.py
     domains:
       - name: api.buragatechnologies.com
   ```

2. **Environment Variables**: Set in Render.com dashboard
   - All Firebase configuration variables
   - All Google Cloud Storage variables
   - Python version and port settings

3. **Deployment Process**:
   - Push to main branch triggers automatic deployment
   - Build process installs dependencies from `backend/requirements.txt`
   - Service starts with Gunicorn for production

#### Local Development Deployment
```bash
# Activate virtual environment
source backend/venv/bin/activate  # or backend\venv\Scripts\activate on Windows

# Run directly
python asr.py

# Or with uvicorn (when FastAPI integration is complete)
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

#### Production Deployment Considerations
- Ensure adequate GPU resources for model training
- Configure proper logging and monitoring
- Set up health checks and auto-restart policies
- Implement proper error handling and recovery
- Configure CORS for frontend integration

## Current Implementation Details

### WhisperASR Class Architecture
The main `WhisperASR` class in `asr.py` provides:

1. **Model Management**:
   - OpenAI Whisper v3 (small) as base model
   - Fine-tuning capabilities for Masalit language
   - Model versioning and checkpointing
   - Automatic model backup to Google Cloud Storage

2. **Training Pipeline**:
   - Custom data collator for speech data
   - Configurable training parameters
   - Real-time progress tracking
   - Automatic evaluation and metrics collection

3. **Audio Processing**:
   - Support for multiple audio formats (WAV, MP3, M4A)
   - Automatic audio preprocessing and normalization
   - FFmpeg integration for format conversion
   - 16kHz mono conversion for optimal processing

4. **Integration Services**:
   - Firebase/Firestore for data storage
   - Google Cloud Storage for model storage
   - Real-time status updates
   - Comprehensive error handling and logging

### Dependencies Overview
Key Python packages used:

- **AI/ML**: `torch`, `transformers`, `openai-whisper`, `datasets`
- **Audio**: `torchaudio`, `soundfile`, `librosa`
- **Web**: `fastapi`, `uvicorn`, `gunicorn`
- **Cloud**: `firebase-admin`, `google-cloud-storage`
- **Utilities**: `psutil`, `GPUtil`, `python-dotenv`

## Migration to Modular Structure

### Planned Refactoring
The current monolithic `asr.py` will be refactored into:

1. **API Layer** (`api/routes/`): FastAPI endpoints
2. **Core Logic** (`core/`): Business logic separation
3. **Services** (`services/`): External service integrations
4. **Models** (`models/`): Data models and schemas
5. **Configuration** (`config/`): Settings management
6. **Utilities** (`utils/`): Helper functions

### Benefits of Modular Structure
- Better code organization and maintainability
- Easier testing and debugging
- Improved scalability
- Better separation of concerns
- Enhanced reusability

## Troubleshooting

### Common Issues

1. **CUDA/GPU Issues**:
   ```bash
   # Check CUDA availability
   python -c "import torch; print(torch.cuda.is_available())"

   # Check GPU memory
   python -c "import torch; print(torch.cuda.get_device_properties(0))"
   ```

2. **FFmpeg Not Found**:
   ```bash
   # Install FFmpeg
   # Windows: Download from https://ffmpeg.org/
   # macOS: brew install ffmpeg
   # Ubuntu: sudo apt install ffmpeg
   ```

3. **Firebase Connection Issues**:
   - Verify credentials in environment variables
   - Check `model-uploader.json` file exists and is valid
   - Ensure Firebase project has proper permissions

4. **Google Cloud Storage Issues**:
   - Verify GCS credentials and bucket permissions
   - Check bucket exists and is accessible
   - Ensure proper IAM roles are assigned

5. **Memory Issues During Training**:
   - Reduce batch size in training configuration
   - Enable gradient checkpointing
   - Use mixed precision training (FP16)
   - Monitor GPU memory usage

### Performance Optimization

1. **GPU Utilization**:
   - Use CUDA when available
   - Enable mixed precision training
   - Optimize batch sizes for your GPU memory

2. **Model Storage**:
   - Implement model caching
   - Use compression for model uploads
   - Regular cleanup of old models

3. **Audio Processing**:
   - Batch process multiple files
   - Use efficient audio loading libraries
   - Implement proper audio caching

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/backend-improvement`)
3. Commit your changes (`git commit -m 'Add backend improvement'`)
4. Push to the branch (`git push origin feature/backend-improvement`)
5. Create a Pull Request

### Development Guidelines
- Follow PEP 8 style guidelines
- Add type hints to all functions
- Write comprehensive docstrings
- Include unit tests for new features
- Update documentation for API changes

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, please:
1. Check the [Troubleshooting](#troubleshooting) section
2. Review the current implementation in `asr.py`
3. Search existing [GitHub Issues](https://github.com/iabakar/masalit-ai/issues)
4. Create a new issue with detailed error information
5. Contact the development team

## Acknowledgments

- OpenAI for the Whisper model and transformers library
- The Masalit language community for language support
- Google Cloud Platform for storage and compute services
- Firebase for real-time database services
- All contributors to the project
- The open-source AI/ML community