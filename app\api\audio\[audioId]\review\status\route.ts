import { NextRequest, NextResponse } from 'next/server'
import { doc, getDoc, setDoc } from 'firebase/firestore'
import { db } from '@/lib/firebase'

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ audioId: string }> }
) {
  try {
    const { audioId } = await params
    const updates = await request.json()

    // Check if audio exists
    const audioDoc = await getDoc(doc(db, 'audio', audioId))
    if (!audioDoc.exists()) {
      return NextResponse.json({ error: 'Audio record not found' }, { status: 404 })
    }

    const timestamp = new Date().toISOString()

    // Update review status
    await setDoc(doc(db, 'audio', audioId, 'review', 'status'), {
      ...updates,
      updated_at: timestamp
    }, { merge: true })

    return NextResponse.json({
      success: true,
      message: 'Review status updated successfully'
    })

  } catch (error) {
    console.error('Error updating review status:', error)
    return NextResponse.json(
      { error: 'Failed to update review status' },
      { status: 500 }
    )
  }
}
