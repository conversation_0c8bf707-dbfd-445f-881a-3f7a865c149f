interface HistoryEntry {
  id: string
  type: 'training' | 'validation'
  timestamp: string
  model_version: string
  accuracy: number
  confidence: number
  wer?: number
  cer?: number
  ser?: number
  samples_tested?: number
  duration?: number
  epochs?: number
  samples_processed?: number
  metrics?: {
    accuracy: number
    confidence: number
  }
  config?: {
    model_type: string
    model_name: string
    epochs: number
    number_of_samples: number
  }
  error_analysis?: Array<{
    audio_id: string
    reference: string
    hypothesis: string
    metrics: {
      wer: number
      cer: number
      ser: number
      confidence: number
    }
    metadata: {
      language: string
      dialect?: string
      speaker_count: number
      transcription_source: string
    }
  }>
  summary?: {
    total_samples: number
    successful_samples: number
    failed_samples: number
    average_confidence: number
  }
  metadata?: {
    validation_type?: string
    validation_method?: string
    validation_duration?: string
  }
}

const HistoryCard: React.FC<{ entry: HistoryEntry }> = ({ entry }) => {
  const isValidation = entry.type === 'validation'
  const date = new Date(entry.timestamp)
  const formattedDate = date.toLocaleString()

  return (
    <Card className="mb-4">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-lg font-semibold">
              {isValidation ? 'Model Validation' : 'Model Training'}
            </h3>
            <p className="text-sm text-gray-500">{formattedDate}</p>
          </div>
          <Badge variant={isValidation ? 'secondary' : 'default'}>
            {isValidation ? 'Validation' : 'Training'}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium">Model Version</p>
            <p className="text-sm text-gray-600">{entry.model_version}</p>
          </div>
          <div>
            <p className="text-sm font-medium">Accuracy</p>
            <p className="text-sm text-gray-600">{(entry.accuracy * 100).toFixed(2)}%</p>
          </div>
          <div>
            <p className="text-sm font-medium">Confidence</p>
            <p className="text-sm text-gray-600">{(entry.confidence * 100).toFixed(2)}%</p>
          </div>
          {isValidation ? (
            <>
              <div>
                <p className="text-sm font-medium">WER</p>
                <p className="text-sm text-gray-600">{(entry.wer! * 100).toFixed(2)}%</p>
              </div>
              <div>
                <p className="text-sm font-medium">CER</p>
                <p className="text-sm text-gray-600">{(entry.cer! * 100).toFixed(2)}%</p>
              </div>
              <div>
                <p className="text-sm font-medium">SER</p>
                <p className="text-sm text-gray-600">{(entry.ser! * 100).toFixed(2)}%</p>
              </div>
              <div>
                <p className="text-sm font-medium">Samples Tested</p>
                <p className="text-sm text-gray-600">{entry.samples_tested}</p>
              </div>
              {entry.summary && (
                <div className="col-span-2">
                  <p className="text-sm font-medium">Summary</p>
                  <div className="grid grid-cols-3 gap-2 mt-1">
                    <div>
                      <p className="text-xs text-gray-500">Total Samples</p>
                      <p className="text-sm">{entry.summary.total_samples}</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Successful</p>
                      <p className="text-sm text-green-600">{entry.summary.successful_samples}</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Failed</p>
                      <p className="text-sm text-red-600">{entry.summary.failed_samples}</p>
                    </div>
                  </div>
                </div>
              )}
            </>
          ) : (
            <>
              <div>
                <p className="text-sm font-medium">Duration</p>
                <p className="text-sm text-gray-600">{entry.duration} seconds</p>
              </div>
              <div>
                <p className="text-sm font-medium">Epochs</p>
                <p className="text-sm text-gray-600">{entry.epochs}</p>
              </div>
              <div>
                <p className="text-sm font-medium">Samples Processed</p>
                <p className="text-sm text-gray-600">{entry.samples_processed}</p>
              </div>
              {entry.config && (
                <div className="col-span-2">
                  <p className="text-sm font-medium">Configuration</p>
                  <div className="grid grid-cols-2 gap-2 mt-1">
                    <div>
                      <p className="text-xs text-gray-500">Model Type</p>
                      <p className="text-sm">{entry.config.model_type}</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Model Name</p>
                      <p className="text-sm">{entry.config.model_name}</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Epochs</p>
                      <p className="text-sm">{entry.config.epochs}</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Batch Size</p>
                      <p className="text-sm">{entry.config.number_of_samples}</p>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
        {isValidation && entry.error_analysis && entry.error_analysis.length > 0 && (
          <div className="mt-4">
            <p className="text-sm font-medium mb-2">Error Analysis</p>
            <div className="max-h-40 overflow-y-auto">
              {entry.error_analysis.map((error, index) => (
                <div key={index} className="border rounded p-2 mb-2">
                  <p className="text-xs text-gray-500">Audio ID: {error.audio_id}</p>
                  <p className="text-xs"><span className="font-medium">Reference:</span> {error.reference}</p>
                  <p className="text-xs"><span className="font-medium">Hypothesis:</span> {error.hypothesis}</p>
                  <div className="grid grid-cols-4 gap-2 mt-1">
                    <div>
                      <p className="text-xs text-gray-500">WER</p>
                      <p className="text-xs">{(error.metrics.wer * 100).toFixed(2)}%</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">CER</p>
                      <p className="text-xs">{(error.metrics.cer * 100).toFixed(2)}%</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">SER</p>
                      <p className="text-xs">{(error.metrics.ser * 100).toFixed(2)}%</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Confidence</p>
                      <p className="text-xs">{(error.metrics.confidence * 100).toFixed(2)}%</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
} 