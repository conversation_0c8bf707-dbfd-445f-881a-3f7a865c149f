"""
ASR Training Pipeline using OpenAI Whisper-small with local fine-tuning
"""

import os
import json
import logging
import asyncio
import tempfile
import tarfile
import librosa
import numpy as np
import subprocess
import queue
import threading
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import torch
import torchaudio
from transformers import (
    WhisperProcessor,
    WhisperForConditionalGeneration,
    Trainer,
    TrainingArguments,
    EarlyStoppingCallback,
    TrainerCallback
)
from datasets import Dataset, Audio
import requests
from dataclasses import dataclass
from backend.config.settings import settings as app_settings
from backend.services.firebase_clean import clean_firebase_service
from backend.services.gcs import gcs_service
from backend.services.logging_service import log_training_event

logger = logging.getLogger(__name__)

class ASRTrainer:
    """ASR training pipeline using Whisper model"""

    def __init__(self):
        self.model = None
        self.processor = None
        self.trainer = None
        self.training_data = None
        self.is_training = False
        self.training_task = None
        self.status_queue = queue.Queue(maxsize=100)  # Limit queue size
        self.status_update_task = None
    
    async def start_training(self, training_settings: Dict[str, Any]) -> Dict[str, Any]:
        """Start ASR training with given settings"""
        try:
            if self.is_training:
                return {"error": "Training is already in progress"}

            # Add start time to training settings
            training_settings['start_time'] = datetime.now().isoformat()

            # Merge with saved ASR settings from Firestore
            try:
                clean_firebase_service.initialize()
                saved_settings = clean_firebase_service.get_settings('asr_training')
                # Merge saved settings with provided settings (provided settings take precedence)
                merged_settings = {**saved_settings, **training_settings}
                training_settings = merged_settings
                logger.info(f"Merged training settings: {training_settings}")
            except Exception as e:
                logger.warning(f"Could not load saved settings, using provided settings: {e}")

            # Update status to starting
            await self._update_status({
                "status": "starting",
                "message": "Initializing training...",
                "progress": 0
            })

            # Start status update handler
            self.status_update_task = asyncio.create_task(
                self._handle_status_updates()
            )

            # Start training in background
            self.training_task = asyncio.create_task(
                self._train_model(training_settings)
            )

            # Log training start event
            log_training_event("asr", "started", {
                "settings": training_settings,
                "timestamp": datetime.now().isoformat()
            })

            return {"message": "Training started successfully", "status": "started"}

        except Exception as e:
            logger.error(f"Error starting training: {e}")
            await self._update_status({
                "status": "failed",
                "error": str(e),
                "progress": 0
            })
            return {"error": str(e)}

    async def _handle_status_updates(self):
        """Handle status updates from the training thread"""
        try:
            logger.info("Status update handler started")
            while True:
                try:
                    # Get status update from queue with timeout
                    status_data = self.status_queue.get(timeout=1.0)
                    logger.info(f"Processing status update: {status_data.get('status', 'unknown')}")
                    await self._update_status(status_data)
                    self.status_queue.task_done()
                except queue.Empty:
                    # If training is done and queue is empty, exit
                    if not self.is_training:
                        logger.info("Training finished, checking for remaining queue items...")
                        # Wait a bit more to ensure all updates are processed
                        await asyncio.sleep(0.5)
                        if self.status_queue.empty():
                            logger.info("Queue empty, exiting status update handler")
                            break
                    continue
                except Exception as e:
                    logger.error(f"Error handling status update: {e}")
                    continue
        except asyncio.CancelledError:
            logger.info("Status update handler cancelled")
        except Exception as e:
            logger.error(f"Error in status update handler: {e}")

    async def stop_training(self) -> Dict[str, Any]:
        """Stop current training"""
        try:
            if not self.is_training:
                return {"message": "No training in progress"}

            if self.training_task:
                self.training_task.cancel()

            # Cancel status update task
            if self.status_update_task:
                self.status_update_task.cancel()
                self.status_update_task = None

            self.is_training = False

            await self._update_status({
                "status": "stopped",
                "message": "Training stopped by user",
                "progress": 0
            })

            return {"message": "Training stopped successfully"}

        except Exception as e:
            logger.error(f"Error stopping training: {e}")
            return {"error": str(e)}
    
    async def get_training_status(self) -> Dict[str, Any]:
        """Get current training status"""
        try:
            # Initialize Firebase if not already done
            clean_firebase_service.initialize()

            # Get status from new clean structure - check for active training sessions
            training_docs = clean_firebase_service.db.collection('training').where('model_type', '==', 'asr').where('status', 'in', ['training', 'preparing_data']).limit(1).get()

            if training_docs:
                # Get progress from active session
                doc = training_docs[0]
                session_id = doc.id
                progress_ref = doc.reference.collection('progress').document('current')
                progress_doc = progress_ref.get()

                if progress_doc.exists:
                    return progress_doc.to_dict()
                else:
                    # Return basic session info
                    session_data = doc.to_dict()
                    return {
                        "status": session_data.get('status', 'unknown'),
                        "progress": 0,
                        "current_epoch": 0,
                        "total_epochs": 0,
                        "message": f"Training session {session_id} in progress"
                    }
            else:
                # No active training sessions
                return {
                    "status": "not_started",
                    "progress": 0,
                    "current_epoch": 0,
                    "total_epochs": 0,
                    "message": "No training has been started yet"
                }

        except Exception as e:
            logger.error(f"Error getting training status: {e}")
            return {
                "status": "error",
                "error": str(e),
                "progress": 0,
                "current_epoch": 0,
                "total_epochs": 0
            }
    
    async def _train_model(self, training_settings: Dict[str, Any]):
        """Main training loop with real Whisper fine-tuning"""
        try:
            logger.info("=== STARTING MAIN TRAINING PIPELINE ===")
            self.is_training = True

            # Step 1: Load training data from Firestore
            await self._update_status({
                "status": "loading_data",
                "message": "Loading training data from Firestore...",
                "progress": 5
            })

            # Import old firebase service for training data (will update this later)
            from backend.services.firebase import firebase_service
            firebase_service.initialize()
            training_data = firebase_service.get_training_data()
            if not training_data:
                raise Exception("No training data available")

            logger.info(f"Retrieved {len(training_data)} samples from Firebase")

            # Limit samples based on settings
            max_samples = training_settings.get('number_of_samples', 100)

            # Ensure max_samples is a valid integer
            try:
                max_samples = int(max_samples)
                if max_samples <= 0:
                    logger.warning(f"Invalid number_of_samples: {max_samples}, using default 100")
                    max_samples = 100
            except (ValueError, TypeError):
                logger.warning(f"Invalid number_of_samples type: {max_samples}, using default 100")
                max_samples = 100

            training_data = training_data[:max_samples]

            logger.info(f"Limited to {len(training_data)} training samples (max: {max_samples})")

            # Log the audio IDs for debugging
            audio_ids_debug = [item.get('audio_id', 'unknown') for item in training_data]
            logger.info(f"Training data audio IDs: {audio_ids_debug}")

            # Step 2: Load Whisper model and processor
            await self._update_status({
                "status": "loading_model",
                "message": "Loading Whisper-small model...",
                "progress": 15
            })

            await self._load_model(training_settings)

            # Step 3: Download and prepare audio data
            await self._update_status({
                "status": "preparing_data",
                "message": "Downloading and processing audio files...",
                "progress": 25,
                "samples_processed": 0,
                "total_samples": len(training_data)
            })

            dataset = await self._prepare_dataset(training_data, training_settings)

            # Step 4: Setup trainer
            await self._update_status({
                "status": "setting_up",
                "message": "Setting up training pipeline...",
                "progress": 40
            })

            trainer = await self._setup_trainer(dataset, training_settings)

            # Step 5: Start actual training
            # Get validated epochs for status updates
            epochs_for_status = training_settings.get('epochs', 5)
            try:
                epochs_for_status = int(epochs_for_status)
                if epochs_for_status <= 0:
                    epochs_for_status = 5
            except (ValueError, TypeError):
                epochs_for_status = 5

            await self._update_status({
                "status": "training",
                "message": "Starting Whisper fine-tuning...",
                "progress": 50,
                "current_epoch": 0,
                "total_epochs": epochs_for_status,
                "samples_processed": 0,
                "total_samples": len(training_data)
            })

            # Run training with real Hugging Face trainer
            logger.info("About to start training execution...")
            training_result = await self._run_training(trainer, training_settings)
            logger.info("Training execution completed successfully!")
            logger.info(f"Training result received: {training_result}")
            logger.info("Proceeding to save model...")

            # Step 6: Save fine-tuned model
            await self._update_status({
                "status": "saving",
                "message": "Saving fine-tuned model...",
                "progress": 90
            })

            model_path = await self._save_model(training_settings)

            # Step 7: Upload to GCS (optional, controlled by environment variable)
            gcs_url = None
            if app_settings.UPLOAD_TO_GCS:
                await self._update_status({
                    "status": "uploading",
                    "message": "Uploading model to cloud storage...",
                    "progress": 95
                })

                try:
                    gcs_url = await self._upload_model(model_path, training_settings)
                    logger.info(f"Model uploaded to GCS: {gcs_url}")
                except Exception as upload_error:
                    logger.warning(f"Failed to upload model to GCS: {upload_error}")
                    logger.info("Continuing without GCS upload - model is saved locally")
                    # Don't fail the entire training if upload fails
            else:
                logger.info("GCS upload disabled by environment variable UPLOAD_TO_GCS=False")

            # Step 8: Mark data as trained
            audio_ids = [item['audio_id'] for item in training_data]
            logger.info(f"Marking {len(audio_ids)} audio samples as trained: {audio_ids}")
            firebase_service.mark_data_as_trained(audio_ids)

            # Step 9: Complete
            completion_message = "Fine-tuning completed successfully"
            if gcs_url:
                completion_message += " and uploaded to cloud storage"
            elif app_settings.UPLOAD_TO_GCS:
                completion_message += " (cloud upload failed, model saved locally)"
            else:
                completion_message += " (cloud upload disabled)"

            model_version = f"whisper-masalit-{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            end_time = datetime.now().isoformat()

            await self._update_status({
                "status": "completed",
                "message": completion_message,
                "progress": 100,
                "current_epoch": epochs_for_status,
                "total_epochs": epochs_for_status,
                "samples_processed": len(training_data),
                "total_samples": len(training_data),
                "samples_trained": len(audio_ids),
                "model_version": model_version,
                "model_path": model_path,
                "gcs_url": gcs_url,
                "upload_success": gcs_url is not None,
                "end_time": end_time
            })

            # Step 10: Save training history record
            try:
                training_history_data = {
                    "model_version": model_version,
                    "base_model": f"openai/whisper-{training_settings.get('model_name', 'small')}",
                    "training_settings": training_settings,
                    "samples_trained": len(audio_ids),
                    "model_path": model_path,
                    "gcs_url": gcs_url,
                    "upload_success": gcs_url is not None,
                    "start_time": training_settings.get('start_time', datetime.now().isoformat()),
                    "end_time": end_time,
                    "status": "completed",
                    "epochs": epochs_for_status,
                    "accuracy": 0.0,  # Will be updated by validation
                    "confidence": 0.0,  # Will be updated by validation
                    "wer": 0.0,  # Will be updated by validation
                    "cer": 0.0,  # Will be updated by validation
                    "ser": 0.0   # Will be updated by validation
                }

                firebase_service.save_training_history("asr", training_history_data)
                logger.info("Training history record saved successfully")
            except Exception as history_error:
                logger.error(f"Failed to save training history: {history_error}")
                # Don't fail the entire training process for history save errors

            # Step 11: Create/Update model record
            try:
                # Check if this model already exists
                existing_models = firebase_service.list_models(model_type="asr", status="active")

                # Deactivate previous active models
                for existing_model in existing_models:
                    try:
                        firebase_service.db.collection('models').document(existing_model['model_id']).update({
                            'status': 'inactive',
                            'updated_at': datetime.now().isoformat()
                        })
                        logger.info(f"Deactivated previous model: {existing_model['model_id']}")
                    except Exception as e:
                        logger.warning(f"Failed to deactivate model {existing_model['model_id']}: {e}")

                # Create new model record
                model_data = {
                    "model_id": f"asr_{model_version}",
                    "model_type": "asr",
                    "model_version": model_version,
                    "base_model": f"openai/whisper-{training_settings.get('model_name', 'small')}",
                    "status": "active",
                    "confidence": 0.0,  # Will be updated by validation
                    "accuracy": 0.0,    # Will be updated by validation
                    "wer": 0.0,         # Will be updated by validation
                    "cer": 0.0,         # Will be updated by validation
                    "ser": 0.0,         # Will be updated by validation
                    "samples_trained": len(audio_ids),
                    "total_training_sessions": 1,  # Will be incremented for retraining
                    "first_trained": end_time,
                    "last_trained": end_time,
                    "model_path": model_path,
                    "gcs_url": gcs_url,
                    "upload_success": gcs_url is not None,
                    "training_epochs": epochs_for_status,
                    "created_at": end_time
                }

                firebase_service.save_model_record(model_data)
                logger.info(f"Model record created: {model_data['model_id']}")
            except Exception as model_error:
                logger.error(f"Failed to save model record: {model_error}")
                # Don't fail the entire training process for model record save errors

            logger.info("=== TRAINING PIPELINE COMPLETED SUCCESSFULLY ===")

            # Log training completion event
            log_training_event("asr", "completed", {
                "samples_trained": len(audio_ids),
                "model_version": model_version,
                "model_path": model_path,
                "gcs_url": gcs_url,
                "upload_success": gcs_url is not None,
                "end_time": end_time
            })

            return {"message": "Training completed successfully", "status": "completed"}

        except asyncio.CancelledError:
            await self._update_status({
                "status": "stopped",
                "message": "Training was cancelled",
                "progress": 0
            })
        except Exception as e:
            logger.error(f"Training failed: {e}")

            # Log training failure event
            log_training_event("asr", "failed", {
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })

            await self._update_status({
                "status": "failed",
                "error": str(e),
                "progress": 0
            })
        finally:
            logger.info("Entering finally block - cleaning up training state")
            self.is_training = False

            # Cancel status update task if it exists
            if self.status_update_task:
                logger.info("Cancelling status update task...")
                self.status_update_task.cancel()
                try:
                    await asyncio.wait_for(self.status_update_task, timeout=2.0)
                except (asyncio.CancelledError, asyncio.TimeoutError):
                    pass
                self.status_update_task = None
                logger.info("Status update task cancelled")

            logger.info("Training cleanup completed")

    async def _load_model(self, training_settings: Dict):
        """Load Whisper model and processor - either base model or existing trained model"""
        try:
            # Check training settings for model selection preference
            use_existing_model = training_settings.get('use_existing_model', True)
            base_model_name = training_settings.get('model_name', 'small')

            model_loaded = False

            # Try to load existing trained model if requested
            if use_existing_model:
                try:
                    # Get the current active model
                    model_info = firebase_service.get_model_info("asr")

                    if (model_info.get('model_id') and
                        model_info.get('status') == 'active' and
                        model_info.get('model_path')):

                        model_path = model_info['model_path']
                        logger.info(f"Attempting to load existing trained model from: {model_path}")

                        # Check if the model path exists
                        if Path(model_path).exists():
                            self.processor = WhisperProcessor.from_pretrained(model_path)
                            self.model = WhisperForConditionalGeneration.from_pretrained(model_path)
                            model_loaded = True
                            logger.info(f"Successfully loaded existing model: {model_info['version']}")

                            # Update training settings to reflect we're continuing from existing model
                            training_settings['base_model_used'] = model_info['version']
                            training_settings['training_type'] = 'incremental'
                        else:
                            logger.warning(f"Model path does not exist: {model_path}")

                except Exception as e:
                    logger.warning(f"Failed to load existing model, falling back to base model: {e}")

            # Fall back to base model if existing model couldn't be loaded
            if not model_loaded:
                model_id = f"openai/whisper-{base_model_name}"
                logger.info(f"Loading base Whisper model: {model_id}")

                self.processor = WhisperProcessor.from_pretrained(model_id)
                self.model = WhisperForConditionalGeneration.from_pretrained(model_id)

                # Update training settings to reflect we're starting from base model
                training_settings['base_model_used'] = model_id
                training_settings['training_type'] = 'from_scratch'

                logger.info(f"Loaded base model: {model_id}")

            # Move to GPU if available
            if torch.cuda.is_available():
                self.model = self.model.cuda()
                logger.info("Model moved to GPU")
            else:
                logger.info("Using CPU for training")

            logger.info(f"Model loaded successfully: {self.model.num_parameters():,} parameters")

        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise

    def _process_audio_with_ffmpeg(self, input_path: str, output_path: str) -> bool:
        """Process audio file using FFmpeg to ensure compatibility"""
        try:
            # Use FFmpeg to convert audio to 16kHz WAV format
            command = [
                'ffmpeg',
                '-i', input_path,
                '-ar', '16000',  # Sample rate 16kHz
                '-ac', '1',      # Mono channel
                '-c:a', 'pcm_s16le',  # PCM 16-bit little-endian
                '-y',            # Overwrite output file
                output_path
            ]

            result = subprocess.run(command, capture_output=True, text=True)

            if result.returncode == 0:
                logger.info(f"Successfully processed audio with FFmpeg: {output_path}")
                return True
            else:
                logger.error(f"FFmpeg error: {result.stderr}")
                return False

        except FileNotFoundError:
            logger.error("FFmpeg not found. Please install FFmpeg.")
            return False
        except Exception as e:
            logger.error(f"Error processing audio with FFmpeg: {e}")
            return False

    def _pad_audio_for_whisper(self, audio_array: np.ndarray, target_length: int = 480000) -> np.ndarray:
        """Pad or truncate audio to target length (30 seconds at 16kHz = 480000 samples)"""
        current_length = len(audio_array)

        if current_length > target_length:
            # Truncate if too long
            logger.info(f"Truncating audio from {current_length} to {target_length} samples")
            return audio_array[:target_length]
        elif current_length < target_length:
            # Pad with zeros if too short
            padding_needed = target_length - current_length
            logger.info(f"Padding audio from {current_length} to {target_length} samples (adding {padding_needed} zeros)")
            return np.pad(audio_array, (0, padding_needed), mode='constant', constant_values=0)
        else:
            return audio_array
    
    async def _prepare_dataset(self, training_data: List[Dict], training_settings: Dict) -> Dataset:
        """Download audio files and prepare dataset for Whisper training"""
        try:
            logger.info(f"Preparing dataset with {len(training_data)} samples")

            processed_samples = []

            # Create temp directory if it doesn't exist
            temp_dir = Path(app_settings.TEMP_DIR)
            temp_dir.mkdir(parents=True, exist_ok=True)

            for i, item in enumerate(training_data):
                try:
                    audio_url = item['audio_url']
                    audio_id = item['audio_id']
                    transcription = item['transcription']

                    if not audio_url or not transcription:
                        logger.warning(f"Skipping {audio_id}: missing audio_url or transcription")
                        continue

                    logger.info(f"Processing audio {audio_id}: {audio_url}")

                    # Download audio file with proper headers
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                    audio_response = requests.get(audio_url, headers=headers, timeout=60)
                    audio_response.raise_for_status()

                    # Save to temporary file with proper extension
                    audio_format = item.get('format', 'audio/wav')
                    file_ext = '.wav' if 'wav' in audio_format else '.mp3'
                    temp_audio_path = temp_dir / f"audio_{audio_id}{file_ext}"
                    processed_audio_path = temp_dir / f"processed_{audio_id}.wav"

                    with open(temp_audio_path, 'wb') as f:
                        f.write(audio_response.content)

                    logger.info(f"Downloaded audio to {temp_audio_path} ({len(audio_response.content)} bytes)")

                    # Process audio with FFmpeg first
                    if not self._process_audio_with_ffmpeg(str(temp_audio_path), str(processed_audio_path)):
                        logger.warning(f"FFmpeg processing failed for {audio_id}, trying librosa directly")
                        processed_audio_path = temp_audio_path

                    # Load and resample audio to 16kHz (required by Whisper)
                    try:
                        audio_array, sample_rate = librosa.load(str(processed_audio_path), sr=16000)
                        logger.info(f"Loaded audio: {len(audio_array)} samples at {sample_rate}Hz")

                        # Ensure audio is not empty
                        if len(audio_array) == 0:
                            logger.warning(f"Empty audio file: {audio_id}")
                            continue

                        # Pad or truncate audio to ensure consistent length for Whisper
                        audio_array = self._pad_audio_for_whisper(audio_array)

                        # Prepare sample for Whisper
                        processed_samples.append({
                            "audio": {
                                "array": audio_array.astype(np.float32),
                                "sampling_rate": 16000
                            },
                            "text": transcription,
                            "audio_id": audio_id
                        })

                        logger.info(f"Successfully processed audio {audio_id}")

                    except Exception as audio_error:
                        logger.error(f"Error loading audio {audio_id}: {audio_error}")
                        continue
                    finally:
                        # Clean up temp files
                        if temp_audio_path.exists():
                            temp_audio_path.unlink()
                        if processed_audio_path.exists() and processed_audio_path != temp_audio_path:
                            processed_audio_path.unlink()

                    # Update progress
                    progress = ((i + 1) / len(training_data)) * 15  # 15% of total progress
                    await self._update_status({
                        "status": "preparing_data",
                        "message": f"Processing audio {i + 1}/{len(training_data)}...",
                        "progress": 25 + progress,
                        "samples_processed": len(processed_samples),  # Use actual processed count
                        "total_samples": len(training_data)
                    })

                except Exception as e:
                    logger.error(f"Failed to process audio {item.get('audio_id', 'unknown')}: {e}")
                    continue

            if not processed_samples:
                raise Exception("No audio samples could be processed. Check audio URLs and network connectivity.")

            logger.info(f"Successfully processed {len(processed_samples)} out of {len(training_data)} samples")

            # Create Hugging Face dataset
            dataset = Dataset.from_list(processed_samples)

            # Cast audio column to Audio feature
            dataset = dataset.cast_column("audio", Audio(sampling_rate=16000))

            logger.info(f"Dataset prepared with {len(processed_samples)} valid samples")
            return dataset

        except Exception as e:
            logger.error(f"Error preparing dataset: {e}")
            raise
    

    
    async def _setup_trainer(self, dataset: Dataset, training_settings: Dict) -> Trainer:
        """Setup Hugging Face trainer for Whisper fine-tuning"""
        try:
            # Ensure checkpoint directory exists
            checkpoint_dir = Path(app_settings.CHECKPOINT_DIR)
            checkpoint_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"Checkpoint directory created/verified: {checkpoint_dir}")

            # Training arguments optimized for Whisper
            # Validate epochs parameter
            epochs = training_settings.get('epochs', 5)
            try:
                epochs = int(epochs)
                if epochs <= 0:
                    logger.warning(f"Invalid epochs: {epochs}, using default 5")
                    epochs = 5
            except (ValueError, TypeError):
                logger.warning(f"Invalid epochs type: {epochs}, using default 5")
                epochs = 5

            training_args = TrainingArguments(
                output_dir=str(checkpoint_dir),
                num_train_epochs=epochs,
                per_device_train_batch_size=training_settings.get('batch_size', 4),  # Smaller batch for memory
                per_device_eval_batch_size=training_settings.get('eval_batch_size', 4),
                learning_rate=training_settings.get('learning_rate', 1e-5),  # Lower LR for fine-tuning
                warmup_steps=training_settings.get('warmup_steps', 50),
                logging_steps=10,
                eval_steps=training_settings.get('eval_steps', 50),
                save_steps=training_settings.get('eval_steps', 50),
                evaluation_strategy="steps",
                save_strategy="steps",
                load_best_model_at_end=True,
                metric_for_best_model="eval_loss",
                greater_is_better=False,
                report_to=None,  # Disable wandb/tensorboard
                dataloader_num_workers=0,  # Avoid multiprocessing issues
                fp16=torch.cuda.is_available(),  # Use mixed precision if GPU available
                gradient_checkpointing=True,  # Save memory
                remove_unused_columns=False,  # Keep all columns for Whisper
                label_names=["labels"],  # Specify label column
            )

            # Split dataset for training and validation
            validation_split = training_settings.get('validation_split', 0.2)

            # Ensure validation_split is a valid float
            try:
                validation_split = float(validation_split)
                if validation_split < 0 or validation_split > 1:
                    logger.warning(f"Invalid validation_split: {validation_split}, using default 0.2")
                    validation_split = 0.2
            except (ValueError, TypeError):
                logger.warning(f"Invalid validation_split type: {validation_split}, using default 0.2")
                validation_split = 0.2

            dataset_size = len(dataset)

            # Handle validation split of 0 (no validation)
            if validation_split == 0 or dataset_size <= 1:
                # No validation split - use all data for training and same data for eval
                train_dataset = dataset
                eval_dataset = dataset
                logger.info(f"No validation split: using all {dataset_size} samples for both training and evaluation")
            elif dataset_size > 1:
                # Calculate minimum validation samples (at least 1)
                min_val_samples = max(1, int(dataset_size * validation_split))
                # Ensure we don't take all samples for validation
                max_val_samples = dataset_size - 1
                val_samples = min(min_val_samples, max_val_samples)

                # Use absolute number of samples instead of fraction
                train_test_split = dataset.train_test_split(test_size=val_samples, seed=42)
                train_dataset = train_test_split['train']
                eval_dataset = train_test_split['test']

                logger.info(f"Dataset split: {len(train_dataset)} training, {len(eval_dataset)} validation samples")
            else:
                # Fallback for edge cases
                train_dataset = dataset
                eval_dataset = dataset
                logger.info(f"Fallback: using all {dataset_size} samples for both training and validation")

            # Data collator for Whisper
            @dataclass
            class WhisperDataCollator:
                processor: WhisperProcessor

                def __call__(self, features):
                    # Extract audio arrays and texts
                    audio_arrays = [feature["audio"]["array"] for feature in features]
                    texts = [feature["text"] for feature in features]

                    # Process audio inputs
                    batch = self.processor(
                        audio_arrays,
                        sampling_rate=16000,
                        return_tensors="pt",
                        padding=True
                    )

                    # Process text labels
                    labels = self.processor.tokenizer(
                        texts,
                        return_tensors="pt",
                        padding=True,
                        truncation=True,
                        max_length=448  # Whisper max length
                    )

                    # Replace padding token id's of the labels by -100 so it's ignored by loss
                    labels["input_ids"] = labels["input_ids"].masked_fill(
                        labels["attention_mask"] == 0, -100
                    )

                    batch["labels"] = labels["input_ids"]

                    return batch

            data_collator = WhisperDataCollator(processor=self.processor)

            # Custom callback for progress updates
            class ProgressCallback(TrainerCallback):
                def __init__(self, status_queue, total_epochs):
                    self.status_queue = status_queue
                    self.total_epochs = total_epochs

                def on_epoch_end(self, args, state, control, **kwargs):
                    # Put status update in queue (thread-safe)
                    try:
                        status_data = {
                            "status": "training",
                            "current_epoch": int(state.epoch),
                            "total_epochs": self.total_epochs,
                            "progress": 50 + (state.epoch / self.total_epochs) * 40,
                            "message": f"Completed epoch {int(state.epoch)}/{self.total_epochs}"
                        }
                        logger.info(f"Putting status update in queue: epoch {int(state.epoch)}")
                        try:
                            self.status_queue.put(status_data, block=False)  # Don't block if queue is full
                            logger.info(f"Status update queued successfully")
                        except queue.Full:
                            logger.warning("Status queue is full, skipping update")
                    except Exception as e:
                        logger.error(f"Error updating progress: {e}")

            # Create trainer
            trainer = Trainer(
                model=self.model,
                args=training_args,
                train_dataset=train_dataset,
                eval_dataset=eval_dataset,
                data_collator=data_collator,
                callbacks=[
                    EarlyStoppingCallback(early_stopping_patience=training_settings.get('early_stopping_patience', 3)),
                    ProgressCallback(self.status_queue, epochs)
                ]
            )

            return trainer

        except Exception as e:
            logger.error(f"Error setting up trainer: {e}")
            raise
    
    async def _run_training(self, trainer: Trainer, training_settings: Dict):
        """Run the actual Whisper fine-tuning"""
        try:
            logger.info("Starting Whisper fine-tuning...")

            # Run training in a separate thread to avoid blocking
            def run_training_sync():
                logger.info("Training thread started")
                try:
                    logger.info("About to call trainer.train()...")
                    result = trainer.train()
                    logger.info("trainer.train() completed successfully")
                    logger.info(f"Training result: {result}")
                    logger.info("Training thread exiting normally")
                    return result
                except Exception as e:
                    logger.error(f"Error in training thread: {e}")
                    raise

            # Execute training with configurable timeout
            logger.info("Submitting training to executor...")
            loop = asyncio.get_event_loop()

            # Get timeout from training settings or use default from app settings
            timeout_seconds = training_settings.get('training_timeout')
            if timeout_seconds is None:
                timeout_seconds = app_settings.DEFAULT_TRAINING_TIMEOUT

            # Ensure timeout_seconds is an integer
            try:
                timeout_seconds = int(timeout_seconds)
            except (ValueError, TypeError):
                logger.warning(f"Invalid timeout value: {timeout_seconds}, using default")
                timeout_seconds = app_settings.DEFAULT_TRAINING_TIMEOUT

            # Enforce maximum timeout limit for safety
            if timeout_seconds > app_settings.MAX_TRAINING_TIMEOUT:
                logger.warning(f"Requested timeout {timeout_seconds}s exceeds maximum {app_settings.MAX_TRAINING_TIMEOUT}s, using maximum")
                timeout_seconds = app_settings.MAX_TRAINING_TIMEOUT

            logger.info(f"Training timeout set to {timeout_seconds} seconds ({timeout_seconds/3600:.1f} hours)")

            try:
                training_result = await asyncio.wait_for(
                    loop.run_in_executor(None, run_training_sync),
                    timeout=timeout_seconds
                )
            except asyncio.TimeoutError:
                logger.error(f"Training timed out after {timeout_seconds} seconds ({timeout_seconds/3600:.1f} hours)")
                raise Exception(f"Training timed out after {timeout_seconds} seconds ({timeout_seconds/3600:.1f} hours)")

            logger.info("Training executor completed successfully")
            logger.info(f"Training result: {training_result}")
            logger.info("About to exit _run_training method")
            return training_result

        except Exception as e:
            logger.error(f"Error during training: {e}")
            raise

    async def _save_model(self, training_settings: Dict) -> str:
        """Save the fine-tuned model"""
        try:
            logger.info("Starting model save process...")
            model_name = f"whisper-masalit-{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            model_path = Path(app_settings.FINAL_MODEL_DIR) / model_name
            model_path.mkdir(parents=True, exist_ok=True)

            logger.info(f"Saving model to {model_path}")

            # Save model and processor in executor to prevent blocking
            def save_model_sync():
                logger.info("Saving model weights...")
                self.model.save_pretrained(str(model_path))
                logger.info("Model weights saved successfully")

                logger.info("Saving processor...")
                self.processor.save_pretrained(str(model_path))
                logger.info("Processor saved successfully")

                return str(model_path)

            loop = asyncio.get_event_loop()
            saved_path = await loop.run_in_executor(None, save_model_sync)

            # Save training info
            logger.info("Saving training info...")

            # Convert Firestore timestamps to ISO strings for JSON serialization
            def convert_firestore_timestamps(obj):
                """Recursively convert Firestore DatetimeWithNanoseconds to ISO strings"""
                if hasattr(obj, 'timestamp'):  # Firestore DatetimeWithNanoseconds
                    return obj.isoformat()
                elif isinstance(obj, dict):
                    return {k: convert_firestore_timestamps(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_firestore_timestamps(item) for item in obj]
                else:
                    return obj

            # Clean training settings for JSON serialization
            clean_training_settings = convert_firestore_timestamps(training_settings)

            training_info = {
                "model_name": model_name,
                "base_model": f"openai/whisper-{training_settings.get('model_name', 'small')}",
                "training_settings": clean_training_settings,
                "saved_at": datetime.now().isoformat(),
                "model_parameters": self.model.num_parameters()
            }

            with open(model_path / "training_info.json", 'w') as f:
                json.dump(training_info, f, indent=2, default=str)
            logger.info("Training info saved successfully")

            logger.info(f"Model saved successfully to {model_path}")
            return saved_path

        except Exception as e:
            logger.error(f"Error saving model: {e}")
            raise

    async def _upload_model(self, model_path: str, training_settings: Dict) -> str:
        """Upload model to GCS with retry logic"""
        archive_path = None
        try:
            logger.info("Starting model upload process...")
            model_name = Path(model_path).stem

            # Create tar.gz archive
            logger.info(f"Creating archive for model: {model_name}")
            archive_path = f"{model_path}.tar.gz"
            with tarfile.open(archive_path, "w:gz") as tar:
                tar.add(model_path, arcname=model_name)

            logger.info(f"Created model archive: {archive_path}")

            # Upload to GCS with retry logic (run in executor since GCS service is synchronous)
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    logger.info(f"Uploading to GCS (attempt {attempt + 1}/{max_retries})...")

                    def upload_sync():
                        return gcs_service.upload_model(archive_path, model_name, "latest")

                    loop = asyncio.get_event_loop()
                    gcs_url = await loop.run_in_executor(None, upload_sync)

                    logger.info(f"Model uploaded to GCS successfully: {gcs_url}")
                    return gcs_url

                except Exception as upload_error:
                    logger.warning(f"Upload attempt {attempt + 1} failed: {upload_error}")
                    if attempt == max_retries - 1:
                        # Last attempt failed, re-raise the error
                        raise upload_error
                    else:
                        # Wait before retrying
                        await asyncio.sleep(2 ** attempt)  # Exponential backoff

        except Exception as e:
            logger.error(f"Error uploading model after {max_retries} attempts: {e}")
            raise
        finally:
            # Clean up local archive regardless of upload success/failure
            if archive_path and os.path.exists(archive_path):
                try:
                    logger.info("Cleaning up local archive...")
                    os.remove(archive_path)
                except Exception as cleanup_error:
                    logger.warning(f"Failed to clean up archive: {cleanup_error}")

    
    async def _update_status(self, status_data: Dict[str, Any]):
        """Update training status in clean Firebase structure"""
        try:
            # For now, we'll create a simple status update
            # In a full implementation, we'd track the current training session
            clean_firebase_service.initialize()

            # Create or update a current training session
            session_id = f"asr_current_{datetime.now().strftime('%Y%m%d')}"

            # Update progress in the training session
            training_ref = clean_firebase_service.db.collection('training').document(session_id)

            # Ensure main document exists
            training_ref.set({
                'session_id': session_id,
                'model_type': 'asr',
                'status': status_data.get('status', 'training'),
                'updated_at': datetime.now().isoformat()
            }, merge=True)

            # Update progress
            progress_ref = training_ref.collection('progress').document('current')
            progress_ref.set({
                **status_data,
                'updated_at': datetime.now().isoformat()
            }, merge=True)

        except Exception as e:
            logger.error(f"Error updating status: {e}")

# Global trainer instance
asr_trainer = ASRTrainer()
