#!/usr/bin/env node

/**
 * Deploy Firestore Indexes Script
 * 
 * This script helps deploy the required Firestore indexes for the Masalit AI platform.
 * Run this script to create all necessary composite indexes.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔥 Firestore Index Deployment Script');
console.log('=====================================\n');

// Check if Firebase CLI is installed
try {
  execSync('firebase --version', { stdio: 'pipe' });
  console.log('✅ Firebase CLI is installed');
} catch (error) {
  console.error('❌ Firebase CLI is not installed. Please install it first:');
  console.error('npm install -g firebase-tools');
  process.exit(1);
}

// Check if firestore.indexes.json exists
const indexesPath = path.join(__dirname, '..', 'firestore.indexes.json');
if (!fs.existsSync(indexesPath)) {
  console.error('❌ firestore.indexes.json not found');
  process.exit(1);
}

console.log('✅ firestore.indexes.json found');

// Read and display the indexes that will be created
const indexesConfig = JSON.parse(fs.readFileSync(indexesPath, 'utf8'));
console.log(`\n📊 Found ${indexesConfig.indexes.length} indexes to deploy:`);

indexesConfig.indexes.forEach((index, i) => {
  console.log(`\n${i + 1}. Collection: ${index.collectionGroup}`);
  console.log(`   Fields: ${index.fields.map(f => `${f.fieldPath} (${f.order})`).join(', ')}`);
});

console.log('\n🚀 Deploying indexes...');

try {
  // Deploy the indexes
  execSync('firebase deploy --only firestore:indexes', { 
    stdio: 'inherit',
    cwd: path.join(__dirname, '..')
  });
  
  console.log('\n✅ Indexes deployed successfully!');
  console.log('\n📝 Note: It may take several minutes for indexes to build.');
  console.log('   You can check the status in the Firebase Console:');
  console.log('   https://console.firebase.google.com/project/YOUR_PROJECT_ID/firestore/indexes');
  
} catch (error) {
  console.error('\n❌ Failed to deploy indexes:', error.message);
  console.log('\n🔧 Manual deployment steps:');
  console.log('1. Run: firebase login');
  console.log('2. Run: firebase use YOUR_PROJECT_ID');
  console.log('3. Run: firebase deploy --only firestore:indexes');
  process.exit(1);
}

console.log('\n🎉 Deployment complete!');
