"""
Validation Types and Configurations
"""

from enum import Enum
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pydantic import BaseModel

try:
    from backend.core.models.types import ModelType
except ImportError:
    # Fallback for when models module is not available
    class ModelType(str, Enum):
        ASR = "asr"
        TTS = "tts"
        NLP = "nlp"
        CUSTOM = "custom"


class ValidationType(str, Enum):
    """Types of validation"""
    QUICK = "quick"          # Fast validation with minimal samples
    FULL = "full"            # Comprehensive validation with all available data
    CUSTOM = "custom"        # Custom validation with user-defined parameters
    BENCHMARK = "benchmark"  # Standardized benchmark testing


class ValidationStatus(str, Enum):
    """Validation status states"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class MetricType(str, Enum):
    """Types of metrics that can be calculated"""
    # ASR Metrics
    WER = "wer"                    # Word Error Rate
    CER = "cer"                    # Character Error Rate
    SER = "ser"                    # Sentence Error Rate
    ACCURACY = "accuracy"          # Overall accuracy
    CONFIDENCE = "confidence"      # Model confidence
    
    # TTS Metrics
    MOS = "mos"                    # Mean Opinion Score
    SIMILARITY = "similarity"     # Voice similarity
    NATURALNESS = "naturalness"   # Speech naturalness
    
    # General Metrics
    LATENCY = "latency"           # Inference latency
    MEMORY_USAGE = "memory_usage" # Memory consumption
    THROUGHPUT = "throughput"     # Samples per second


class ValidationConfig(BaseModel):
    """Validation configuration"""
    model_config = {"protected_namespaces": ()}

    validation_type: ValidationType = ValidationType.QUICK
    model_type: ModelType
    
    # Sample selection
    max_samples: Optional[int] = None
    min_confidence: Optional[float] = None
    sample_selection_strategy: str = "random"  # random, worst, best, diverse
    
    # Test dataset
    test_dataset: Optional[str] = None
    use_validation_split: bool = True
    validation_split_ratio: float = 0.2
    
    # Metrics to compute
    metrics_to_compute: List[MetricType] = []
    
    # Performance settings
    batch_size: int = 8
    max_duration_seconds: Optional[int] = None
    
    # Model-specific configurations
    asr_config: Optional[Dict[str, Any]] = None
    tts_config: Optional[Dict[str, Any]] = None
    
    # Output settings
    save_predictions: bool = True
    save_error_analysis: bool = True
    generate_report: bool = True


class ValidationMetrics(BaseModel):
    """Validation metrics results"""
    # Core metrics (always present)
    accuracy: float = 0.0
    confidence: float = 0.0
    
    # ASR-specific metrics
    wer: Optional[float] = None
    cer: Optional[float] = None
    ser: Optional[float] = None
    
    # TTS-specific metrics
    mos: Optional[float] = None
    similarity: Optional[float] = None
    naturalness: Optional[float] = None
    
    # Performance metrics
    latency_ms: Optional[float] = None
    memory_usage_mb: Optional[float] = None
    throughput_samples_per_sec: Optional[float] = None
    
    # Additional metrics
    custom_metrics: Dict[str, float] = {}


class ErrorAnalysis(BaseModel):
    """Error analysis for individual samples"""
    sample_id: str
    reference: str
    prediction: str
    
    # Sample-level metrics
    sample_metrics: ValidationMetrics
    
    # Error categorization
    error_type: Optional[str] = None
    error_severity: Optional[str] = None  # low, medium, high
    error_category: Optional[str] = None  # substitution, insertion, deletion, etc.
    
    # Context information
    metadata: Dict[str, Any] = {}
    
    # Confidence and uncertainty
    prediction_confidence: Optional[float] = None
    uncertainty_score: Optional[float] = None


class ValidationSummary(BaseModel):
    """Summary of validation results"""
    total_samples: int
    successful_samples: int
    failed_samples: int
    skipped_samples: int
    
    # Aggregate metrics
    average_metrics: ValidationMetrics
    best_metrics: ValidationMetrics
    worst_metrics: ValidationMetrics
    
    # Distribution statistics
    confidence_distribution: Dict[str, int] = {}  # confidence ranges -> count
    error_distribution: Dict[str, int] = {}       # error types -> count
    
    # Performance statistics
    total_duration_seconds: float
    average_latency_ms: float
    total_memory_usage_mb: float


class ValidationResult(BaseModel):
    """Complete validation result"""
    model_config = {"protected_namespaces": ()}

    # Identification
    validation_id: str
    model_id: str
    model_type: ModelType
    
    # Configuration
    config: ValidationConfig
    
    # Status and timing
    status: ValidationStatus
    started_at: datetime
    completed_at: Optional[datetime] = None
    duration_seconds: Optional[float] = None
    
    # Results
    metrics: ValidationMetrics
    summary: ValidationSummary
    error_analysis: List[ErrorAnalysis] = []
    
    # Predictions (if saved)
    predictions: Optional[List[Dict[str, Any]]] = None
    
    # Error information (if failed)
    error_message: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None
    
    # Report generation
    report_path: Optional[str] = None
    report_url: Optional[str] = None


class BenchmarkResult(BaseModel):
    """Benchmark validation result"""
    model_config = {"protected_namespaces": ()}

    benchmark_name: str
    benchmark_version: str
    model_id: str
    model_type: ModelType
    
    # Benchmark-specific metrics
    benchmark_score: float
    percentile_rank: Optional[float] = None
    
    # Detailed results
    validation_result: ValidationResult
    
    # Comparison with baseline
    baseline_comparison: Optional[Dict[str, float]] = None
    
    # Benchmark metadata
    benchmark_metadata: Dict[str, Any] = {}


class ValidationProgress(BaseModel):
    """Validation progress tracking"""
    validation_id: str
    status: ValidationStatus
    progress_percentage: float
    current_step: str
    samples_processed: int
    total_samples: int
    estimated_time_remaining_seconds: Optional[float] = None
    current_metrics: Optional[ValidationMetrics] = None
