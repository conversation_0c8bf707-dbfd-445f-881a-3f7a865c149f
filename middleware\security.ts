import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

interface SecurityMiddlewareOptions {
  requireAuth?: boolean
  adminOnly?: boolean
  rateLimitKey?: string
  rateLimitMax?: number
  rateLimitWindow?: number // in minutes
}

// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

export function withSecurity(
  handler: (req: NextRequest, ...args: any[]) => Promise<NextResponse>,
  options: SecurityMiddlewareOptions = {}
) {
  return async function (req: NextRequest, ...args: any[]): Promise<NextResponse> {
    const startTime = Date.now()
    
    try {
      // Get client information
      const ip_address = req.headers.get('x-forwarded-for') || 
                        req.headers.get('x-real-ip') || 
                        'unknown'
      const user_agent = req.headers.get('user-agent') || 'unknown'

      // Rate limiting
      if (options.rateLimitKey) {
        const rateLimitResult = await checkRateLimit(
          options.rateLimitKey,
          ip_address,
          options.rateLimitMax || 100,
          options.rateLimitWindow || 15
        )
        
        if (!rateLimitResult.allowed) {
          return NextResponse.json(
            { error: 'Rate limit exceeded' },
            { 
              status: 429,
              headers: {
                'X-RateLimit-Limit': options.rateLimitMax?.toString() || '100',
                'X-RateLimit-Remaining': '0',
                'X-RateLimit-Reset': rateLimitResult.resetTime?.toString() || '0'
              }
            }
          )
        }
      }

      // Authentication check
      const session = await getServerSession(authOptions)
      
      if (options.requireAuth && !session) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Admin-only check
      if (options.adminOnly && session?.user?.role !== 'admin') {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // Execute the handler
      return await handler(req, ...args)
    } catch (error) {
      console.error('Security middleware error:', error)
      throw error
    }
  }
}

async function checkRateLimit(
  key: string,
  identifier: string,
  maxRequests: number,
  windowMinutes: number
): Promise<{ allowed: boolean; resetTime?: number }> {
  const now = Date.now()
  const windowMs = windowMinutes * 60 * 1000
  const rateLimitKey = `${key}:${identifier}`
  
  const current = rateLimitStore.get(rateLimitKey)
  
  if (!current || now > current.resetTime) {
    // New window or expired window
    const resetTime = now + windowMs
    rateLimitStore.set(rateLimitKey, { count: 1, resetTime })
    return { allowed: true, resetTime }
  }
  
  if (current.count >= maxRequests) {
    return { allowed: false, resetTime: current.resetTime }
  }
  
  // Increment count
  rateLimitStore.set(rateLimitKey, { 
    count: current.count + 1, 
    resetTime: current.resetTime 
  })
  
  return { allowed: true, resetTime: current.resetTime }
}

// Predefined middleware configurations
export const authRequired = (handler: any) =>
  withSecurity(handler, { requireAuth: true })

export const adminRequired = (handler: any) =>
  withSecurity(handler, { requireAuth: true, adminOnly: true })

export const rateLimited = (max: number, windowMinutes: number) => (handler: any) =>
  withSecurity(handler, {
    rateLimitKey: 'api',
    rateLimitMax: max,
    rateLimitWindow: windowMinutes
  })

export const secureEndpoint = (handler: any) =>
  withSecurity(handler, {
    requireAuth: true,
    rateLimitKey: 'secure',
    rateLimitMax: 50,
    rateLimitWindow: 15
  })

// Clean up rate limit store periodically (in production, use a proper cache)
setInterval(() => {
  const now = Date.now()
  for (const [key, value] of rateLimitStore.entries()) {
    if (now > value.resetTime) {
      rateLimitStore.delete(key)
    }
  }
}, 5 * 60 * 1000) // Clean up every 5 minutes
