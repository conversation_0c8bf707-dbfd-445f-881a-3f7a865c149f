"""
Google Cloud Storage service integration
"""

import os
import logging
from pathlib import Path
from typing import Optional, List
from google.cloud import storage
from google.oauth2 import service_account
from backend.config.settings import settings

logger = logging.getLogger(__name__)

class GCSService:
    """Google Cloud Storage service for model management"""
    
    def __init__(self):
        self.client = None
        self.bucket = None
        self._initialized = False
    
    def initialize(self):
        """Initialize GCS client"""
        if self._initialized:
            return
        
        try:
            # Create credentials from settings
            credentials = service_account.Credentials.from_service_account_info(
                settings.gcs_credentials
            )
            
            # Initialize GCS client
            self.client = storage.Client(
                project=settings.GCS_PROJECT_ID,
                credentials=credentials
            )
            
            # Get bucket
            self.bucket = self.client.bucket(settings.GCS_BUCKET_NAME)
            
            self._initialized = True
            logger.info("GCS initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize GCS: {e}")
            raise
    
    def upload_model(self, local_path: str, model_name: str, version: str = "latest") -> str:
        """Upload model to GCS"""
        try:
            if not self._initialized:
                self.initialize()
            
            # Create blob name
            blob_name = f"models/{model_name}/{version}/model.tar.gz"
            
            # Upload file
            blob = self.bucket.blob(blob_name)
            blob.upload_from_filename(local_path)
            
            # Make blob publicly readable (optional)
            # blob.make_public()
            
            gcs_url = f"gs://{settings.GCS_BUCKET_NAME}/{blob_name}"
            logger.info(f"Model uploaded to GCS: {gcs_url}")
            
            return gcs_url
            
        except Exception as e:
            logger.error(f"Error uploading model to GCS: {e}")
            raise
    
    def download_model(self, model_name: str, version: str = "latest", local_path: str = None) -> str:
        """Download model from GCS"""
        try:
            if not self._initialized:
                self.initialize()
            
            # Create blob name
            blob_name = f"models/{model_name}/{version}/model.tar.gz"
            
            # Set default local path if not provided
            if local_path is None:
                local_path = os.path.join(settings.FINAL_MODEL_DIR, f"{model_name}_{version}.tar.gz")
            
            # Ensure directory exists
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            
            # Download file
            blob = self.bucket.blob(blob_name)
            blob.download_to_filename(local_path)
            
            logger.info(f"Model downloaded from GCS: {local_path}")
            return local_path
            
        except Exception as e:
            logger.error(f"Error downloading model from GCS: {e}")
            raise
    
    def list_models(self, model_name: Optional[str] = None) -> List[dict]:
        """List available models in GCS"""
        try:
            if not self._initialized:
                self.initialize()
            
            prefix = "models/"
            if model_name:
                prefix += f"{model_name}/"
            
            blobs = self.client.list_blobs(self.bucket, prefix=prefix)
            
            models = []
            for blob in blobs:
                if blob.name.endswith("model.tar.gz"):
                    parts = blob.name.split("/")
                    if len(parts) >= 4:  # models/model_name/version/model.tar.gz
                        model_info = {
                            "name": parts[1],
                            "version": parts[2],
                            "size": blob.size,
                            "created": blob.time_created,
                            "updated": blob.updated,
                            "gcs_path": f"gs://{settings.GCS_BUCKET_NAME}/{blob.name}"
                        }
                        models.append(model_info)
            
            logger.info(f"Found {len(models)} models in GCS")
            return models
            
        except Exception as e:
            logger.error(f"Error listing models from GCS: {e}")
            raise
    
    def delete_model(self, model_name: str, version: str) -> bool:
        """Delete model from GCS"""
        try:
            if not self._initialized:
                self.initialize()
            
            blob_name = f"models/{model_name}/{version}/model.tar.gz"
            blob = self.bucket.blob(blob_name)
            
            if blob.exists():
                blob.delete()
                logger.info(f"Model deleted from GCS: {blob_name}")
                return True
            else:
                logger.warning(f"Model not found in GCS: {blob_name}")
                return False
                
        except Exception as e:
            logger.error(f"Error deleting model from GCS: {e}")
            raise
    
    def model_exists(self, model_name: str, version: str = "latest") -> bool:
        """Check if model exists in GCS"""
        try:
            if not self._initialized:
                self.initialize()
            
            blob_name = f"models/{model_name}/{version}/model.tar.gz"
            blob = self.bucket.blob(blob_name)
            
            return blob.exists()
            
        except Exception as e:
            logger.error(f"Error checking model existence in GCS: {e}")
            return False

# Global GCS service instance
gcs_service = GCSService()
