"use client"

import { SignupForm } from "@/components/signup-form"
import { useFocusedLanguage, FocusedLanguageProvider } from "@/components/focused-language-provider"
import { Button } from "@/components/ui/button"
import { Globe } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

function SignupContent() {
  const { language, setLanguage, isRTL } = useFocusedLanguage()

  return (
    <div className={`container flex h-screen w-screen flex-col items-center justify-center ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        {/* Language Selector */}
        <div className="flex justify-end">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="gap-2">
                <Globe className="h-4 w-4" />
                {language === 'ar' ? 'العربية' : 'English'}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setLanguage('en')}>
                English
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setLanguage('ar')}>
                العربية
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <SignupForm />
      </div>
    </div>
  )
}

export default function SignupPage() {
  return (
    <FocusedLanguageProvider>
      <SignupContent />
    </FocusedLanguageProvider>
  )
}