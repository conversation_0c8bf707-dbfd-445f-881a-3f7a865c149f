"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Play, Square, Zap, Loader2 } from "lucide-react"
import { TrainingStatus, TrainingSettings, ModelInfo } from "@/types/asr"
import { useToast } from "@/hooks/use-toast"
import { startTraining, stopTraining } from "@/lib/asr"

interface TrainingControlPanelProps {
  status: TrainingStatus
  settings: TrainingSettings
  onSettingsChange: (settings: TrainingSettings) => void
  onStatusChange?: (status: TrainingStatus) => void
  isServerAvailable: boolean
  modelInfo: ModelInfo | null
  onModelInfoUpdate?: () => void
}

export function TrainingControlPanel({
  status,
  settings,
  onSettingsChange,
  onStatusChange,
  isServerAvailable,
  modelInfo,
  onModelInfoUpdate
}: TrainingControlPanelProps) {
  const { toast } = useToast()
  const [isTraining, setIsTraining] = useState(status.status === 'training')
  const [isLoading, setIsLoading] = useState(false)

  const handleStartTraining = async () => {
    setIsLoading(true)
    try {
      // Call the actual training API with current settings
      const result = await startTraining(settings)

      toast({
        title: "Training Started",
        description: result.message || "ASR model training has begun",
      })
      setIsTraining(true)

      // Notify parent component of status change
      if (onStatusChange) {
        onStatusChange({
          ...status,
          status: 'training',
          progress: 0,
          current_epoch: 0,
          total_epochs: settings.epochs || 5
        })
      }

      // Refresh model info after training starts
      if (onModelInfoUpdate) {
        setTimeout(onModelInfoUpdate, 2000) // Refresh after a short delay
      }
    } catch (error: any) {
      console.error("Error starting training:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to start training",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleStopTraining = async () => {
    setIsLoading(true)
    try {
      // Call the actual stop training API
      const result = await stopTraining()

      toast({
        title: "Training Stopped",
        description: result.message || "ASR model training has been stopped",
      })
      setIsTraining(false)

      // Notify parent component of status change
      if (onStatusChange) {
        onStatusChange({
          ...status,
          status: 'stopped'
        })
      }
    } catch (error: any) {
      console.error("Error stopping training:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to stop training",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="grid gap-6 lg:grid-cols-3">
      {/* Training Control */}
      <div className="lg:col-span-2">
        <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="rounded-full bg-blue-100 p-2">
                  <Zap className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <CardTitle className="text-xl">Training Control</CardTitle>
                  <CardDescription>Start, stop, and monitor training progress</CardDescription>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Training Progress */}
            {status.status === 'training' && (
              <div className="space-y-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200">
                <div className="flex items-center justify-between">
                  <span className="font-medium text-blue-900">Training in Progress</span>
                  <span className="text-sm text-blue-700">{Math.round(status.progress * 100)}%</span>
                </div>
                <Progress value={status.progress * 100} className="h-3" />
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex justify-between">
                    <span className="text-blue-700">Epoch:</span>
                    <span className="font-medium text-blue-900">{status.current_epoch}/{status.total_epochs}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-blue-700">Samples:</span>
                    <span className="font-medium text-blue-900">{status.samples_processed || 0}/{status.total_samples || 0}</span>
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex items-center space-x-4">
              <Button 
                size="lg"
                onClick={handleStartTraining}
                disabled={isTraining || isLoading || !isServerAvailable}
                className="flex-1 h-12 text-base font-medium"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    Loading...
                  </>
                ) : isTraining ? (
                  <>
                    <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    Training in Progress
                  </>
                ) : (
                  <>
                    <Play className="mr-2 h-5 w-5" />
                    Start Training
                  </>
                )}
              </Button>
              
              <Button 
                size="lg"
                variant="destructive"
                onClick={handleStopTraining}
                disabled={!isServerAvailable || !isTraining}
                className="h-12"
              >
                <Square className="mr-2 h-4 w-4" />
                Stop
              </Button>
            </div>

            {/* Quick Settings */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium">Model Size</Label>
                <Select
                  value={settings.model_name}
                  onValueChange={(value) => onSettingsChange({ ...settings, model_name: value })}
                >
                  <SelectTrigger className="h-10">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="tiny">Tiny (Fast)</SelectItem>
                    <SelectItem value="base">Base (Balanced)</SelectItem>
                    <SelectItem value="small">Small (Good)</SelectItem>
                    <SelectItem value="medium">Medium (Better)</SelectItem>
                    <SelectItem value="large">Large (Best)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium">Training Samples</Label>
                <Input
                  type="number"
                  min="1"
                  value={settings.number_of_samples}
                  onChange={(e) => onSettingsChange({
                    ...settings,
                    number_of_samples: parseInt(e.target.value) || 1
                  })}
                  className="h-10"
                />
              </div>
            </div>

            {/* Model Selection Option */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Training Mode</Label>
              <Select
                value={settings.use_existing_model ? "continue" : "fresh"}
                onValueChange={(value) => onSettingsChange({
                  ...settings,
                  use_existing_model: value === "continue"
                })}
              >
                <SelectTrigger className="h-10">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="continue">
                    Continue from existing model {modelInfo?.version !== 'No model' ? `(${modelInfo?.version})` : '(none available)'}
                  </SelectItem>
                  <SelectItem value="fresh">Start fresh from base model</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500">
                {settings.use_existing_model
                  ? modelInfo?.version !== 'No model'
                    ? `Will continue training from your existing model: ${modelInfo?.version}`
                    : "No existing model found, will start from base model"
                  : `Will start fresh training from openai/whisper-${settings.model_name}`
                }
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Stats */}
      <div className="space-y-6">
        <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="pb-4">
            <div className="flex items-center space-x-3">
              <div className="rounded-full bg-green-100 p-2">
                <Zap className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <CardTitle className="text-lg">Training Data</CardTitle>
                <CardDescription>Available samples</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Status</span>
                <span className={`text-lg font-bold ${
                  modelInfo?.status === 'active' ? 'text-green-600' :
                  modelInfo?.status === 'training' ? 'text-blue-600' :
                  'text-gray-600'
                }`}>
                  {modelInfo?.status === 'active' ? 'Ready' :
                   modelInfo?.status === 'training' ? 'Training' :
                   'No Model'}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Current Model</span>
                <span className="text-sm font-bold text-green-600 truncate max-w-[120px]" title={modelInfo?.version || 'None'}>
                  {modelInfo?.version !== 'No model' ? modelInfo?.version || 'None' : 'None'}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Trained Samples</span>
                <span className="text-lg font-bold text-orange-600">{modelInfo?.samples_trained || 0}</span>
              </div>
              {modelInfo && modelInfo.accuracy > 0 && (
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Accuracy</span>
                  <span className="text-lg font-bold text-purple-600">{Math.round(modelInfo.accuracy * 100)}%</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
