@echo off
title Masalit AI Platform - Development Mode
color 0A

echo.
echo ========================================================
echo  Masalit AI Platform - Development Mode (Windows 11)
echo ========================================================
echo.

REM Create logs directory if it doesn't exist
if not exist "logs" mkdir logs

echo [1/3] Starting Backend (FastAPI)...
echo Backend will be available at: http://127.0.0.1:8000
echo API Documentation: http://127.0.0.1:8000/docs
echo.

REM Start backend in a new window
start "Masalit AI - Backend" cmd /k "python start-backend.py && echo Backend stopped. Press any key to close... && pause"

REM Wait for backend to start
echo Waiting for backend to initialize...
timeout /t 5 /nobreak > nul

echo [2/3] Starting Frontend (Next.js)...
echo Frontend will be available at: http://localhost:3000
echo.

REM Check if pnpm is available, otherwise use npm
where pnpm >nul 2>nul
if %errorlevel% == 0 (
    echo Using pnpm...
    start "Masalit AI - Frontend" cmd /k "pnpm dev && echo Frontend stopped. Press any key to close... && pause"
) else (
    echo Using npm...
    start "Masalit AI - Frontend" cmd /k "npm run dev && echo Frontend stopped. Press any key to close... && pause"
)

REM Wait for frontend to start
echo Waiting for frontend to initialize...
timeout /t 3 /nobreak > nul

echo [3/3] Development Environment Ready!
echo.
echo ========================================================
echo  🚀 DEVELOPMENT SERVICES RUNNING
echo ========================================================
echo  Frontend:     http://localhost:3000
echo  Backend:      http://127.0.0.1:8000
echo  API Docs:     http://127.0.0.1:8000/docs
echo  Health Check: http://127.0.0.1:8000/health
echo ========================================================
echo.
echo 📝 Logs are saved in the 'logs' directory
echo 🔧 Both services are running in separate windows
echo 🛑 Close the service windows to stop the servers
echo.
echo Press any key to open the application in your browser...
pause > nul

REM Open the application in default browser
start http://localhost:3000

echo.
echo ✅ Development environment is ready!
echo    Frontend opened in your default browser.
echo.
echo Press any key to exit this window...
pause > nul
