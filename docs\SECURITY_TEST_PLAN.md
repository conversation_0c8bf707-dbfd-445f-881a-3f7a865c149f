# Security Test Plan

This document outlines the security measures implemented and test scenarios to verify that regular users cannot access admin functionality.

## Security Measures Implemented

### 1. Next.js Middleware (Root Level)
- **File**: `middleware.ts`
- **Protection**: Application-level route protection
- **Features**:
  - Blocks unauthenticated access to protected routes
  - Prevents non-admin users from accessing admin routes
  - Returns 403 for API endpoints, redirects for pages
  - Adds security headers

### 2. Client-Side Navigation Guards
- **File**: `components/navigation-guard.tsx`
- **Protection**: Component-level access control
- **Features**:
  - AdminGuard for admin-only components
  - AuthGuard for authenticated-only components
  - Automatic redirects for unauthorized access
  - Loading states during verification

### 3. Server-Side Authentication
- **File**: `lib/server-auth.ts`
- **Protection**: Server-side verification utilities
- **Features**:
  - requireAuth() for general authentication
  - requireAdmin() for admin-only pages
  - getUserById() for API route verification

### 4. API Endpoint Security
- **Files**: Various API routes
- **Protection**: Individual endpoint authentication
- **Features**:
  - Session cookie verification
  - Role-based access control
  - User status checks (disabled accounts)

## Test Scenarios

### Test 1: Unauthenticated Access
**Objective**: Verify that unauthenticated users cannot access protected resources

**Test Steps**:
1. Open browser in incognito mode
2. Try to access `/dashboard/admin`
3. Try to access `/dashboard/ai`
4. Try to access `/api/users`
5. Try to access `/api/admin/migrate-timestamps`

**Expected Results**:
- All requests should redirect to `/auth/signin` or return 401/403
- No admin content should be visible

### Test 2: Regular User Access
**Objective**: Verify that regular users cannot access admin functionality

**Test Steps**:
1. Log in as a regular user (role: 'user')
2. Try to access `/dashboard/admin`
3. Try to access `/dashboard/ai`
4. Try to make API calls to admin endpoints
5. Try to modify user roles via API

**Expected Results**:
- Admin pages should redirect to `/dashboard?error=admin-required`
- API calls should return 403 Forbidden
- No admin functionality should be accessible

### Test 3: Disabled User Access
**Objective**: Verify that disabled users cannot access any functionality

**Test Steps**:
1. Set a user's `isDisabled` field to `true` in Firestore
2. Try to log in with that user
3. If already logged in, try to access any protected route

**Expected Results**:
- Login should fail with account disabled message
- Existing sessions should be terminated
- All protected routes should be inaccessible

### Test 4: Direct URL Manipulation
**Objective**: Verify that direct URL access is blocked

**Test Steps**:
1. Log in as regular user
2. Manually type admin URLs in address bar:
   - `/dashboard/admin`
   - `/dashboard/ai`
   - `/dashboard/admin/users`
3. Use browser developer tools to make direct API calls

**Expected Results**:
- All admin URLs should redirect or show access denied
- API calls should return 403 status codes

### Test 5: Session Manipulation
**Objective**: Verify that session tampering is detected

**Test Steps**:
1. Log in as regular user
2. Use browser developer tools to inspect session cookies
3. Try to modify session data
4. Try to access admin routes

**Expected Results**:
- Modified sessions should be rejected
- User should be redirected to sign in
- No admin access should be granted

### Test 6: Role Escalation Prevention
**Objective**: Verify that users cannot escalate their own privileges

**Test Steps**:
1. Log in as regular user
2. Try to update own role via API: `PATCH /api/users/{userId}` with `{"role": "admin"}`
3. Try to use admin API endpoints after role change attempt

**Expected Results**:
- Role change should be rejected (400 error: "Cannot change your own role")
- User should remain with 'user' role
- Admin endpoints should remain inaccessible

## Automated Testing Commands

### Frontend Security Tests
```bash
# Test middleware protection
curl -X GET http://localhost:3000/dashboard/admin
# Expected: Redirect to signin

# Test API protection
curl -X GET http://localhost:3000/api/users
# Expected: 401 Unauthorized

# Test admin API protection
curl -X POST http://localhost:3000/api/admin/migrate-timestamps
# Expected: 401 Unauthorized
```

### Manual Testing Checklist

- [ ] Unauthenticated users cannot access `/dashboard/admin`
- [ ] Unauthenticated users cannot access `/dashboard/ai`
- [ ] Regular users cannot access admin pages
- [ ] Regular users cannot call admin APIs
- [ ] Disabled users cannot access any protected routes
- [ ] Direct URL manipulation is blocked
- [ ] Session tampering is detected
- [ ] Users cannot escalate their own roles
- [ ] All admin API endpoints require authentication
- [ ] All admin API endpoints verify admin role

## Security Headers Verification

The middleware adds the following security headers:
- `X-Frame-Options: DENY`
- `X-Content-Type-Options: nosniff`
- `Referrer-Policy: strict-origin-when-cross-origin`
- `X-XSS-Protection: 1; mode=block`

Test these headers are present:
```bash
curl -I http://localhost:3000/dashboard
```

## Notes

- All security measures work in layers (defense in depth)
- Client-side guards are for UX, server-side checks are for security
- Middleware provides the first line of defense
- API endpoints have individual authentication checks
- User role verification happens on every request
- Session cookies are verified server-side for all protected routes
