#!/usr/bin/env node

/**
 * Command-line script to migrate timestamps in Firestore
 * 
 * Usage:
 *   node scripts/migrate-timestamps.js --dry-run
 *   node scripts/migrate-timestamps.js --execute
 *   node scripts/migrate-timestamps.js --execute --batch-size=100
 */

const { program } = require('commander')

program
  .name('migrate-timestamps')
  .description('Migrate Firestore timestamps to ISO 8601 format')
  .option('-d, --dry-run', 'Run without making changes (preview mode)')
  .option('-e, --execute', 'Execute the migration')
  .option('-b, --batch-size <number>', 'Batch size for processing', '50')
  .option('-v, --verbose', 'Verbose output')
  .parse()

const options = program.opts()

if (!options.dryRun && !options.execute) {
  console.error('❌ Error: You must specify either --dry-run or --execute')
  console.log('\nUsage:')
  console.log('  node scripts/migrate-timestamps.js --dry-run     # Preview changes')
  console.log('  node scripts/migrate-timestamps.js --execute    # Execute migration')
  process.exit(1)
}

async function runMigration() {
  try {
    console.log('🚀 Starting timestamp migration...')
    console.log(`📋 Mode: ${options.dryRun ? 'DRY RUN' : 'EXECUTE'}`)
    console.log(`📦 Batch size: ${options.batchSize}`)
    console.log(`🔍 Verbose: ${options.verbose ? 'Yes' : 'No'}`)
    console.log('')

    // Determine the base URL
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
    const url = `${baseUrl}/api/admin/migrate-timestamps`

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        dryRun: options.dryRun,
        batchSize: parseInt(options.batchSize),
        verbose: options.verbose
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const result = await response.json()

    if (!result.success) {
      throw new Error(result.error || 'Migration failed')
    }

    console.log('✅ Migration completed successfully!')
    console.log('')
    console.log('📊 Results:')
    console.log(`   Total documents: ${result.result.totalProcessed}`)
    console.log(`   Updated: ${result.result.updated}`)
    console.log(`   Errors: ${result.result.errors.length}`)
    console.log('')
    console.log('📋 Details:')
    console.log(`   Main documents: ${result.result.details.mainDocuments}`)
    console.log(`   Transcriptions: ${result.result.details.transcriptions}`)
    console.log(`   Metadata: ${result.result.details.metadata}`)
    console.log(`   Reviews: ${result.result.details.reviews}`)
    console.log(`   Training: ${result.result.details.training}`)
    console.log(`   Analytics: ${result.result.details.analytics}`)

    if (result.result.errors.length > 0) {
      console.log('')
      console.log('❌ Errors:')
      result.result.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`)
      })
    }

    if (options.dryRun) {
      console.log('')
      console.log('💡 This was a dry run - no changes were made.')
      console.log('   Run with --execute to apply the changes.')
    }

  } catch (error) {
    console.error('❌ Migration failed:', error.message)
    process.exit(1)
  }
}

// Check if we're running in a Node.js environment with fetch
if (typeof fetch === 'undefined') {
  console.log('Installing node-fetch for Node.js compatibility...')
  try {
    const fetch = require('node-fetch')
    global.fetch = fetch
  } catch (e) {
    console.error('❌ Error: node-fetch is required for this script.')
    console.log('Install it with: npm install node-fetch')
    process.exit(1)
  }
}

runMigration()
