# Masalit AI Platform - Firebase Collections Structure

This file contains the NEW HIERARCHICAL structure of the Firestore collections used in the Masalit AI Platform.
Updated: 2025-06-18 23:52:24

## 📁 Hierarchical Audio Collection

### Structure:
```
📁 audio/
  └── {audio_id}/                    # e.g., audio_1747011480759
      ├── (main document)            # Core audio metadata
      ├── transcriptions/
      │   └── primary/               # Main transcription
      ├── metadata/
      │   └── details/               # Audio metadata
      ├── review/
      │   └── status/                # Review status
      ├── training/
      │   └── status/                # Unified training status
      └── analytics/
          └── metrics/               # Usage metrics
```

### Example Document: audio/audio_1747011480759/

**Main Document:**
```
id: "audio_1747011480759"
title: "Masalit Audio Sample"
audio_url: "gs://masalit-ai.appspot.com/audio/..."
duration: 15.5
format: "wav"
created_at: 2025-06-18T...
user_id: "GkFeiG7JyIg3Ru7EnnWlMLxCp323"
source: "direct_upload"
```

**transcriptions/primary:**
```
content: "Sîgâl gi ŋgârîyê? Gi amara ye. Amara tii ŋgonndaaye?..."
language: "masalit"
transcription_source: "human/manual"
type: "txt"
speaker_count: 1
created_at: 2025-06-18T...
```

**metadata/details:**
```
gender: "male"
language: "masalit"
dialect: null
quality_rating: 4.5
recording_context: "studio"
```

**review/status:**
```
action: "approved"
reviewed_by: "admin_user_id"
reviewed_at: 2025-06-18T...
feedback: "Good quality recording"
is_flagged: false
```

**training/status:** (UNIFIED for audio + transcription)
```
trained_asr: true
tts_trained: false
training_sessions: ["asr_session_20250618_001"]
last_trained_at: 2025-06-18T...
note: "Audio and transcription training status unified"
```

**analytics/metrics:**
```
play_count: 15
download_count: 3
validation_uses: 2
last_accessed: 2025-06-18T...
```

## 📁 Hierarchical Users Collection

### Structure:
```
📁 users/
  └── {user_id}/                     # e.g., GkFeiG7JyIg3Ru7EnnWlMLxCp323
      ├── (main document)            # Core user info
      ├── profile/
      │   └── details/               # Extended profile
      ├── statistics/
      │   └── summary/               # User statistics
      ├── preferences/
      │   └── settings/              # User preferences
      └── security/
          └── status/                # Security settings
```

### Example Document: users/GkFeiG7JyIg3Ru7EnnWlMLxCp323/

**Main Document:**
```
email: "<EMAIL>"
name: "John Doe"
username: "johndoe"
role: "user"
created_at: 2025-06-18T...
```

**profile/details:**
```
avatar_url: "https://..."
bio: "Masalit language contributor"
location: "Sudan"
language_preferences: ["masalit", "en"]
email_verified: true
phone_verified: false
```

**statistics/summary:**
```
contribution_count: 25
audio_uploads: 15
transcriptions_created: 10
reviews_completed: 5
training_sessions: 2
total_audio_duration: 450.5
last_activity: 2025-06-18T...
```

**preferences/settings:**
```
theme: "light"
language: "en"
notifications: {
  email: true,
  push: true,
  training_updates: true
}
privacy: {
  profile_public: false,
  stats_public: false
}
```

**security/status:**
```
last_login: 2025-06-18T...
login_count: 45
failed_login_attempts: 0
account_locked: false
two_factor_enabled: false
is_disabled: false
```

## 📁 Other Collections (Clean Structure)

### settings/
```
📁 settings/
  ├── asr_training/          # ASR training configuration
  ├── asr_validation/        # ASR validation configuration
  ├── tts_training/          # TTS training configuration
  └── system/                # System settings
```

### validation/
```
📁 validation/
  └── {validation_id}/
      ├── (main document)    # Basic validation info
      ├── config/
      │   └── settings       # Validation configuration
      ├── progress/
      │   └── current        # Real-time progress
      └── results/
          └── final          # Final validation results
```

### training/
```
📁 training/
  └── {session_id}/
      ├── (main document)    # Basic training info
      ├── config/
      │   └── settings       # Training configuration
      ├── progress/
      │   └── current        # Real-time progress
      └── results/
          └── final          # Final training results
```

### system/
```
📁 system/
  └── config/
      └── settings/
          ├── logging        # Log configuration
          ├── security       # Security settings
          ├── performance    # Performance settings
          └── features       # Feature toggles
```

### logs/
```
📁 logs/
  ├── system/
  │   └── entries/           # System log entries
  ├── training/
  │   └── entries/           # Training log entries
  └── validation/
      └── entries/           # Validation log entries
```

### monitoring/
```
📁 monitoring/
  ├── health                 # System health data
  ├── metrics               # Performance metrics
  └── alerts                # System alerts
```

## 🎯 Key Benefits of New Structure

1. **Single Query Performance**: Get audio + transcription + training status in one call
2. **Unified Training Status**: Audio and transcription training managed together (no duplication)
3. **Hierarchical Organization**: Related data grouped logically in subcollections
4. **Better Caching**: Logical data boundaries improve caching efficiency
5. **Future-Ready**: Easy to extend with new features (alternative transcriptions, AI-generated content)
6. **Clean Collection Names**: No "_v2" or temporary suffixes
7. **Consistent Patterns**: All collections follow the same hierarchical approach

## 🔄 Migration Completed

- ✅ All 35 audio records migrated to hierarchical structure
- ✅ All 35 transcriptions moved to audio subcollections
- ✅ All 4 user records migrated to hierarchical structure
- ✅ Training status unified (single source of truth)
- ✅ Old collections cleaned up
- ✅ Collection names cleaned (no "_v2" suffixes)

This structure provides a solid foundation for the Masalit AI Platform's continued growth and development.
