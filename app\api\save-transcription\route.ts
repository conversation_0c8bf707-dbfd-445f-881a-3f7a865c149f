import { NextRequest, NextResponse } from "next/server"
import { db } from "@/lib/firebase-admin"
import { authOptions } from "@/lib/auth"
import { getServerSession } from "next-auth"
import { FieldValue } from "firebase-admin/firestore"

export async function POST(request: NextRequest) {
  try {
    // Get the current user's ID from the session
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }
    const userId = session.user.id

    // Get form data
    const formData = await request.formData()
    const audioUrl = formData.get("audioUrl") as string
    const title = formData.get("title") as string
    const duration = parseInt(formData.get("duration") as string)
    const transcriptionText = formData.get("transcriptionText") as string
    const transcriptionFile = formData.get("transcriptionFile") as File | null

    if (!audioUrl || !title || !duration) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      )
    }

    if (!transcriptionText && !transcriptionFile) {
      return NextResponse.json(
        { error: "Transcription text or file is required" },
        { status: 400 }
      )
    }

    // Create transcription document
    const transcriptionData = {
      userId,
      audioUrl,
      title,
      duration,
      transcriptionText: transcriptionText || null,
      transcriptionFileUrl: null, // Will be updated if file is uploaded
      createdAt: FieldValue.serverTimestamp(),
      updatedAt: FieldValue.serverTimestamp()
    }

    // Add document to Firestore
    const docRef = await db.collection("transcriptions").add(transcriptionData)

    return NextResponse.json({
      id: docRef.id,
      ...transcriptionData
    })
  } catch (error) {
    console.error("Error saving transcription:", error)
    return NextResponse.json(
      { error: "Failed to save transcription" },
      { status: 500 }
    )
  }
} 