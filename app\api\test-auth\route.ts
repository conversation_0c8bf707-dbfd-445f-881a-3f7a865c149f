import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { auth, db } from '@/lib/firebase-admin'

export async function GET(request: NextRequest) {
  try {
    // Get session cookie
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get('session')?.value

    if (!sessionCookie) {
      return NextResponse.json({ 
        error: 'No session cookie found',
        step: 'session_cookie_check'
      }, { status: 401 })
    }

    // Verify the session cookie
    let decodedClaims
    try {
      decodedClaims = await auth.verifySessionCookie(sessionCookie, true)
    } catch (error) {
      return NextResponse.json({ 
        error: 'Invalid session cookie',
        step: 'session_verification',
        details: error.message
      }, { status: 401 })
    }

    const currentUserId = decodedClaims.uid

    // Test Firestore access
    let currentUserDoc
    try {
      currentUserDoc = await db.collection('users').doc(currentUserId).get()
    } catch (error) {
      return NextResponse.json({
        error: 'Firestore access failed',
        step: 'firestore_access',
        details: error.message
      }, { status: 500 })
    }

    if (!currentUserDoc.exists) {
      return NextResponse.json({
        error: 'User document not found',
        step: 'user_document_check',
        userId: currentUserId
      }, { status: 404 })
    }

    const currentUserData = currentUserDoc.data()

    // Test subcollection access
    let preferencesDoc
    try {
      preferencesDoc = await db.collection('users').doc(currentUserId).collection('preferences').doc('settings').get()
    } catch (error) {
      return NextResponse.json({
        error: 'Preferences subcollection access failed',
        step: 'preferences_access',
        details: error.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      user: {
        id: currentUserId,
        email: currentUserData.email,
        role: currentUserData.role
      },
      preferences: {
        exists: preferencesDoc.exists,
        data: preferencesDoc.exists ? preferencesDoc.data() : null
      },
      steps_completed: [
        'session_cookie_check',
        'session_verification', 
        'firestore_access',
        'user_document_check',
        'preferences_access'
      ]
    })

  } catch (error) {
    console.error('Test auth error:', error)
    return NextResponse.json({ 
      error: 'Unexpected error',
      step: 'unknown',
      details: error.message,
      stack: error.stack
    }, { status: 500 })
  }
}
