import { NextRequest, NextResponse } from 'next/server'
import { doc, setDoc, getDoc } from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { cookies } from 'next/headers'
import { auth } from '@/lib/firebase-admin'

export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get('session')?.value

    if (!sessionCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the session cookie
    const decodedClaims = await auth.verifySessionCookie(sessionCookie, true)
    const userId = decodedClaims.uid

    // Get user data from Firestore to check role
    const userDoc = await getDoc(doc(db, 'users', userId))
    if (!userDoc.exists()) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const userData = userDoc.data()
    if (userData.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden: Admin access required' }, { status: 403 })
    }

    // Get settings from request body
    const settings = await request.json()

    // Note: Settings and status are now handled by the backend using clean Firebase structure
    // The backend will update settings to 'settings/asr_training' and create training sessions
    // in 'training/{session_id}' with progress tracking in subcollections

    try {
      // Make request to backend service using environment variable
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL
      if (!backendUrl) {
        throw new Error('Backend URL not configured')
      }

      const response = await fetch(`${backendUrl}/api/asr/training/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      })

      if (!response.ok) {
        let errorMessage = 'Failed to start training'
        try {
          const errorData = await response.json()
          errorMessage = errorData.detail || errorData.error || errorMessage
        } catch (e) {
          // If response is not JSON, use status text
          errorMessage = response.statusText || errorMessage
        }
        throw new Error(errorMessage)
      }

      const data = await response.json()
      return NextResponse.json({ 
        message: 'Training started successfully',
        status: 'started',
        data
      })
    } catch (error: any) {
      // Log error (status updates are handled by backend)
      console.error('Backend training request failed:', error)
      throw error
    }

  } catch (error: any) {
    console.error('Error starting training:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to start training' },
      { status: 500 }
    )
  }
} 