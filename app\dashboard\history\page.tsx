'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { listUserAudio, deleteAudioRecord, updateAudioRecord } from "@/lib/audio-api-v2"
import { useAuth } from "@/components/auth-provider"
import { useToast } from "@/hooks/use-toast"

import {
  Search,
  Filter,
  Download,
  Trash2,
  Edit,
  Play,
  Pause,
  CheckCircle,
  XCircle,
  Clock,
  Save,
  X,
  BarChart3,
  FileAudio,
  TrendingUp,
  Calendar
} from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"

interface AudioRecord {
  id: string
  title: string
  audio_url: string
  duration: number
  format: string
  created_at: string
  user_id: string
  source: string

  // Hierarchical data (flattened for UI)
  transcription_content?: string
  gender?: string
  language?: string
  action?: string
  approved?: number
  trained_asr?: boolean
  tts_trained?: boolean

  // Subcollections (loaded on demand)
  transcriptions?: {
    primary?: {
      content: string
      language: string
      transcription_source: string
      created_at: string
      type: string
      speaker_count: number
    }
  }
  metadata?: {
    details?: {
      gender: string
      language: string
      recording_context?: string
    }
  }
  review?: {
    status?: {
      action: 'pending' | 'approved' | 'rejected'
      reviewed_by?: string
      reviewed_at?: string
      feedback?: string
      is_flagged: boolean
    }
  }
  training?: {
    status?: {
      trained_asr: boolean
      tts_trained: boolean
      training_sessions: string[]
      last_trained_at?: string
    }
  }
}

export default function MyRecordingsPage() {
  const { user } = useAuth()
  const { toast } = useToast()

  const [recordings, setRecordings] = useState<AudioRecord[]>([])
  const [filteredRecordings, setFilteredRecordings] = useState<AudioRecord[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [sourceFilter, setSourceFilter] = useState("all")
  const [sortBy, setSortBy] = useState("created_at")
  const [sortOrder, setSortOrder] = useState("desc")
  const [editingRecording, setEditingRecording] = useState<AudioRecord | null>(null)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [recordingToDelete, setRecordingToDelete] = useState<AudioRecord | null>(null)
  const [showSuccessDialog, setShowSuccessDialog] = useState(false)
  const [playingAudio, setPlayingAudio] = useState<string | null>(null)


  // Statistics
  const totalRecordings = recordings.length
  const approvedRecordings = recordings.filter(r => r.action === 'approved').length
  const pendingRecordings = recordings.filter(r => r.action === 'pending').length
  const rejectedRecordings = recordings.filter(r => r.action === 'rejected').length
  const totalDuration = recordings.reduce((sum, r) => sum + r.duration, 0)

  useEffect(() => {
    if (user) {
      loadRecordings()
    }
  }, [user])

  useEffect(() => {
    filterAndSortRecordings()
  }, [recordings, searchTerm, statusFilter, sourceFilter, sortBy, sortOrder])



  const loadRecordings = async () => {
    try {
      setLoading(true)
      console.log('Loading user recordings...')
      const data = await listUserAudio(user!.id, { limit: 50 })

      // Transform hierarchical data to flat structure for UI compatibility
      const transformedData: AudioRecord[] = data.map(item => ({
        ...item,
        // Flatten transcription data
        transcription_content: item.transcriptions?.primary?.content || '',
        // Flatten metadata
        gender: item.metadata?.details?.gender || '',
        language: item.metadata?.details?.language || 'masalit',
        // Flatten review status
        action: item.review?.status?.action || 'pending',
        approved: item.review?.status?.action === 'approved' ? 1 : 0,
        // Flatten training status
        trained_asr: item.training?.status?.trained_asr || false,
        tts_trained: item.training?.status?.tts_trained || false,
      }))

      setRecordings(transformedData)
    } catch (error) {
      console.error('Error loading recordings:', error)
      toast({
        title: "Error",
        description: "Failed to load recordings",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const filterAndSortRecordings = () => {
    let filtered = recordings

    if (searchTerm) {
      filtered = filtered.filter(recording =>
        recording.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        recording.transcription_content?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (statusFilter !== "all") {
      filtered = filtered.filter(recording => recording.action === statusFilter)
    }

    if (sourceFilter !== "all") {
      filtered = filtered.filter(recording => recording.source === sourceFilter)
    }

    // Sort recordings
    filtered.sort((a, b) => {
      let aValue: any = a[sortBy as keyof AudioRecord]
      let bValue: any = b[sortBy as keyof AudioRecord]

      // Handle date sorting
      if (sortBy === 'created_at') {
        aValue = new Date(aValue || 0).getTime()
        bValue = new Date(bValue || 0).getTime()
      }

      // Handle string sorting
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    setFilteredRecordings(filtered)
  }

  const handleSaveEdit = async () => {
    if (!editingRecording) return

    try {
      await updateAudioRecord(editingRecording.id, {
        title: editingRecording.title,
        transcription_content: editingRecording.transcription_content
      })

      setRecordings(prev => prev.map(r =>
        r.id === editingRecording.id ? editingRecording : r
      ))

      setEditingRecording(null)
      toast({
        title: "Success",
        description: "Recording updated successfully",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update recording",
        variant: "destructive",
      })
    }
  }

  const handleDelete = (recording: AudioRecord) => {
    setRecordingToDelete(recording)
    setShowDeleteDialog(true)
  }

  const confirmDelete = async () => {
    if (!recordingToDelete) return

    try {
      await deleteAudioRecord(recordingToDelete.id)
      setRecordings(prev => prev.filter(r => r.id !== recordingToDelete.id))
      setShowDeleteDialog(false)
      setRecordingToDelete(null)
      toast({
        title: "Success",
        description: "Recording deleted successfully",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete recording",
        variant: "destructive",
      })
    }
  }

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A'

    const date = new Date(dateString)
    if (isNaN(date.getTime())) return 'Invalid Date'

    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const toggleAudio = (audioUrl: string) => {
    if (playingAudio === audioUrl) {
      setPlayingAudio(null)
    } else {
      setPlayingAudio(audioUrl)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Loading your recordings...</div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">My Recordings</h1>
          <p className="text-muted-foreground">
            Manage and review your audio submissions
          </p>
        </div>

      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Recordings</CardTitle>
            <FileAudio className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalRecordings}</div>
            <p className="text-xs text-muted-foreground">
              {totalDuration > 0 && `${Math.round(totalDuration / 60)} minutes total`}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{approvedRecordings}</div>
            <p className="text-xs text-muted-foreground">
              Ready for training
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{pendingRecordings}</div>
            <p className="text-xs text-muted-foreground">
              Under review
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rejected</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{rejectedRecordings}</div>
            <p className="text-xs text-muted-foreground">
              Need revision
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Search and Filter</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search recordings..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>

            <Select value={sourceFilter} onValueChange={setSourceFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by source" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Sources</SelectItem>
                <SelectItem value="direct_upload">Direct Upload</SelectItem>
                <SelectItem value="bulk_upload">Bulk Upload</SelectItem>
                <SelectItem value="recording">Recording</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex flex-col sm:flex-row gap-4">
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created_at">Date Created</SelectItem>
                <SelectItem value="title">Title</SelectItem>
                <SelectItem value="duration">Duration</SelectItem>
                <SelectItem value="action">Status</SelectItem>
              </SelectContent>
            </Select>

            <Select value={sortOrder} onValueChange={setSortOrder}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Order" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="desc">Newest First</SelectItem>
                <SelectItem value="asc">Oldest First</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Recordings List */}
      <Card>
        <CardHeader>
          <CardTitle>Your Recordings ({filteredRecordings.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredRecordings.map((recording) => (
              <div key={recording.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <h3 className="font-medium">{recording.title}</h3>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <span>{formatDuration(recording.duration)}</span>
                      <span>{formatDate(recording.created_at)}</span>
                      <Badge variant={
                        recording.action === 'approved' ? 'default' :
                        recording.action === 'rejected' ? 'destructive' :
                        'secondary'
                      }>
                        {recording.action}
                      </Badge>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => toggleAudio(recording.audio_url)}
                    >
                      {playingAudio === recording.audio_url ? (
                        <Pause className="h-4 w-4" />
                      ) : (
                        <Play className="h-4 w-4" />
                      )}
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setEditingRecording({ ...recording })}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDelete(recording)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {recording.transcription_content && (
                  <div className="text-sm text-muted-foreground bg-muted p-3 rounded">
                    {recording.transcription_content}
                  </div>
                )}
              </div>
            ))}

            {filteredRecordings.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                No recordings found. Start by uploading your first recording!
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Recording</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{recordingToDelete?.title}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-red-600 hover:bg-red-700">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Edit Dialog */}
      {editingRecording && (
        <Dialog open={!!editingRecording} onOpenChange={() => setEditingRecording(null)}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Edit Recording</DialogTitle>
              <DialogDescription>
                Update the title and transcription for your recording.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Title</label>
                <Input
                  value={editingRecording.title}
                  onChange={(e) => setEditingRecording({
                    ...editingRecording,
                    title: e.target.value
                  })}
                />
              </div>
              <div>
                <label className="text-sm font-medium">Transcription</label>
                <Textarea
                  value={editingRecording.transcription_content || ''}
                  onChange={(e) => setEditingRecording({
                    ...editingRecording,
                    transcription_content: e.target.value
                  })}
                  rows={6}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setEditingRecording(null)}>
                Cancel
              </Button>
              <Button onClick={handleSaveEdit}>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Audio Player */}
      {playingAudio && (
        <div className="fixed bottom-4 right-4 bg-background border rounded-lg p-4 shadow-lg">
          <audio
            src={playingAudio}
            controls
            autoPlay
            onEnded={() => setPlayingAudio(null)}
            className="w-64"
          />
        </div>
      )}
    </div>
  )
}
