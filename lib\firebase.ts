import { initializeApp } from "firebase/app";
import { getAnalytics, isSupported } from "firebase/analytics";
import { getAuth, connectAuthEmulator } from "firebase/auth";
import { getFirestore, initializeFirestore, persistentLocalCache, persistentMultipleTabManager, memoryLocalCache, connectFirestoreEmulator } from "firebase/firestore";
import { getStorage, connectStorageEmulator } from "firebase/storage";

// Check if we're in development mode
const isDevelopment = process.env.NODE_ENV === 'development'
const useEmulator = isDevelopment && process.env.NEXT_PUBLIC_USE_FIREBASE_EMULATOR === 'true'

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || "kana-masark-255aa.firebasestorage.app",
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

// Initialize Firestore with appropriate cache based on environment
const db = initializeFirestore(app, {
  localCache: typeof window !== 'undefined'
    ? persistentLocalCache({
        tabManager: persistentMultipleTabManager()
      })
    : memoryLocalCache()
});

// Initialize Storage with explicit bucket
const storage = getStorage(app, firebaseConfig.storageBucket);

// Connect to emulators in development if enabled
if (useEmulator && typeof window !== 'undefined') {
  try {
    // Connect to Auth emulator
    if (!auth.config.emulator) {
      connectAuthEmulator(auth, "http://127.0.0.1:9099", { disableWarnings: true });
    }

    // Connect to Firestore emulator
    if (!db._delegate._databaseId.projectId.includes('localhost')) {
      connectFirestoreEmulator(db, '127.0.0.1', 8080);
    }

    // Connect to Storage emulator
    if (!storage._location.bucket.includes('localhost')) {
      connectStorageEmulator(storage, "127.0.0.1", 9199);
    }

    console.log('🔧 Connected to Firebase emulators for development');
  } catch (error) {
    console.warn('⚠️ Could not connect to Firebase emulators:', error);
  }
}

// Initialize Analytics only on the client side
let analytics = null;
if (typeof window !== 'undefined') {
  // Check if analytics is supported in this environment
  isSupported().then(yes => {
    if (yes) {
      analytics = getAnalytics(app);
    }
  });
}

export { app, analytics, auth, db, storage }; 