import { db } from './firebase';
import { doc, setDoc, collection } from 'firebase/firestore';

export async function initializeDatabase() {
  try {
    // Initialize hierarchical audio collection
    // Structure: audio/{audio_id}/transcriptions/primary/
    const audioRef = collection(db, 'audio');

    // Initialize hierarchical users collection
    // Structure: users/{user_id}/profile/details/, statistics/summary/, etc.
    const usersRef = collection(db, 'users');

    // Initialize other clean collections
    const settingsRef = collection(db, 'settings');
    const validationRef = collection(db, 'validation');
    const trainingRef = collection(db, 'training');

    console.log('Database collections initialized successfully with hierarchical structure');
    return true;
  } catch (error) {
    console.error('Error initializing database:', error);
    throw error;
  }
}

// Function to create a user document if it doesn't exist
export async function createUserIfNotExists(userId: string) {
  try {
    const userRef = doc(db, 'users', userId);
    await setDoc(userRef, {
      contribution_count: 0,
      created_at: new Date(),
      updated_at: new Date()
    }, { merge: true });
    
    return true;
  } catch (error) {
    console.error('Error creating user:', error);
    throw error;
  }
} 