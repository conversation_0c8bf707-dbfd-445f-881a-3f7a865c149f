import { NextResponse } from 'next/server';
import { auth } from '@/lib/firebase-admin';
import { db } from '@/lib/firebase-admin';
import { ActionCodeSettings } from 'firebase-admin/auth';

export async function POST(request: Request) {
  try {
    const { email, ip_address, user_agent } = await request.json();
    console.log('Starting email verification process for:', email);

    if (!email) {
      console.error('Email is missing from request');
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Create custom action code settings
    const actionCodeSettings: ActionCodeSettings = {
      url: `${process.env.NEXT_PUBLIC_APP_URL}/auth/verify-email`,
      handleCodeInApp: true,
    };

    console.log('Generating verification link with settings:', {
      url: actionCodeSettings.url,
      handleCodeInApp: actionCodeSettings.handleCodeInApp
    });

    // Generate verification email
    const link = await auth.generateEmailVerificationLink(email, actionCodeSettings);
    console.log('Verification link generated successfully');

    // Format device and location information
    const deviceInfo = user_agent ? `Device: ${user_agent}` : '';
    const locationInfo = ip_address ? `Location: ${ip_address}` : '';
    const securityInfo = [deviceInfo, locationInfo].filter(Boolean).join('\n');

    // Send email using Brevo API
    console.log('Sending email via Brevo API...');
    const response = await fetch('https://api.brevo.com/v3/smtp/email', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'api-key': process.env.BREVO_API_KEY || '',
      },
      body: JSON.stringify({
        sender: {
          name: 'Masalit Platform',
          email: '<EMAIL>',
        },
        to: [{ email }],
        subject: 'Verify your email address',
        htmlContent: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <h1 style="color: #4F46E5; text-align: center;">Welcome to Masalit Platform!</h1>
            <p style="font-size: 16px; line-height: 1.5; color: #374151;">Please verify your email address by clicking the button below:</p>
            <div style="text-align: center; margin: 30px 0;">
              <a href="${link}" style="display: inline-block; padding: 12px 24px; background-color: #4F46E5; color: white; text-decoration: none; border-radius: 4px; font-weight: bold;">Verify Email</a>
            </div>
            ${securityInfo ? `
            <div style="background-color: #F3F4F6; padding: 15px; border-radius: 4px; margin: 20px 0;">
              <h3 style="color: #1F2937; margin-top: 0;">Security Information</h3>
              <p style="color: #4B5563; margin: 0; white-space: pre-line;">${securityInfo}</p>
            </div>
            ` : ''}
            <p style="font-size: 14px; color: #6B7280; text-align: center;">If you didn't create an account, you can safely ignore this email.</p>
            <hr style="border: none; border-top: 1px solid #E5E7EB; margin: 20px 0;">
            <p style="font-size: 12px; color: #9CA3AF; text-align: center;">This is an automated message, please do not reply to this email.</p>
          </div>
        `,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      console.error('Brevo API error:', {
        status: response.status,
        statusText: response.statusText,
        error: errorData,
        apiKey: process.env.BREVO_API_KEY ? 'Present' : 'Missing',
        email: email,
        link: link
      });
      throw new Error('Failed to send verification email');
    }

    const responseData = await response.json();
    console.log('Email sent successfully:', {
      messageId: responseData.messageId,
      email: email
    });

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error sending verification email:', {
      error: error.message,
      stack: error.stack,
      email: error.email
    });
    return NextResponse.json(
      { error: error.message || 'Failed to send verification email' },
      { status: 500 }
    );
  }
} 