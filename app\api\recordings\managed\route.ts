import { NextResponse } from 'next/server'
import { db } from '@/lib/firebase'
import { collection, query, where, getDocs, doc as firestoreDoc, getDoc } from 'firebase/firestore'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status') || 'approved' // Default to approved
    const source = searchParams.get('source') || 'all'
    const userSearch = searchParams.get('userSearch') || ''
    
    // Build the base query - get approved or rejected recordings
    let q = query(collection(db, "audio"), where("action", "==", status))
    
    // Add source filter if specified
    if (source !== 'all') {
      q = query(q, where("source", "==", source))
    }
    
    // Get all recordings
    const querySnapshot = await getDocs(q)
    const recordings = []
    
    // Process each recording
    for (const doc of querySnapshot.docs) {
      const recording = doc.data()
      
      // Get user details
      let userDetails = {}
      if (recording.user_id) {
        const userDoc = await getDoc(firestoreDoc(db, "users", recording.user_id))
        if (userDoc.exists()) {
          const userData = userDoc.data()
          userDetails = {
            email: userData.email,
            username: userData.username,
            name: userData.name
          }
        }
      }

      // Get transcription details
      let transcriptionDetails = {}
      const transcriptionDoc = await getDocs(query(collection(db, "transcription"), where("audio_id", "==", doc.id)))
      if (!transcriptionDoc.empty) {
        const transcriptionData = transcriptionDoc.docs[0].data()
        transcriptionDetails = {
          content: transcriptionData.content,
          language: transcriptionData.language,
          status: transcriptionData.status
        }
      }
      
      // Apply user search filter if provided
      if (userSearch) {
        const email = userDetails.email?.toLowerCase() || ''
        const username = userDetails.username?.toLowerCase() || ''
        const searchTerm = userSearch.toLowerCase()
        
        if (!email.includes(searchTerm) && !username.includes(searchTerm)) {
          continue
        }
      }
      
      recordings.push({
        id: doc.id,
        ...recording,
        ...userDetails,
        ...transcriptionDetails,
        created_at: recording.created_at?.toDate(),
        updated_at: recording.updated_at?.toDate()
      })
    }
    
    // Sort recordings by creation date
    recordings.sort((a, b) => b.created_at - a.created_at)
    
    return NextResponse.json({ recordings })
  } catch (error) {
    console.error('Error fetching recordings:', error)
    return NextResponse.json(
      { error: 'Failed to fetch recordings' },
      { status: 500 }
    )
  }
} 