import { NextResponse } from 'next/server'
import { db } from '@/lib/firebase'
import { collection, query, where, getDocs, doc as firestoreDoc, getDoc } from 'firebase/firestore'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status') || 'approved' // Default to approved
    const source = searchParams.get('source') || 'all'
    const userSearch = searchParams.get('userSearch') || ''

    // Get all audio documents (we'll filter by review status from subcollections)
    let q = query(collection(db, "audio"))

    // Add source filter if specified (this is still on the main document)
    if (source !== 'all') {
      q = query(q, where("source", "==", source))
    }
    
    // Get all recordings
    const querySnapshot = await getDocs(q)
    const recordings = []
    
    // Process each recording with hierarchical structure
    for (const audioDoc of querySnapshot.docs) {
      const recording = audioDoc.data()
      const audioId = audioDoc.id

      try {
        // Check review status from subcollection
        const reviewStatusDoc = await getDoc(firestoreDoc(db, 'audio', audioId, 'review', 'status'))

        // Skip if no review status or doesn't match requested status
        if (!reviewStatusDoc.exists()) {
          continue
        }

        const reviewData = reviewStatusDoc.data()
        if (reviewData.action !== status) {
          continue
        }

        // Get user details
        let userDetails = {}
        if (recording.user_id) {
          const userDoc = await getDoc(firestoreDoc(db, "users", recording.user_id))
          if (userDoc.exists()) {
            const userData = userDoc.data()
            userDetails = {
              email: userData.email,
              username: userData.username,
              name: userData.name
            }
          }
        }

        // Get transcription details from subcollection
        let transcriptionDetails = {}
        const transcriptionDoc = await getDoc(firestoreDoc(db, 'audio', audioId, 'transcriptions', 'primary'))
        if (transcriptionDoc.exists()) {
          const transcriptionData = transcriptionDoc.data()
          transcriptionDetails = {
            content: transcriptionData.content,
            language: transcriptionData.language,
            confidence: transcriptionData.confidence,
            source: transcriptionData.source
          }
        }

        // Get metadata from subcollection
        let metadataDetails = {}
        const metadataDoc = await getDoc(firestoreDoc(db, 'audio', audioId, 'metadata', 'details'))
        if (metadataDoc.exists()) {
          const metadataData = metadataDoc.data()
          metadataDetails = {
            title: metadataData.title,
            dialect: metadataData.dialect,
            gender: metadataData.gender,
            age_group: metadataData.age_group,
            recording_context: metadataData.recording_context
          }
        }

        // Get training status from subcollection
        let trainingDetails = {}
        const trainingDoc = await getDoc(firestoreDoc(db, 'audio', audioId, 'training', 'status'))
        if (trainingDoc.exists()) {
          const trainingData = trainingDoc.data()
          trainingDetails = {
            trained_asr: trainingData.trained_asr,
            tts_trained: trainingData.tts_trained,
            last_trained_at: trainingData.last_trained_at
          }
        }

        // Apply user search filter if provided
        if (userSearch) {
          const email = userDetails.email?.toLowerCase() || ''
          const username = userDetails.username?.toLowerCase() || ''
          const searchTerm = userSearch.toLowerCase()

          if (!email.includes(searchTerm) && !username.includes(searchTerm)) {
            continue
          }
        }

        recordings.push({
          id: audioId,
          ...recording,
          ...userDetails,
          ...transcriptionDetails,
          ...metadataDetails,
          ...trainingDetails,
          review_status: reviewData,
          created_at: recording.created_at?.toDate(),
          updated_at: recording.updated_at?.toDate()
        })

      } catch (error) {
        console.warn(`Error processing audio ${audioId}:`, error)
        continue
      }
    }
    
    // Sort recordings by creation date
    recordings.sort((a, b) => b.created_at - a.created_at)
    
    return NextResponse.json({ recordings })
  } catch (error) {
    console.error('Error fetching recordings:', error)
    return NextResponse.json(
      { error: 'Failed to fetch recordings' },
      { status: 500 }
    )
  }
} 