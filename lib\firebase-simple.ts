// Simplified Firebase Service for Testing
import { 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  setDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  limit as firestoreLimit
} from "firebase/firestore"
import { db } from "@/lib/firebase"

// Simple audio creation without subcollections
export async function createSimpleAudio(audioData: {
  title: string
  duration: number
  format: string
  user_id: string
  source: string
  audio_url: string
  transcription_content: string
  gender: string
}): Promise<{ audio_id: string }> {
  const audio_id = `audio_${Date.now()}`
  const timestamp = new Date().toISOString()
  
  try {
    console.log('Creating simple audio:', audio_id)
    
    // Create a single document with all data
    const audioRef = doc(db, 'audio', audio_id)
    await setDoc(audioRef, {
      id: audio_id,
      title: audioData.title,
      audio_url: audioData.audio_url,
      duration: audioData.duration,
      format: audioData.format,
      created_at: timestamp,
      user_id: audioData.user_id,
      source: audioData.source,
      // Embedded data instead of subcollections
      transcription_content: audioData.transcription_content,
      gender: audioData.gender,
      language: "masalit",
      action: "pending",
      approved: 0,
      trained_asr: false,
      tts_trained: false
    })

    console.log('Simple audio created successfully:', audio_id)
    return { audio_id }
  } catch (error) {
    console.error('Error creating simple audio:', error)
    throw error
  }
}

// Simple list user audio
export async function listSimpleUserAudio(userId: string): Promise<any[]> {
  try {
    console.log('Listing simple user audio for:', userId)
    
    const audioQuery = query(
      collection(db, 'audio'),
      where('user_id', '==', userId),
      firestoreLimit(20)
    )

    const audioSnapshot = await getDocs(audioQuery)
    console.log('Found audio documents:', audioSnapshot.docs.length)
    
    const audioRecords = audioSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }))

    console.log('Returning audio records:', audioRecords.length)
    return audioRecords
  } catch (error) {
    console.error('Error listing simple user audio:', error)
    throw error
  }
}

// Simple get pending audio
export async function getSimplePendingAudio(): Promise<any[]> {
  try {
    console.log('Getting simple pending audio')
    
    const audioQuery = query(
      collection(db, 'audio'),
      where('action', '==', 'pending'),
      firestoreLimit(20)
    )

    const audioSnapshot = await getDocs(audioQuery)
    console.log('Found pending documents:', audioSnapshot.docs.length)
    
    const audioRecords = audioSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }))

    console.log('Returning pending records:', audioRecords.length)
    return audioRecords
  } catch (error) {
    console.error('Error getting simple pending audio:', error)
    throw error
  }
}

// Simple update audio
export async function updateSimpleAudio(audioId: string, updates: any): Promise<void> {
  try {
    console.log('Updating simple audio:', audioId, updates)
    
    const audioRef = doc(db, 'audio', audioId)
    await updateDoc(audioRef, updates)
    
    console.log('Audio updated successfully')
  } catch (error) {
    console.error('Error updating simple audio:', error)
    throw error
  }
}

// Simple delete audio
export async function deleteSimpleAudio(audioId: string): Promise<void> {
  try {
    console.log('Deleting simple audio:', audioId)
    
    const audioRef = doc(db, 'audio', audioId)
    await deleteDoc(audioRef)
    
    console.log('Audio deleted successfully')
  } catch (error) {
    console.error('Error deleting simple audio:', error)
    throw error
  }
}

// Simple list audio with filters
export async function listSimpleAudio(filters?: {
  status?: string
  source?: string
  userSearch?: string
}): Promise<any[]> {
  try {
    console.log('Listing simple audio with filters:', filters)

    let audioQuery = query(collection(db, 'audio'), firestoreLimit(50))

    // Apply status filter if provided
    if (filters?.status && filters.status !== 'all') {
      audioQuery = query(
        collection(db, 'audio'),
        where('action', '==', filters.status),
        firestoreLimit(50)
      )
    }

    const audioSnapshot = await getDocs(audioQuery)
    console.log('Found audio documents:', audioSnapshot.docs.length)

    let audioRecords = audioSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }))

    // Apply client-side filters
    if (filters?.source && filters.source !== 'all') {
      audioRecords = audioRecords.filter(record => record.source === filters.source)
    }

    if (filters?.userSearch) {
      const search = filters.userSearch.toLowerCase()
      audioRecords = audioRecords.filter(record =>
        (record.email || '').toLowerCase().includes(search) ||
        (record.username || '').toLowerCase().includes(search) ||
        (record.name || '').toLowerCase().includes(search)
      )
    }

    console.log('Returning filtered records:', audioRecords.length)
    return audioRecords
  } catch (error) {
    console.error('Error listing simple audio:', error)
    throw error
  }
}

// Bulk update audio records
export async function bulkUpdateAudio(audioIds: string[], updates: any): Promise<void> {
  try {
    console.log('Bulk updating audio:', audioIds.length, 'records')

    const promises = audioIds.map(audioId => {
      const audioRef = doc(db, 'audio', audioId)
      return updateDoc(audioRef, updates)
    })

    await Promise.all(promises)
    console.log('Bulk update completed successfully')
  } catch (error) {
    console.error('Error bulk updating audio:', error)
    throw error
  }
}

// Bulk upload audio (placeholder implementation)
export async function bulkUploadAudio(files: File[], metadata: any[], userId: string): Promise<{ success: boolean; message: string }> {
  try {
    console.log('Bulk uploading audio:', files.length, 'files')

    // This is a placeholder implementation
    // In a real implementation, you would:
    // 1. Upload files to storage
    // 2. Create audio records with metadata
    // 3. Handle errors and progress

    console.log('Bulk upload completed (placeholder)')
    return {
      success: true,
      message: `Successfully uploaded ${files.length} recordings`
    }
  } catch (error) {
    console.error('Error bulk uploading audio:', error)
    return {
      success: false,
      message: 'Failed to upload recordings'
    }
  }
}

// Test Firebase connection
export async function testFirebaseConnection(userId?: string): Promise<boolean> {
  try {
    console.log('Testing Firebase connection...')

    if (userId) {
      // Try to read from user's audio collection
      const testQuery = query(
        collection(db, 'audio'),
        where('user_id', '==', userId),
        firestoreLimit(1)
      )
      const snapshot = await getDocs(testQuery)
      console.log('Firebase connection test successful, found user docs:', snapshot.docs.length)
    } else {
      // Just test basic connection without reading data
      console.log('Testing basic Firebase connection without data access...')
    }

    return true
  } catch (error) {
    console.error('Firebase connection test failed:', error)
    return false
  }
}
