"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { useToast } from "@/hooks/use-toast"
import { db } from "@/lib/firebase"
import { collection, query, where, orderBy, onSnapshot, doc, getDoc, setDoc, deleteDoc, updateDoc, writeBatch } from "firebase/firestore"
import { useAuth } from "@/components/auth-provider"
import { usePreferencesStore, UserPreferencesService } from "@/lib/user-preferences"

// Helper function to safely convert timestamps to Date objects
const safeToDate = (timestamp: any): Date => {
  if (!timestamp) return new Date()
  if (timestamp instanceof Date) return timestamp
  if (typeof timestamp === 'string') return new Date(timestamp)
  if (timestamp.toDate && typeof timestamp.toDate === 'function') return timestamp.toDate()
  if (timestamp.seconds) return new Date(timestamp.seconds * 1000)
  return new Date()
}

import {
  ArrowLeft,
  Filter,
  Search,
  Trash2,
  Eye,
  Download,
  RotateCcw,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  BarChart3,
  FileText,
  Calendar,
  Clock,
  CheckSquare,
  Square,
  MoreHorizontal,
  RefreshCw,
  Archive,
  Star,
  StarOff,
  Table as TableIcon
} from "lucide-react"
import Link from "next/link"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"

export default function ASRHistoryPage() {
  const { toast } = useToast()
  const { user } = useAuth()
  const { addToFavorites, removeFromFavorites, updatePreferences, addToRecentActivity, preferences, setPreferences } = usePreferencesStore()

  const [history, setHistory] = useState<any[]>([])
  const [filteredHistory, setFilteredHistory] = useState<any[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [typeFilter, setTypeFilter] = useState<string>("all")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [dateRange, setDateRange] = useState({ start: "", end: "" })
  const [isLoading, setIsLoading] = useState(true)
  const [selectedHistory, setSelectedHistory] = useState<any>(null)
  const [showDetails, setShowDetails] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [historyToDelete, setHistoryToDelete] = useState<string | null>(null)
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set())
  const [showBulkActions, setShowBulkActions] = useState(false)

  // Initialize from user preferences or defaults
  const [viewMode, setViewMode] = useState<'table' | 'cards'>(
    preferences?.asr_preferences.default_view_mode || 'table'
  )
  const [sortBy, setSortBy] = useState<'date' | 'accuracy' | 'wer'>(
    preferences?.asr_preferences.default_sort_by || 'date'
  )
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>(
    preferences?.asr_preferences.default_sort_order || 'desc'
  )
  const [showComparison, setShowComparison] = useState(false)
  const [comparisonItems, setComparisonItems] = useState<any[]>([])

  // Get favorites from preferences
  const favoriteItems = new Set(preferences?.asr_preferences.favorites || [])
  const isLoggedIn = !!(user?.id || user?.uid)

  useEffect(() => {
    // Set up real-time listener for ASR history
    const historyRef = collection(db, 'training_history')
    const historyQuery = query(historyRef, orderBy('timestamp', 'desc'))
    
    const unsubscribe = onSnapshot(historyQuery, (snapshot) => {
      try {
        const historyData = snapshot.docs
          .map(doc => ({
            id: doc.id,
            ...doc.data()
          }))
          .filter((item: any) => item.model_type === 'asr') // Filter client-side for ASR entries

        // Only update if the data has actually changed
        if (JSON.stringify(historyData) !== JSON.stringify(history)) {
          setHistory(historyData)
          setFilteredHistory(historyData)
        }
        setIsLoading(false)
      } catch (error) {
        console.error("Error processing history data:", error)
        toast({
          title: "Error",
          description: "Failed to load history data. Please try refreshing the page.",
          variant: "destructive",
        })
      }
    }, (error) => {
      console.error("Error in history listener:", error)
      toast({
        title: "Error",
        description: "Failed to connect to history data. Please try refreshing the page.",
        variant: "destructive",
      })
    })

    return () => unsubscribe()
  }, [])

  useEffect(() => {
    // Apply filters
    let filtered = [...history]

    // Apply type filter
    if (typeFilter !== 'all') {
      filtered = filtered.filter(item => {
        const itemType = item.type || 'training' // Default to training if no type
        return itemType === typeFilter
      })
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(item => {
        const itemStatus = item.status || 'completed' // Default to completed if no status
        return itemStatus === statusFilter
      })
    }

    // Apply search term
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase()
      filtered = filtered.filter(item =>
        item.model_version?.toLowerCase().includes(searchLower) ||
        (item.type || 'training').toLowerCase().includes(searchLower) ||
        (item.status || 'completed').toLowerCase().includes(searchLower) ||
        item.id?.toLowerCase().includes(searchLower)
      )
    }

    // Apply date range with better timestamp handling
    if (dateRange.start) {
      const startDate = new Date(dateRange.start)
      startDate.setHours(0, 0, 0, 0) // Start of day
      filtered = filtered.filter(item => {
        if (!item.timestamp) return false
        const itemDate = item.timestamp.toDate ? item.timestamp.toDate() : new Date(item.timestamp)
        return itemDate >= startDate
      })
    }
    if (dateRange.end) {
      const endDate = new Date(dateRange.end)
      endDate.setHours(23, 59, 59, 999) // End of day
      filtered = filtered.filter(item => {
        if (!item.timestamp) return false
        const itemDate = safeToDate(item.timestamp)
        return itemDate <= endDate
      })
    }

    // Apply sorting with better error handling
    filtered.sort((a, b) => {
      let aValue, bValue

      switch (sortBy) {
        case 'accuracy':
          aValue = a.accuracy || 0
          bValue = b.accuracy || 0
          break
        case 'wer':
          aValue = a.wer || 1 // Default to 1 (100% error) if no WER
          bValue = b.wer || 1
          break
        case 'date':
        default:
          // Handle timestamp properly
          const aDate = a.timestamp ? (a.timestamp.toDate ? a.timestamp.toDate() : new Date(a.timestamp)) : new Date(0)
          const bDate = b.timestamp ? (b.timestamp.toDate ? b.timestamp.toDate() : new Date(b.timestamp)) : new Date(0)
          aValue = aDate.getTime()
          bValue = bDate.getTime()
          break
      }

      if (sortOrder === 'asc') {
        return aValue - bValue
      } else {
        return bValue - aValue
      }
    })

    setFilteredHistory(filtered)
  }, [history, typeFilter, statusFilter, searchTerm, dateRange, sortBy, sortOrder])

  const handleRollback = async (version: string) => {
    try {
      const historyRef = doc(db, 'training_history', version)
      const historyDoc = await getDoc(historyRef)

      if (!historyDoc.exists()) {
        throw new Error('History record not found')
      }

      // Update current model version
      const statusRef = doc(db, 'training_status', 'asr')
      await setDoc(statusRef, {
        ...historyDoc.data(),
        status: 'idle',
        updated_at: new Date()
      })

      toast({
        title: "Model rolled back",
        description: `Successfully rolled back to version ${version}`,
      })
    } catch (error) {
      console.error("Error rolling back model:", error)
      toast({
        title: "Error",
        description: "Failed to rollback model. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleDeleteHistory = async (historyId: string) => {
    try {
      await deleteDoc(doc(db, 'training_history', historyId))
      toast({
        title: "History Deleted",
        description: "Training history entry has been deleted successfully.",
      })
      setShowDeleteConfirm(false)
      setHistoryToDelete(null)
    } catch (error) {
      console.error("Error deleting history:", error)
      toast({
        title: "Error",
        description: "Failed to delete history entry. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleBulkDelete = async () => {
    try {
      const batch = writeBatch(db)
      selectedItems.forEach(id => {
        batch.delete(doc(db, 'training_history', id))
      })
      await batch.commit()

      toast({
        title: "Bulk Delete Completed",
        description: `${selectedItems.size} history entries have been deleted.`,
      })
      setSelectedItems(new Set())
      setShowBulkActions(false)
    } catch (error) {
      console.error("Error bulk deleting:", error)
      toast({
        title: "Error",
        description: "Failed to delete selected entries. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleBulkExport = () => {
    try {
      const selectedData = filteredHistory.filter(item => selectedItems.has(item.id))
      const exportData = {
        exported_at: new Date().toISOString(),
        total_entries: selectedData.length,
        entries: selectedData
      }

      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `asr_history_export_${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      toast({
        title: "Export Completed",
        description: `${selectedData.length} entries exported successfully.`,
      })
    } catch (error) {
      console.error("Error exporting:", error)
      toast({
        title: "Error",
        description: "Failed to export data. Please try again.",
        variant: "destructive",
      })
    }
  }

  const toggleSelectAll = () => {
    if (selectedItems.size === filteredHistory.length) {
      setSelectedItems(new Set())
    } else {
      setSelectedItems(new Set(filteredHistory.map(item => item.id)))
    }
  }

  const toggleSelectItem = (id: string) => {
    const newSelected = new Set(selectedItems)
    if (newSelected.has(id)) {
      newSelected.delete(id)
    } else {
      newSelected.add(id)
    }
    setSelectedItems(newSelected)
  }

  const toggleFavorite = async (id: string) => {
    if (!isLoggedIn) {
      toast({
        title: "Login Required",
        description: "Please login to save favorites",
        variant: "destructive",
      })
      return
    }

    // If preferences aren't loaded yet, try to create them
    if (!preferences) {
      try {
        const userId = user?.id || user?.uid
        if (userId) {
          const prefs = await UserPreferencesService.getUserPreferences(userId)
          setPreferences(prefs)
        } else {
          throw new Error("No user ID available")
        }
      } catch (error) {
        console.error('Error creating preferences:', error)
        toast({
          title: "Error",
          description: "Failed to initialize user preferences. Please try again.",
          variant: "destructive",
        })
        return
      }
    }

    try {
      if (favoriteItems.has(id)) {
        await removeFromFavorites('asr', id)
        toast({
          title: "Removed from Favorites",
          description: "Training session removed from favorites",
        })
      } else {
        await addToFavorites('asr', id)
        toast({
          title: "Added to Favorites",
          description: "Training session added to favorites",
        })
      }
    } catch (error) {
      console.error('Error toggling favorite:', error)
      toast({
        title: "Error",
        description: "Failed to update favorites. Please try again.",
        variant: "destructive",
      })
    }
  }

  const addToComparison = (item: any) => {
    if (comparisonItems.length < 3 && !comparisonItems.find(c => c.id === item.id)) {
      setComparisonItems([...comparisonItems, item])
    }
  }

  const removeFromComparison = (id: string) => {
    setComparisonItems(comparisonItems.filter(item => item.id !== id))
  }

  const handleViewDetails = (historyItem: any) => {
    setSelectedHistory(historyItem)
    setShowDetails(true)

    // Add to recent activity
    if (isLoggedIn) {
      addToRecentActivity('recent_models', historyItem.id)
    }
  }

  const handleDownloadModel = async (historyItem: any) => {
    try {
      // Create a downloadable JSON file with the model information
      const modelData = {
        id: historyItem.id,
        version: historyItem.model_version,
        type: historyItem.type,
        timestamp: historyItem.timestamp,
        metrics: {
          accuracy: historyItem.accuracy,
          confidence: historyItem.confidence,
          wer: historyItem.wer,
          cer: historyItem.cer,
          ser: historyItem.ser
        },
        training_config: historyItem.training_config,
        model_path: historyItem.model_path
      }

      const blob = new Blob([JSON.stringify(modelData, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `asr_model_${historyItem.model_version || historyItem.id}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      toast({
        title: "Download Started",
        description: "Model information has been downloaded.",
      })
    } catch (error) {
      console.error("Error downloading model:", error)
      toast({
        title: "Error",
        description: "Failed to download model information.",
        variant: "destructive",
      })
    }
  }

  const formatMetric = (value: number | undefined, isPercentage = true) => {
    if (value === undefined || value === null) return 'N/A'
    return isPercentage ? `${(value * 100).toFixed(2)}%` : value.toFixed(3)
  }

  const getMetricTrend = (current: number, previous: number) => {
    if (!current || !previous) return null
    const diff = current - previous
    return {
      direction: diff > 0 ? 'up' : diff < 0 ? 'down' : 'same',
      percentage: Math.abs((diff / previous) * 100).toFixed(1)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 border-green-200'
      case 'failed': return 'bg-red-100 text-red-800 border-red-200'
      case 'stopped': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'training': return 'bg-blue-100 text-blue-800 border-blue-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // Load user preferences when user logs in
  useEffect(() => {
    const userId = user?.id || user?.uid // Handle both id and uid fields
    if (userId) {
      const unsubscribe = UserPreferencesService.subscribeToPreferences(
        userId,
        (loadedPreferences) => {
          setPreferences(loadedPreferences)
        }
      )
      return unsubscribe
    } else {
      setPreferences(null)
    }
  }, [user?.id, user?.uid, setPreferences])

  // Update view preferences when they change
  useEffect(() => {
    if (preferences && isLoggedIn) {
      setViewMode(preferences.asr_preferences.default_view_mode)
      setSortBy(preferences.asr_preferences.default_sort_by)
      setSortOrder(preferences.asr_preferences.default_sort_order)
    }
  }, [preferences, isLoggedIn])

  // Save view mode preference when changed
  const handleViewModeChange = async (newMode: 'table' | 'cards') => {
    setViewMode(newMode)
    if (isLoggedIn && preferences) {
      await updatePreferences({
        asr_preferences: {
          ...preferences.asr_preferences,
          default_view_mode: newMode
        }
      })
    }
  }

  // Save sort preferences when changed
  const handleSortChange = async (newSortBy?: 'date' | 'accuracy' | 'wer', newSortOrder?: 'asc' | 'desc') => {
    if (newSortBy) setSortBy(newSortBy)
    if (newSortOrder) setSortOrder(newSortOrder)

    if (isLoggedIn && preferences) {
      await updatePreferences({
        asr_preferences: {
          ...preferences.asr_preferences,
          default_sort_by: newSortBy || sortBy,
          default_sort_order: newSortOrder || sortOrder
        }
      })
    }
  }



  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-bold tracking-tight">ASR Training History</h3>
          <p className="text-muted-foreground">
            View and manage ASR model training and validation history
          </p>

        </div>
        <div className="flex items-center gap-2">
          {comparisonItems.length > 0 && (
            <Button
              variant="outline"
              onClick={() => setShowComparison(true)}
              className="relative"
            >
              <BarChart3 className="mr-2 h-4 w-4" />
              Compare ({comparisonItems.length})
            </Button>
          )}
          <Link href="/dashboard/ai/asr">
            <Button variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to ASR
            </Button>
          </Link>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-800">Total Sessions</CardTitle>
            <div className="p-2 bg-blue-500 rounded-lg">
              <FileText className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-900">{history.length}</div>
            <div className="text-xs text-blue-600">
              <span className="inline-flex items-center gap-1">
                <span className="w-2 h-2 rounded-full bg-blue-500 block"></span>
                {history.filter(h => (h.type || 'training') === 'training').length} training
              </span>
              {', '}
              <span className="inline-flex items-center gap-1">
                <span className="w-2 h-2 rounded-full bg-purple-500 block"></span>
                {history.filter(h => h.type === 'validation').length} validation
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-green-200 bg-gradient-to-br from-green-50 to-green-100/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-800">Best Accuracy</CardTitle>
            <div className="p-2 bg-green-500 rounded-lg">
              <TrendingUp className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-900">
              {(() => {
                const accuracyValues = history.map(h => h.accuracy || 0).filter(a => a > 0)
                const maxAccuracy = accuracyValues.length > 0 ? Math.max(...accuracyValues) : 0
                return maxAccuracy > 0 ? `${(maxAccuracy * 100).toFixed(1)}%` : 'N/A'
              })()}
            </div>
            <div className="text-xs text-green-600">
              Highest recorded accuracy
            </div>
          </CardContent>
        </Card>

        <Card className="border-red-200 bg-gradient-to-br from-red-50 to-red-100/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-red-800">Lowest WER</CardTitle>
            <div className="p-2 bg-red-500 rounded-lg">
              <TrendingDown className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-900">
              {(() => {
                const werValues = history.map(h => h.wer || 0).filter(w => w > 0 && w < 1)
                const minWer = werValues.length > 0 ? Math.min(...werValues) : 0
                return minWer > 0 ? `${(minWer * 100).toFixed(1)}%` : 'N/A'
              })()}
            </div>
            <div className="text-xs text-red-600">
              Best word error rate
            </div>
          </CardContent>
        </Card>

        <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-purple-100/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-800">Recent Activity</CardTitle>
            <div className="p-2 bg-purple-500 rounded-lg">
              <Clock className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-900">
              {history.filter(h => {
                if (!h.timestamp) return false
                const date = h.timestamp.toDate ? h.timestamp.toDate() : new Date(h.timestamp)
                const now = new Date()
                const diffTime = Math.abs(now.getTime() - date.getTime())
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
                return diffDays <= 7
              }).length}
            </div>
            <div className="text-xs text-purple-600">
              Sessions in last 7 days
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Filters and Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Filters & Controls
              </CardTitle>
              <CardDescription>Filter, search, and manage training history</CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <div className="flex items-center border rounded-lg p-1 bg-muted/50">
                <Button
                  variant={viewMode === 'table' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleViewModeChange('table')}
                  className="h-8 px-3"
                >
                  <TableIcon className="mr-2 h-4 w-4" />
                  Table
                </Button>
                <Button
                  variant={viewMode === 'cards' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleViewModeChange('cards')}
                  className="h-8 px-3"
                >
                  <Square className="mr-2 h-4 w-4" />
                  Cards
                </Button>
              </div>
              {selectedItems.size > 0 && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-orange-200 text-orange-700 hover:bg-orange-50"
                    >
                      <CheckSquare className="mr-2 h-4 w-4" />
                      Bulk Actions ({selectedItems.size})
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuLabel className="flex items-center gap-2">
                      <CheckSquare className="h-4 w-4" />
                      Bulk Actions
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleBulkExport}>
                      <Download className="mr-2 h-4 w-4 text-blue-500" />
                      Export Selected
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={handleBulkDelete}
                      className="text-destructive"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete Selected
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-6">
            <div className="space-y-2">
              <Label className="flex items-center gap-2 text-sm font-medium">
                <FileText className="h-4 w-4 text-blue-500" />
                Type
              </Label>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="border-blue-200 focus:border-blue-500">
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">
                    <span className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-gray-400 block"></span>
                      All Types
                    </span>
                  </SelectItem>
                  <SelectItem value="training">
                    <span className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-blue-500 block"></span>
                      Training
                    </span>
                  </SelectItem>
                  <SelectItem value="validation">
                    <span className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-purple-500 block"></span>
                      Validation
                    </span>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label className="flex items-center gap-2 text-sm font-medium">
                <AlertTriangle className="h-4 w-4 text-orange-500" />
                Status
              </Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="border-orange-200 focus:border-orange-500">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">
                    <span className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-gray-400 block"></span>
                      All Status
                    </span>
                  </SelectItem>
                  <SelectItem value="completed">
                    <span className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-green-500 block"></span>
                      Completed
                    </span>
                  </SelectItem>
                  <SelectItem value="failed">
                    <span className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-red-500 block"></span>
                      Failed
                    </span>
                  </SelectItem>
                  <SelectItem value="stopped">
                    <span className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-yellow-500 block"></span>
                      Stopped
                    </span>
                  </SelectItem>
                  <SelectItem value="training">
                    <span className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-blue-500 animate-pulse block"></span>
                      Training
                    </span>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label className="flex items-center gap-2 text-sm font-medium">
                <BarChart3 className="h-4 w-4 text-purple-500" />
                Sort By
              </Label>
              <Select value={sortBy} onValueChange={(value: any) => handleSortChange(value)}>
                <SelectTrigger className="border-purple-200 focus:border-purple-500">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="date">
                    <span className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      Date
                    </span>
                  </SelectItem>
                  <SelectItem value="accuracy">
                    <span className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-green-500" />
                      Accuracy
                    </span>
                  </SelectItem>
                  <SelectItem value="wer">
                    <span className="flex items-center gap-2">
                      <TrendingDown className="h-4 w-4 text-red-500" />
                      WER
                    </span>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label className="flex items-center gap-2 text-sm font-medium">
                <Search className="h-4 w-4 text-green-500" />
                Search
              </Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-green-400" />
                <Input
                  placeholder="Search versions, status..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8 border-green-200 focus:border-green-500"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label className="flex items-center gap-2 text-sm font-medium">
                <Calendar className="h-4 w-4 text-indigo-500" />
                Start Date
              </Label>
              <Input
                type="date"
                value={dateRange.start}
                onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                className="border-indigo-200 focus:border-indigo-500"
              />
            </div>

            <div className="space-y-2">
              <Label className="flex items-center gap-2 text-sm font-medium">
                <Calendar className="h-4 w-4 text-indigo-500" />
                End Date
              </Label>
              <Input
                type="date"
                value={dateRange.end}
                onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                className="border-indigo-200 focus:border-indigo-500"
              />
            </div>
          </div>

          {/* Quick Filter Buttons */}
          <div className="flex flex-wrap gap-2 mt-4 pt-4 border-t">
            <Button
              variant={typeFilter === 'all' && statusFilter === 'all' && !searchTerm && !dateRange.start && !dateRange.end ? 'default' : 'outline'}
              size="sm"
              onClick={() => {
                setTypeFilter('all')
                setStatusFilter('all')
                setSearchTerm('')
                setDateRange({ start: '', end: '' })
              }}
              className="h-8"
            >
              <RefreshCw className="mr-2 h-3 w-3" />
              Clear All Filters
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const today = new Date()
                const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
                setDateRange({
                  start: weekAgo.toISOString().split('T')[0],
                  end: today.toISOString().split('T')[0]
                })
              }}
              className="h-8 border-purple-200 text-purple-700 hover:bg-purple-50"
            >
              <Clock className="mr-2 h-3 w-3" />
              Last 7 Days
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* History Content */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Archive className="h-5 w-5" />
                Training History
              </CardTitle>
              <CardDescription className="flex items-center gap-2">
                <span>
                  Showing {filteredHistory.length} of {history.length} entries
                  {selectedItems.size > 0 && ` • ${selectedItems.size} selected`}
                </span>
                {(typeFilter !== 'all' || statusFilter !== 'all' || searchTerm || dateRange.start || dateRange.end) && (
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200">
                    <Filter className="mr-1 h-3 w-3" />
                    Filtered
                  </Badge>
                )}
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleSortChange(undefined, sortOrder === 'asc' ? 'desc' : 'asc')}
              className="border-indigo-200 text-indigo-700 hover:bg-indigo-50"
            >
              {sortOrder === 'asc' ? (
                <>
                  <TrendingUp className="mr-2 h-4 w-4" />
                  Oldest First
                </>
              ) : (
                <>
                  <TrendingDown className="mr-2 h-4 w-4" />
                  Newest First
                </>
              )}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {viewMode === 'table' ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedItems.size === filteredHistory.length && filteredHistory.length > 0}
                      onCheckedChange={toggleSelectAll}
                    />
                  </TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Version</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Accuracy</TableHead>
                  <TableHead>Confidence</TableHead>
                  <TableHead>WER</TableHead>
                  <TableHead>CER</TableHead>
                  <TableHead>Samples</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={11} className="text-center py-8">
                      <div className="flex items-center justify-center gap-2">
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        Loading history...
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredHistory.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={11} className="text-center py-8">
                      <div className="flex flex-col items-center gap-2">
                        <Archive className="h-8 w-8 text-muted-foreground" />
                        <p className="text-muted-foreground">No history entries found</p>
                        <p className="text-sm text-muted-foreground">Try adjusting your filters</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredHistory.map((item, index) => {
                    const previousItem = index > 0 ? filteredHistory[index - 1] : null
                    const accuracyTrend = previousItem ? getMetricTrend(item.accuracy, previousItem.accuracy) : null

                    return (
                      <TableRow key={item.id} className="group hover:bg-muted/50">
                        <TableCell>
                          <Checkbox
                            checked={selectedItems.has(item.id)}
                            onCheckedChange={() => toggleSelectItem(item.id)}
                          />
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span className="font-medium">
                              {item.timestamp ? (() => {
                                const date = item.timestamp.toDate ? item.timestamp.toDate() : new Date(item.timestamp);
                                return date.toLocaleDateString('en-US', {
                                  month: 'short',
                                  day: 'numeric',
                                  year: 'numeric'
                                });
                              })() : 'N/A'}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              {item.timestamp ? (() => {
                                const date = item.timestamp.toDate ? item.timestamp.toDate() : new Date(item.timestamp);
                                return date.toLocaleTimeString('en-US', {
                                  hour: '2-digit',
                                  minute: '2-digit'
                                });
                              })() : ''}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={item.type === 'validation' ? 'default' : 'secondary'}>
                            {item.type || 'training'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <span className="font-mono text-sm">{item.model_version || 'N/A'}</span>
                            {favoriteItems.has(item.id) && (
                              <Star className="h-3 w-3 text-yellow-500 fill-current" />
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant="outline"
                            className={getStatusColor(item.status || 'completed')}
                          >
                            {item.status || 'completed'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <span className={item.accuracy !== undefined ? 'font-medium' : 'text-muted-foreground'}>
                              {item.accuracy !== undefined ? `${Math.round(item.accuracy * 100)}%` : 'N/A'}
                            </span>
                            {accuracyTrend && (
                              <span className={`text-xs ${accuracyTrend.direction === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                                {accuracyTrend.direction === 'up' ? '↗' : '↘'} {accuracyTrend.percentage}%
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className={item.confidence !== undefined ? 'font-medium' : 'text-muted-foreground'}>
                            {item.confidence !== undefined ? `${Math.round(item.confidence * 100)}%` : 'N/A'}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className={item.wer !== undefined ? 'font-medium' : 'text-muted-foreground'}>
                            {item.wer !== undefined ? `${Math.round(item.wer * 100)}%` : 'N/A'}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className={item.cer !== undefined ? 'font-medium' : 'text-muted-foreground'}>
                            {item.cer !== undefined ? `${Math.round(item.cer * 100)}%` : 'N/A'}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className="font-medium">
                            {item.samples_trained || item.samples_processed || item.samples_tested || 'N/A'}
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => toggleFavorite(item.id)}
                              title={favoriteItems.has(item.id) ? "Remove from favorites" : "Add to favorites"}
                            >
                              {favoriteItems.has(item.id) ? (
                                <Star className="h-4 w-4 text-yellow-500 fill-current" />
                              ) : (
                                <StarOff className="h-4 w-4" />
                              )}
                            </Button>

                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => handleViewDetails(item)}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  View Details
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => addToComparison(item)}>
                                  <BarChart3 className="mr-2 h-4 w-4" />
                                  Add to Compare
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleDownloadModel(item)}>
                                  <Download className="mr-2 h-4 w-4" />
                                  Download Info
                                </DropdownMenuItem>
                                {item.type === 'training' && (
                                  <DropdownMenuItem onClick={() => handleRollback(item.id)}>
                                    <RotateCcw className="mr-2 h-4 w-4" />
                                    Rollback
                                  </DropdownMenuItem>
                                )}
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() => handleDeleteHistory(item.id)}
                                  className="text-destructive"
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </TableCell>
                      </TableRow>
                    )
                  })
                )}
              </TableBody>
            </Table>
          ) : (
            // Cards View
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {isLoading ? (
                Array.from({ length: 6 }).map((_, i) => (
                  <Card key={i} className="animate-pulse">
                    <CardHeader>
                      <div className="h-4 bg-muted rounded w-3/4"></div>
                      <div className="h-3 bg-muted rounded w-1/2"></div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="h-3 bg-muted rounded"></div>
                        <div className="h-3 bg-muted rounded w-2/3"></div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : filteredHistory.length === 0 ? (
                <div className="col-span-full flex flex-col items-center justify-center py-12">
                  <Archive className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No history entries found</h3>
                  <p className="text-muted-foreground text-center">
                    Try adjusting your filters or start a new training session
                  </p>
                </div>
              ) : (
                filteredHistory.map((item) => (
                  <Card key={item.id} className="group hover:shadow-md transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-2">
                          <Checkbox
                            checked={selectedItems.has(item.id)}
                            onCheckedChange={() => toggleSelectItem(item.id)}
                          />
                          <Badge variant={item.type === 'validation' ? 'default' : 'secondary'}>
                            {item.type || 'training'}
                          </Badge>
                          <Badge
                            variant="outline"
                            className={getStatusColor(item.status || 'completed')}
                          >
                            {item.status || 'completed'}
                          </Badge>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleFavorite(item.id)}
                        >
                          {favoriteItems.has(item.id) ? (
                            <Star className="h-4 w-4 text-yellow-500 fill-current" />
                          ) : (
                            <StarOff className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                      <div>
                        <CardTitle className="text-lg font-mono">
                          {item.model_version || 'N/A'}
                        </CardTitle>
                        <CardDescription>
                          {item.timestamp ? (() => {
                            const date = safeToDate(item.timestamp);
                            return date.toLocaleString('en-US', {
                              month: 'short',
                              day: 'numeric',
                              year: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            });
                          })() : 'N/A'}
                        </CardDescription>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="grid grid-cols-2 gap-3 mb-4">
                        <div className="text-center p-2 bg-green-50 rounded">
                          <div className="text-sm text-muted-foreground">Accuracy</div>
                          <div className="font-bold text-green-700">
                            {item.accuracy !== undefined ? `${Math.round(item.accuracy * 100)}%` : 'N/A'}
                          </div>
                        </div>
                        <div className="text-center p-2 bg-red-50 rounded">
                          <div className="text-sm text-muted-foreground">WER</div>
                          <div className="font-bold text-red-700">
                            {item.wer !== undefined ? `${Math.round(item.wer * 100)}%` : 'N/A'}
                          </div>
                        </div>
                        <div className="text-center p-2 bg-blue-50 rounded">
                          <div className="text-sm text-muted-foreground">Confidence</div>
                          <div className="font-bold text-blue-700">
                            {item.confidence !== undefined ? `${Math.round(item.confidence * 100)}%` : 'N/A'}
                          </div>
                        </div>
                        <div className="text-center p-2 bg-purple-50 rounded">
                          <div className="text-sm text-muted-foreground">Samples</div>
                          <div className="font-bold text-purple-700">
                            {item.samples_trained || item.samples_processed || item.samples_tested || 'N/A'}
                          </div>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewDetails(item)}
                          className="flex-1"
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          Details
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => addToComparison(item)}
                          disabled={comparisonItems.length >= 3}
                        >
                          <BarChart3 className="h-4 w-4" />
                        </Button>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleDownloadModel(item)}>
                              <Download className="mr-2 h-4 w-4" />
                              Download
                            </DropdownMenuItem>
                            {item.type === 'training' && (
                              <DropdownMenuItem onClick={() => handleRollback(item.id)}>
                                <RotateCcw className="mr-2 h-4 w-4" />
                                Rollback
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleDeleteHistory(item.id)}
                              className="text-destructive"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Model Comparison Dialog */}
      <Dialog open={showComparison} onOpenChange={setShowComparison}>
        <DialogContent className="max-w-6xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Model Performance Comparison
            </DialogTitle>
            <DialogDescription>
              Compare performance metrics across different training sessions
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {comparisonItems.length === 0 ? (
              <div className="text-center py-8">
                <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No models selected for comparison</p>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Comparison Table */}
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Model</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Accuracy</TableHead>
                      <TableHead>Confidence</TableHead>
                      <TableHead>WER</TableHead>
                      <TableHead>CER</TableHead>
                      <TableHead>Samples</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {comparisonItems.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell className="font-mono">{item.model_version}</TableCell>
                        <TableCell>
                          {item.timestamp ? (() => {
                            const date = item.timestamp.toDate ? item.timestamp.toDate() : new Date(item.timestamp);
                            return date.toLocaleDateString();
                          })() : 'N/A'}
                        </TableCell>
                        <TableCell>
                          <span className="font-medium text-green-700">
                            {item.accuracy !== undefined ? `${Math.round(item.accuracy * 100)}%` : 'N/A'}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className="font-medium text-blue-700">
                            {item.confidence !== undefined ? `${Math.round(item.confidence * 100)}%` : 'N/A'}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className="font-medium text-red-700">
                            {item.wer !== undefined ? `${Math.round(item.wer * 100)}%` : 'N/A'}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className="font-medium text-orange-700">
                            {item.cer !== undefined ? `${Math.round(item.cer * 100)}%` : 'N/A'}
                          </span>
                        </TableCell>
                        <TableCell>{item.samples_trained || item.samples_processed || 'N/A'}</TableCell>
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => removeFromComparison(item.id)}
                          >
                            Remove
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* Best Performer Highlight */}
                {comparisonItems.length > 1 && (
                  <div className="grid gap-4 md:grid-cols-3">
                    <Card className="border-green-200 bg-green-50">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm text-green-800">Best Accuracy</CardTitle>
                      </CardHeader>
                      <CardContent>
                        {(() => {
                          const best = comparisonItems.reduce((prev, current) =>
                            (current.accuracy || 0) > (prev.accuracy || 0) ? current : prev
                          )
                          return (
                            <div>
                              <div className="font-mono text-sm">{best.model_version}</div>
                              <div className="text-lg font-bold text-green-700">
                                {best.accuracy ? `${Math.round(best.accuracy * 100)}%` : 'N/A'}
                              </div>
                            </div>
                          )
                        })()}
                      </CardContent>
                    </Card>

                    <Card className="border-red-200 bg-red-50">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm text-red-800">Lowest WER</CardTitle>
                      </CardHeader>
                      <CardContent>
                        {(() => {
                          const best = comparisonItems.reduce((prev, current) =>
                            (current.wer || 1) < (prev.wer || 1) ? current : prev
                          )
                          return (
                            <div>
                              <div className="font-mono text-sm">{best.model_version}</div>
                              <div className="text-lg font-bold text-red-700">
                                {best.wer ? `${Math.round(best.wer * 100)}%` : 'N/A'}
                              </div>
                            </div>
                          )
                        })()}
                      </CardContent>
                    </Card>

                    <Card className="border-blue-200 bg-blue-50">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm text-blue-800">Most Samples</CardTitle>
                      </CardHeader>
                      <CardContent>
                        {(() => {
                          const best = comparisonItems.reduce((prev, current) =>
                            (current.samples_trained || 0) > (prev.samples_trained || 0) ? current : prev
                          )
                          return (
                            <div>
                              <div className="font-mono text-sm">{best.model_version}</div>
                              <div className="text-lg font-bold text-blue-700">
                                {best.samples_trained || 'N/A'}
                              </div>
                            </div>
                          )
                        })()}
                      </CardContent>
                    </Card>
                  </div>
                )}
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setComparisonItems([])}>
              Clear All
            </Button>
            <Button variant="outline" onClick={() => setShowComparison(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* History Details Dialog */}
      <Dialog open={showDetails} onOpenChange={setShowDetails}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Training History Details</DialogTitle>
            <DialogDescription>
              Detailed information about this training session
            </DialogDescription>
          </DialogHeader>
          {selectedHistory && (
            <div className="py-4 space-y-6">
              {/* Basic Information */}
              <div>
                <h4 className="font-medium mb-3">Basic Information</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Type:</span>
                    <div className="font-medium">{selectedHistory.type || 'training'}</div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Version:</span>
                    <div className="font-medium">{selectedHistory.model_version || 'N/A'}</div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Date:</span>
                    <div className="font-medium">
                      {selectedHistory.timestamp ? (() => {
                        const date = selectedHistory.timestamp.toDate ? selectedHistory.timestamp.toDate() : new Date(selectedHistory.timestamp);
                        return date.toLocaleString();
                      })() : 'N/A'}
                    </div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Model Path:</span>
                    <div className="font-medium">{selectedHistory.model_path || 'N/A'}</div>
                  </div>
                </div>
              </div>

              {/* Performance Metrics */}
              <div>
                <h4 className="font-medium mb-3">Performance Metrics</h4>
                <div className="grid grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="p-3 border rounded">
                    <div className="text-sm text-muted-foreground">Accuracy</div>
                    <div className="text-lg font-bold text-green-600">
                      {formatMetric(selectedHistory.accuracy)}
                    </div>
                  </div>
                  <div className="p-3 border rounded">
                    <div className="text-sm text-muted-foreground">Confidence</div>
                    <div className="text-lg font-bold text-blue-600">
                      {formatMetric(selectedHistory.confidence)}
                    </div>
                  </div>
                  <div className="p-3 border rounded">
                    <div className="text-sm text-muted-foreground">WER</div>
                    <div className="text-lg font-bold text-red-600">
                      {formatMetric(selectedHistory.wer)}
                    </div>
                  </div>
                  <div className="p-3 border rounded">
                    <div className="text-sm text-muted-foreground">CER</div>
                    <div className="text-lg font-bold text-orange-600">
                      {formatMetric(selectedHistory.cer)}
                    </div>
                  </div>
                  <div className="p-3 border rounded">
                    <div className="text-sm text-muted-foreground">SER</div>
                    <div className="text-lg font-bold text-purple-600">
                      {formatMetric(selectedHistory.ser)}
                    </div>
                  </div>
                  <div className="p-3 border rounded">
                    <div className="text-sm text-muted-foreground">Samples</div>
                    <div className="text-lg font-bold text-gray-600">
                      {selectedHistory.samples_trained || selectedHistory.samples_processed || selectedHistory.samples_tested || 'N/A'}
                    </div>
                  </div>
                </div>
              </div>

              {/* Training Configuration */}
              {selectedHistory.training_config && (
                <div>
                  <h4 className="font-medium mb-3">Training Configuration</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Epochs:</span>
                      <div className="font-medium">{selectedHistory.training_config.epochs || 'N/A'}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Learning Rate:</span>
                      <div className="font-medium">{selectedHistory.training_config.learning_rate || 'N/A'}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Model Name:</span>
                      <div className="font-medium">{selectedHistory.training_config.model_name || 'N/A'}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Validation Split:</span>
                      <div className="font-medium">{selectedHistory.training_config.validation_split || 'N/A'}</div>
                    </div>
                  </div>
                </div>
              )}

              {/* Additional Information */}
              <div>
                <h4 className="font-medium mb-3">Additional Information</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Duration:</span>
                    <div className="font-medium">{selectedHistory.training_duration || 'N/A'}</div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Status:</span>
                    <div className="font-medium">{selectedHistory.status || 'completed'}</div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Model Type:</span>
                    <div className="font-medium">{selectedHistory.model_type || 'asr'}</div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Upload Status:</span>
                    <div className="font-medium">{selectedHistory.upload_status || 'N/A'}</div>
                  </div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDetails(false)}>
              Close
            </Button>
            {selectedHistory?.type === 'training' && (
              <Button onClick={() => {
                handleRollback(selectedHistory.id);
                setShowDetails(false);
              }}>
                <RotateCcw className="mr-2 h-4 w-4" />
                Rollback to this Version
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
