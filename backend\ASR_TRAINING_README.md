# ASR Training with <PERSON>hisper Fine-tuning

This document explains how to set up and use local ASR training with OpenAI Whisper-small fine-tuning.

## Overview

The ASR training system uses:
- **OpenAI Whisper-small** from Hugging Face Transformers
- **Local fine-tuning** (no external APIs)
- **Real training** with your Masalit audio data
- **Progress tracking** with dynamic color-coded progress bars
- **Model versioning** and cloud storage

## Setup Instructions

### 1. Install Dependencies

```bash
# Navigate to backend directory
cd backend

# Install ASR training dependencies
python install_asr_deps.py

# Install FFmpeg for audio processing
python install_ffmpeg.py

# Or manually install
pip install -r requirements-asr.txt
# And install FFmpeg separately for your OS
```

### 2. Test Setup

```bash
# Test if Whisper is properly installed
python test_whisper_setup.py
```

Expected output:
```
✅ PyTorch version: 2.0.0+cu118
✅ CUDA available: True
✅ Whisper model loaded successfully!
✅ Model parameters: 243,201,023
🎉 All tests passed! Whisper is ready for fine-tuning.
```

### 3. Hardware Requirements

**Minimum:**
- 8GB RAM
- 4GB free disk space
- CPU training (slow but works)

**Recommended:**
- 16GB+ RAM
- NVIDIA GPU with 8GB+ VRAM
- 10GB+ free disk space
- CUDA-compatible GPU

## Training Process

### 1. Data Preparation
- Audio files are downloaded from Firebase Storage
- Resampled to 16kHz (Whisper requirement)
- Paired with transcriptions from Firestore
- Split into train/validation sets

### 2. Model Fine-tuning
- Loads `openai/whisper-small` (244M parameters)
- Fine-tunes on your Masalit data
- Uses mixed precision training (if GPU available)
- Implements early stopping to prevent overfitting

### 3. Progress Tracking
- Real-time updates to Firestore
- Dynamic progress bars with color coding:
  - 🟢 Green: Good progress/low resource usage
  - 🟡 Yellow: Medium progress/moderate usage
  - 🟠 Orange: High progress/high usage
  - 🔴 Red: Critical/very high usage

### 4. Model Saving
- Saves fine-tuned model locally
- Uploads to Google Cloud Storage
- Marks training samples as trained
- Creates training history record

## Training Settings

### Default Settings
```python
{
    "epochs": 5,
    "learning_rate": 1e-5,
    "batch_size": 4,
    "number_of_samples": 100,
    "validation_split": 0.2,
    "early_stopping_patience": 3,
    "model_name": "small"
}
```

### Customizable Parameters
- **epochs**: Number of training epochs (1-20)
- **learning_rate**: Learning rate (1e-6 to 1e-3)
- **batch_size**: Batch size (1-16, depends on GPU memory)
- **number_of_samples**: Max samples to use for training
- **validation_split**: Fraction for validation (0.1-0.3)
- **early_stopping_patience**: Epochs to wait before stopping

## File Structure

```
backend/
├── core/asr/
│   └── trainer.py              # Main training implementation
├── requirements-asr.txt        # ASR dependencies
├── install_asr_deps.py        # Dependency installer
├── test_whisper_setup.py      # Setup verification
└── models/                    # Saved models directory
    ├── checkpoints/           # Training checkpoints
    └── final/                 # Final trained models
```

## Training Workflow

1. **Start Training**: Click "Start Training" in the dashboard
2. **Data Loading**: System downloads audio files from Firebase
3. **Model Loading**: Loads Whisper-small from Hugging Face
4. **Fine-tuning**: Trains model on your data with progress updates
5. **Validation**: Evaluates model performance
6. **Saving**: Saves model and uploads to cloud storage
7. **Completion**: Marks samples as trained and creates history

## Monitoring Training

### Dashboard Features
- Real-time progress bars with dynamic colors
- Current epoch and sample counts
- Training metrics (accuracy, confidence, WER, CER, SER)
- System resource monitoring (CPU, Memory, GPU)
- Training history and model versioning

### Log Monitoring
```bash
# View training logs
tail -f backend/logs/training.log

# Monitor system resources
htop  # or top on Windows
nvidia-smi  # for GPU monitoring
```

## Troubleshooting

### Common Issues

**1. Out of Memory Error**
```
Solution: Reduce batch_size to 2 or 1
```

**2. CUDA Out of Memory**
```
Solution: 
- Reduce batch_size
- Enable gradient_checkpointing
- Use CPU training (slower)
```

**3. Audio Download Fails**
```
Solution: Check Firebase Storage permissions
```

**4. Model Loading Fails**
```
Solution: Check internet connection for Hugging Face download
```

### Performance Tips

1. **GPU Training**: Much faster than CPU
2. **Batch Size**: Larger = faster, but needs more memory
3. **Mixed Precision**: Reduces memory usage
4. **Gradient Checkpointing**: Saves memory at cost of speed

## Model Output

### Trained Model Files
- `pytorch_model.bin`: Fine-tuned Whisper weights
- `config.json`: Model configuration
- `preprocessor_config.json`: Audio preprocessing config
- `tokenizer.json`: Text tokenizer
- `training_info.json`: Training metadata

### Model Usage
The fine-tuned model can be used for:
- Real-time ASR transcription
- Batch audio processing
- Further fine-tuning
- Model comparison and evaluation

## Next Steps

After successful training:
1. Test the model with new audio samples
2. Compare with previous model versions
3. Deploy for production use
4. Continue training with more data
5. Experiment with different hyperparameters
