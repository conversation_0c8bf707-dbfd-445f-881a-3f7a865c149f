import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { auth } from '@/lib/firebase-admin'
import { collection, query, where, getDocs, doc, getDoc, updateDoc, orderBy, limit, startAfter, DocumentSnapshot } from 'firebase/firestore'
import { db } from '@/lib/firebase'

interface UserFilters {
  role?: 'admin' | 'user'
  status?: 'active' | 'disabled' | 'unverified'
  search?: string
  sortBy?: 'name' | 'email' | 'created_at' | 'last_activity'
  sortOrder?: 'asc' | 'desc'
  page?: number
  pageSize?: number
}

interface UserListResponse {
  users: any[]
  totalCount: number
  hasMore: boolean
  nextPage?: number
}

// GET /api/users - List users with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    // Get session cookie
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get('session')?.value

    if (!sessionCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the session cookie
    const decodedClaims = await auth.verifySessionCookie(sessionCookie, true)
    const userId = decodedClaims.uid

    // Get user data from Firestore to check role
    const userDoc = await getDoc(doc(db, 'users', userId))
    if (!userDoc.exists()) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const userData = userDoc.data()

    // Remove admin check - authenticated users can list users



    const { searchParams } = new URL(request.url)
    const roleParam = searchParams.get('role')
    const statusParam = searchParams.get('status')

    const filters: UserFilters = {
      role: roleParam && roleParam !== 'all' ? roleParam as 'admin' | 'user' : undefined,
      status: statusParam && statusParam !== 'all' ? statusParam as 'active' | 'disabled' | 'unverified' : undefined,
      search: searchParams.get('search') || undefined,
      sortBy: searchParams.get('sortBy') as 'name' | 'email' | 'created_at' | 'last_activity' || 'created_at',
      sortOrder: searchParams.get('sortOrder') as 'asc' | 'desc' || 'desc',
      page: parseInt(searchParams.get('page') || '1'),
      pageSize: parseInt(searchParams.get('pageSize') || '20')
    }

    // Build query with proper indexes (now deployed!)
    let usersQuery = query(collection(db, 'users'))

    // Apply role filter
    if (filters.role) {
      usersQuery = query(usersQuery, where('role', '==', filters.role))
    }

    // Apply sorting (now supported with indexes)
    if (filters.sortBy) {
      usersQuery = query(usersQuery, orderBy(filters.sortBy, filters.sortOrder))
    }

    // Apply pagination
    if (filters.pageSize) {
      usersQuery = query(usersQuery, limit(filters.pageSize))
    }

    const querySnapshot = await getDocs(usersQuery)
    let users = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }))

    // Apply client-side filters (search, status)
    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      users = users.filter(user => 
        user.name?.toLowerCase().includes(searchLower) ||
        user.email?.toLowerCase().includes(searchLower) ||
        user.username?.toLowerCase().includes(searchLower)
      )
    }

    if (filters.status) {
      users = users.filter(user => {
        switch (filters.status) {
          case 'active':
            return !user.isDisabled && user.email_verified
          case 'disabled':
            return user.isDisabled
          case 'unverified':
            return !user.email_verified && !user.isDisabled
          default:
            return true
        }
      })
    }

    // Check if there are more results
    const hasMore = users.length > (filters.pageSize || 20)
    if (hasMore) {
      users = users.slice(0, filters.pageSize || 20)
    }

    // Get enhanced user data with basic subcollections
    const enhancedUsers = await Promise.all(
      users.map(async (user) => {
        try {
          // Get basic statistics from subcollection if it exists
          const statsRef = doc(db, 'users', user.id, 'statistics', 'summary')
          const statsDoc = await getDoc(statsRef)
          const statistics = statsDoc.exists() ? { summary: statsDoc.data() } : null

          return {
            ...user,
            statistics,
            // Ensure required fields exist
            email_verified: user.email_verified ?? false,
            isDisabled: user.isDisabled ?? false,
            role: user.role || 'user',
            created_at: user.created_at || new Date().toISOString()
          }
        } catch (error) {
          console.error(`Error fetching subcollections for user ${user.id}:`, error)
          return {
            ...user,
            // Ensure required fields exist even on error
            email_verified: user.email_verified ?? false,
            isDisabled: user.isDisabled ?? false,
            role: user.role || 'user',
            created_at: user.created_at || new Date().toISOString()
          }
        }
      })
    )

    const response: UserListResponse = {
      users: enhancedUsers,
      totalCount: users.length,
      hasMore,
      nextPage: hasMore ? (filters.page || 1) + 1 : undefined
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PATCH /api/users - Bulk update users
export async function PATCH(request: NextRequest) {
  try {
    // Get session cookie
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get('session')?.value

    if (!sessionCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the session cookie
    const decodedClaims = await auth.verifySessionCookie(sessionCookie, true)
    const userId = decodedClaims.uid

    // Get user data from Firestore to check role
    const userDoc = await getDoc(doc(db, 'users', userId))
    if (!userDoc.exists()) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const userData = userDoc.data()

    // Check if user is admin
    if (userData.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { userIds, updates } = await request.json()

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json({ error: 'User IDs are required' }, { status: 400 })
    }

    if (!updates || typeof updates !== 'object') {
      return NextResponse.json({ error: 'Updates are required' }, { status: 400 })
    }

    // Validate updates
    const allowedUpdates = ['role', 'isDisabled', 'email_verified']
    const updateKeys = Object.keys(updates)
    const invalidKeys = updateKeys.filter(key => !allowedUpdates.includes(key))
    
    if (invalidKeys.length > 0) {
      return NextResponse.json({ 
        error: `Invalid update fields: ${invalidKeys.join(', ')}` 
      }, { status: 400 })
    }

    // Perform bulk updates
    const updatePromises = userIds.map(async (userId: string) => {
      try {
        const userRef = doc(db, 'users', userId)
        await updateDoc(userRef, {
          ...updates,
          updated_at: new Date().toISOString()
        })
        return { userId, success: true }
      } catch (error) {
        console.error(`Error updating user ${userId}:`, error)
        return { userId, success: false, error: error.message }
      }
    })

    const results = await Promise.all(updatePromises)
    const successful = results.filter(r => r.success).length
    const failed = results.filter(r => !r.success)

    return NextResponse.json({
      message: `Updated ${successful} users successfully`,
      successful,
      failed: failed.length,
      failures: failed
    })
  } catch (error) {
    console.error('Error bulk updating users:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
