"use client"

import React, { useState, useEffect } from 'react'

interface CSPTestProps {
  showInDevelopment?: boolean
}

export function CSPTest({ showInDevelopment = true }: CSPTestProps) {
  const [cspStatus, setCSPStatus] = useState<{
    firebase: 'testing' | 'allowed' | 'blocked'
    google: 'testing' | 'allowed' | 'blocked'
    message?: string
  }>({
    firebase: 'testing',
    google: 'testing'
  })

  useEffect(() => {
    // Only show in development if enabled
    if (process.env.NODE_ENV !== 'development' && showInDevelopment) {
      return
    }

    const testCSP = async () => {
      try {
        // Suppress console errors during CSP testing
        const originalError = console.error
        console.error = () => {}

        // Test Firebase connectivity with a simpler approach
        const firebaseTest = new Promise<'allowed' | 'blocked'>((resolve) => {
          const img = new Image()
          img.onload = () => resolve('allowed')
          img.onerror = () => resolve('blocked')
          img.src = 'https://firebase.googleapis.com/favicon.ico'
          // Timeout after 3 seconds
          setTimeout(() => resolve('blocked'), 3000)
        })

        // Test Google connectivity
        const googleTest = new Promise<'allowed' | 'blocked'>((resolve) => {
          const img = new Image()
          img.onload = () => resolve('allowed')
          img.onerror = () => resolve('blocked')
          img.src = 'https://www.google.com/favicon.ico'
          // Timeout after 3 seconds
          setTimeout(() => resolve('blocked'), 3000)
        })

        const [firebaseResult, googleResult] = await Promise.all([firebaseTest, googleTest])

        // Restore console.error
        console.error = originalError

        setCSPStatus({
          firebase: firebaseResult,
          google: googleResult,
          message: process.env.NODE_ENV === 'development'
            ? 'CSP connectivity test results'
            : undefined
        })
      } catch (error) {
        // Silently handle CSP test errors
        setCSPStatus({
          firebase: 'blocked',
          google: 'blocked',
          message: 'CSP test completed'
        })
      }
    }

    // Run test after a short delay to let the page load
    const timer = setTimeout(testCSP, 2000)
    return () => clearTimeout(timer)
  }, [showInDevelopment])

  // Don't render in production unless explicitly requested
  if (process.env.NODE_ENV === 'production' && showInDevelopment) {
    return null
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'allowed': return 'text-green-600'
      case 'blocked': return 'text-red-600'
      default: return 'text-yellow-600'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'allowed': return '✅'
      case 'blocked': return '❌'
      default: return '⏳'
    }
  }

  return (
    <div className="fixed bottom-4 left-4 bg-white border border-gray-200 rounded-lg shadow-lg p-3 text-xs z-50">
      <div className="font-semibold text-gray-700 mb-1">CSP Status</div>
      <div className="space-y-1">
        <div className={`flex items-center gap-2 ${getStatusColor(cspStatus.firebase)}`}>
          <span>{getStatusIcon(cspStatus.firebase)}</span>
          <span>Firebase: {cspStatus.firebase}</span>
        </div>
        <div className={`flex items-center gap-2 ${getStatusColor(cspStatus.google)}`}>
          <span>{getStatusIcon(cspStatus.google)}</span>
          <span>Google: {cspStatus.google}</span>
        </div>
        {cspStatus.message && (
          <div className="text-gray-500 text-xs mt-1">
            {cspStatus.message}
          </div>
        )}
      </div>
    </div>
  )
}

export default CSPTest
