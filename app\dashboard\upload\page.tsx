"use client"

import React, { useState, useRef, useEffect, use<PERSON><PERSON>back } from "react"
import { useRout<PERSON> } from "next/navigation"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/components/auth-provider"
import { createAudioWithTranscription } from "@/lib/audio-api-v2"
import { testFirebaseConnection } from "@/lib/firebase-simple"
import { ref, uploadBytes, getDownloadURL } from "firebase/storage"
import { storage } from "@/lib/firebase"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Upload, X, Play, Pause, Loader2, CheckCircle2, History, RotateCcw,
  FileAudio, Menu, ArrowLeft, Home, User, Brain, Save, Eye, Smartphone,
  Monitor, Mic, Cloud, Download, Volume2, VolumeX, Trash2, Edit3,
  Clock, FileText, Settings, Zap, Star, Shield
} from "lucide-react"
import { AudioRecorder } from "@/components/audio-recorder"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Slider } from "@/components/ui/slider"
import { useFocusedLanguage } from "@/components/focused-language-provider"
import { FocusedLanguageProvider } from "@/components/focused-language-provider"
import { FocusedLanguageSelector } from "@/components/focused-language-selector"
import Link from "next/link"

const MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB
const SUPPORTED_AUDIO_FORMATS = ['audio/wav', 'audio/mp3', 'audio/mpeg', 'audio/ogg', 'audio/webm']



// Device detection hook
const useDeviceDetection = () => {
  const [isMobile, setIsMobile] = useState(false)
  const [isTablet, setIsTablet] = useState(false)

  useEffect(() => {
    const checkDevice = () => {
      const width = window.innerWidth
      setIsMobile(width < 768)
      setIsTablet(width >= 768 && width < 1024)
    }

    checkDevice()
    window.addEventListener('resize', checkDevice)
    return () => window.removeEventListener('resize', checkDevice)
  }, [])

  return { isMobile, isTablet, isDesktop: !isMobile && !isTablet }
}

// Drag and drop hook
const useDragAndDrop = (onDrop: (files: FileList) => void) => {
  const [isDragging, setIsDragging] = useState(false)
  const dragCounter = useRef(0)

  const handleDragEnter = useCallback((e: DragEvent) => {
    e.preventDefault()
    dragCounter.current++
    if (e.dataTransfer?.items && e.dataTransfer.items.length > 0) {
      setIsDragging(true)
    }
  }, [])

  const handleDragLeave = useCallback((e: DragEvent) => {
    e.preventDefault()
    dragCounter.current--
    if (dragCounter.current === 0) {
      setIsDragging(false)
    }
  }, [])

  const handleDragOver = useCallback((e: DragEvent) => {
    e.preventDefault()
  }, [])

  const handleDrop = useCallback((e: DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
    dragCounter.current = 0

    if (e.dataTransfer?.files && e.dataTransfer.files.length > 0) {
      onDrop(e.dataTransfer.files)
    }
  }, [onDrop])

  useEffect(() => {
    document.addEventListener('dragenter', handleDragEnter)
    document.addEventListener('dragleave', handleDragLeave)
    document.addEventListener('dragover', handleDragOver)
    document.addEventListener('drop', handleDrop)

    return () => {
      document.removeEventListener('dragenter', handleDragEnter)
      document.removeEventListener('dragleave', handleDragLeave)
      document.removeEventListener('dragover', handleDragOver)
      document.removeEventListener('drop', handleDrop)
    }
  }, [handleDragEnter, handleDragLeave, handleDragOver, handleDrop])

  return { isDragging }
}

interface TranscriptionMetadata {
  audio_id: string
  content: string
  created_at: string
  dialect: null
  is_flagged: boolean
  language: string
  trained_asr: boolean
  transcription_source: string
  tts_trained: boolean
  type: string
  updated_at: string
  user_id: string
}

interface AudioMetadata {
  action: string
  audio_url: string
  created_at: string
  duration: number
  feedback: null
  format: string
  gender: string
  id: string
  is_flagged: boolean
  last_trained: null
  last_trained_at: null
  reviewed_at: null
  reviewed_by: null
  source: string
  title: string
  trained_asr: boolean
  tts_trained: boolean
  updated_at: string
  user_id: string
  transcription: TranscriptionMetadata
}

function UploadPageContent() {
  const { user } = useAuth()
  const { toast } = useToast()
  const router = useRouter()
  const { t, isRTL } = useFocusedLanguage()
  const { isMobile, isTablet, isDesktop } = useDeviceDetection()

  // Form state
  const [audioFile, setAudioFile] = useState<File | null>(null)
  const [recordedBlob, setRecordedBlob] = useState<Blob | null>(null)
  const [title, setTitle] = useState("")
  const [transcription, setTranscription] = useState("")
  const [gender, setGender] = useState("")
  const [duration, setDuration] = useState(0)
  const [format, setFormat] = useState("")

  // UI state
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isSuccess, setIsSuccess] = useState(false)
  const [showSuccessDialog, setShowSuccessDialog] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [isCalculatingDuration, setIsCalculatingDuration] = useState(false)
  const [volume, setVolume] = useState([1])
  const [playbackRate, setPlaybackRate] = useState([1])

  // Refs
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const audioUrlRef = useRef<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement | null>(null)



  // Drag and drop functionality
  const { isDragging } = useDragAndDrop((files) => {
    if (files.length > 0) {
      handleFileSelection(files[0])
    }
  })



  const calculateDuration = async (file: File | Blob): Promise<number> => {
    return new Promise((resolve) => {
      if (file instanceof File) {
        // For uploaded files
        const audio = new Audio()
        const objectUrl = URL.createObjectURL(file)
        
        audio.addEventListener('loadedmetadata', () => {
          URL.revokeObjectURL(objectUrl)
          resolve(Math.round(audio.duration))
        })

        audio.addEventListener('error', () => {
          URL.revokeObjectURL(objectUrl)
          resolve(0)
        })

        audio.src = objectUrl
      } else {
        // For recorded blobs
        const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
        const reader = new FileReader()
        
        reader.onload = async () => {
          try {
            const arrayBuffer = reader.result as ArrayBuffer
            const audioBuffer = await audioContext.decodeAudioData(arrayBuffer)
            resolve(Math.round(audioBuffer.duration))
          } catch (error) {
            console.error('Error decoding audio:', error)
            resolve(0)
          }
        }
        
        reader.onerror = () => {
          console.error('Error reading blob:', reader.error)
          resolve(0)
        }
        
        reader.readAsArrayBuffer(file)
      }
    })
  }

  const validateFile = (file: File): string | null => {
    if (file.size > MAX_FILE_SIZE) {
      return `File size must be less than ${MAX_FILE_SIZE / (1024 * 1024)}MB`
    }

    if (!SUPPORTED_AUDIO_FORMATS.includes(file.type)) {
      return 'Unsupported audio format. Please upload WAV, MP3, or OGG files.'
    }

    return null
  }

  const handleFileSelection = async (file: File) => {
    const error = validateFile(file)
    if (error) {
      setError(error)
      return
    }

    setError(null)
    setAudioFile(file)
    setRecordedBlob(null)
    setFormat(file.type)

    // Create audio preview
    if (audioUrlRef.current) {
      URL.revokeObjectURL(audioUrlRef.current)
    }
    audioUrlRef.current = URL.createObjectURL(file)

    // Calculate duration
    setIsCalculatingDuration(true)
    const fileDuration = await calculateDuration(file)
    setDuration(fileDuration)
    setIsCalculatingDuration(false)
    setShowPreview(true)
  }

  const clearAudio = () => {
    setAudioFile(null)
    setRecordedBlob(null)
    setDuration(0)
    setError(null)
    setShowPreview(false)
    if (audioUrlRef.current) {
      URL.revokeObjectURL(audioUrlRef.current)
      audioUrlRef.current = null
    }
    if (audioRef.current) {
      audioRef.current.pause()
      setIsPlaying(false)
    }
  }

  const handleSubmit = async (e?: React.FormEvent) => {
    if (e) e.preventDefault()
    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to upload recordings",
        variant: "destructive",
      })
      return
    }

    if (!audioFile && !recordedBlob) {
      toast({
        title: "Error",
        description: "Please record or upload audio before submitting",
        variant: "destructive",
      })
      return
    }

    if (!title) {
      toast({
        title: "Error",
        description: "Please provide a title for your recording",
        variant: "destructive",
      })
      return
    }

    if (!gender) {
      toast({
        title: "Error",
        description: "Please select the speaker's gender",
        variant: "destructive",
      })
      return
    }

    if (!transcription) {
      toast({
        title: "Error",
        description: "Please provide the transcription of the audio",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)
    setUploadProgress(0)

    try {
      const fileToUpload = audioFile || new File([recordedBlob!], `${title}.wav`, { type: "audio/wav" })

      // Upload file to Firebase Storage first
      const timestamp = Date.now()
      const fileExtension = fileToUpload.name.split('.').pop()
      const fileName = `${user.id}/${timestamp}_${title}.${fileExtension}`
      const storageRef = ref(storage, `audio/${fileName}`)

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return 90
          }
          return prev + 10
        })
      }, 500)

      // Upload the file
      const snapshot = await uploadBytes(storageRef, fileToUpload)
      const downloadURL = await getDownloadURL(snapshot.ref)

      // Create hierarchical audio record with transcription
      const audioData = {
        title: title,
        duration: duration,
        format: format || fileToUpload.type,
        user_id: user.id,
        source: "direct_upload",
        audio_url: downloadURL,
        transcription: {
          content: transcription,
          language: "masalit",
          transcription_source: "human/manual",
          type: "txt"
        },
        metadata: {
          gender: gender,
          language: "masalit",
          recording_context: "direct_upload"
        }
      }

      // Test Firebase connection first
      const connectionTest = await testFirebaseConnection(user.id)
      console.log('Firebase connection test result:', connectionTest)

      if (!connectionTest) {
        throw new Error('Firebase connection failed')
      }

      console.log('Attempting to create hierarchical audio with data:', audioData)

      const result = await createAudioWithTranscription(audioData)

      console.log('Audio creation result:', result)

      clearInterval(progressInterval)
      setUploadProgress(100)

      setIsSuccess(true)
      setShowSuccessDialog(true)

      toast({
        title: "Recording submitted successfully",
        description: "Your recording has been submitted for review.",
      })
    } catch (error: any) {
      toast({
        title: "Error submitting recording",
        description: error.message || "There was an error submitting your recording. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
      setUploadProgress(0)
    }
  }

  // Desktop Navigation Component
  const DesktopNavigation = () => (
    <div className="hidden md:block mb-8">
      <div className="flex items-center justify-center space-x-8">
        <Link
          href="/dashboard"
          className="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-white/50 dark:hover:bg-slate-800/50 transition-colors"
        >
          <Home className="h-5 w-5 text-gray-600 dark:text-gray-300" />
          <span className="font-medium text-gray-700 dark:text-gray-200">{t('dashboard')}</span>
        </Link>
        <Link
          href="/dashboard/history"
          className="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-white/50 dark:hover:bg-slate-800/50 transition-colors"
        >
          <History className="h-5 w-5 text-gray-600 dark:text-gray-300" />
          <span className="font-medium text-gray-700 dark:text-gray-200">{t('viewMyRecordings')}</span>
        </Link>
        <div className="flex items-center space-x-2 px-4 py-2 rounded-lg bg-blue-100 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800">
          <FileAudio className="h-5 w-5 text-blue-600" />
          <span className="font-medium text-blue-600">{t('uploadAudio')}</span>
        </div>
        {user?.role === "admin" && (
          <>
            <Link
              href="/dashboard/upload/bulk"
              className="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-white/50 dark:hover:bg-slate-800/50 transition-colors"
            >
              <Upload className="h-5 w-5 text-orange-600 dark:text-orange-300" />
              <span className="font-medium text-orange-700 dark:text-orange-200">{t('bulkUpload')}</span>
            </Link>
            <Link
              href="/dashboard/ai"
              className="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-white/50 dark:hover:bg-slate-800/50 transition-colors"
            >
              <Brain className="h-5 w-5 text-gray-600 dark:text-gray-300" />
              <span className="font-medium text-gray-700 dark:text-gray-200">{t('aiTraining')}</span>
            </Link>
          </>
        )}
      </div>
    </div>
  )

  // Mobile Navigation Component
  const MobileNavigation = () => (
    <div className="md:hidden">
      <div className="flex items-center justify-between p-4 bg-white/90 backdrop-blur-sm border-b border-gray-200 dark:bg-slate-900/90 dark:border-slate-700">
        <div className="flex items-center space-x-3">
          <Link href="/dashboard" className="p-2 hover:bg-gray-100 dark:hover:bg-slate-800 rounded-lg transition-colors">
            <ArrowLeft className="h-5 w-5 text-gray-600 dark:text-gray-300" />
          </Link>
          <div>
            <h1 className="text-lg font-semibold text-gray-900 dark:text-white">{t('uploadAudio')}</h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">{t('recordOrUpload')}</p>
          </div>
        </div>
        <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
          <SheetTrigger asChild>
            <Button variant="ghost" size="sm" className="p-2">
              <Menu className="h-5 w-5" />
            </Button>
          </SheetTrigger>
          <SheetContent side="right" className="w-80">
            <SheetHeader>
              <SheetTitle>Navigation</SheetTitle>
              <SheetDescription>
                Access your dashboard and recordings
              </SheetDescription>
            </SheetHeader>
            <div className="mt-6 space-y-4">
              <Link
                href="/dashboard"
                className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-800 transition-colors"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <Home className="h-5 w-5 text-gray-500" />
                <span className="font-medium">Dashboard</span>
              </Link>
              <Link
                href="/dashboard/history"
                className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-800 transition-colors"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <History className="h-5 w-5 text-gray-500" />
                <span className="font-medium">My Recordings</span>
              </Link>
              <Link
                href="/dashboard/upload"
                className="flex items-center space-x-3 p-3 rounded-lg bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <FileAudio className="h-5 w-5 text-blue-600" />
                <span className="font-medium text-blue-600">Upload Audio</span>
              </Link>
              {user?.role === "admin" && (
                <Link
                  href="/dashboard/ai"
                  className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-800 transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <Brain className="h-5 w-5 text-gray-500" />
                  <span className="font-medium">AI Training</span>
                </Link>
              )}
            </div>
          </SheetContent>
        </Sheet>
      </div>
    </div>
  )

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFileSelection(files[0])
    }
  }

  const handleRecordingComplete = async (blob: Blob) => {
    setRecordedBlob(blob)
    setAudioFile(null)
    setError(null)
    setFormat("audio/wav")

    // Create audio preview
    if (audioUrlRef.current) {
      URL.revokeObjectURL(audioUrlRef.current)
    }
    audioUrlRef.current = URL.createObjectURL(blob)

    // Calculate duration
    setIsCalculatingDuration(true)
    const recordingDuration = await calculateDuration(blob)
    setDuration(recordingDuration)
    setIsCalculatingDuration(false)
    setShowPreview(true)
  }

  const handleAudioPlay = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause()
      } else {
        audioRef.current.play()
      }
      setIsPlaying(!isPlaying)
    }
  }

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (audioUrlRef.current) {
        URL.revokeObjectURL(audioUrlRef.current)
      }
    }
  }, [])

  const resetForm = () => {
    setTitle("")
    setTranscription("")
    setGender("")
    setDuration(0)
    setFormat("")
    clearAudio()
    setIsSuccess(false)
    setError(null)
  }

  const handleViewRecordings = () => {
    setShowSuccessDialog(false)
    router.push("/dashboard/history")
  }

  const handleRecordAnother = () => {
    setShowSuccessDialog(false)
    resetForm()
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-md mx-auto">
            <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardContent className="pt-6">
                <div className="text-center">
                  <FileAudio className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold text-red-500 mb-2">Authentication Required</h3>
                  <p className="text-muted-foreground">Please log in to upload recordings</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Language Selector */}
      <div className="absolute top-4 right-4 z-50">
        <FocusedLanguageSelector showText={true} />
      </div>

      {/* Mobile Navigation */}
      {isMobile && (
        <div className="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-200 dark:bg-slate-900/95 dark:border-slate-700">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center space-x-3">
              <Link href="/dashboard" className="p-2 hover:bg-gray-100 dark:hover:bg-slate-800 rounded-lg transition-colors">
                <ArrowLeft className="h-5 w-5 text-gray-600 dark:text-gray-300" />
              </Link>
              <div>
                <h1 className="text-lg font-semibold text-gray-900 dark:text-white">{t('uploadAudio')}</h1>
                <div className="flex items-center space-x-2">
                  <Smartphone className="h-3 w-3 text-blue-500" />
                  <span className="text-xs text-gray-500 dark:text-gray-400">Mobile Optimized</span>
                </div>
              </div>
            </div>
            <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm" className="p-2">
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-80">
                <SheetHeader>
                  <SheetTitle>Navigation</SheetTitle>
                  <SheetDescription>Access your dashboard and recordings</SheetDescription>
                </SheetHeader>
                <div className="mt-6 space-y-4">
                  <Link href="/dashboard" className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-800 transition-colors" onClick={() => setIsMobileMenuOpen(false)}>
                    <Home className="h-5 w-5 text-gray-500" />
                    <span className="font-medium">Dashboard</span>
                  </Link>
                  <Link href="/dashboard/history" className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-800 transition-colors" onClick={() => setIsMobileMenuOpen(false)}>
                    <History className="h-5 w-5 text-gray-500" />
                    <span className="font-medium">My Recordings</span>
                  </Link>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      )}

      {/* Desktop Navigation */}
      {isDesktop && (
        <div className="mb-8 pt-8">
          <div className="flex items-center justify-center space-x-8">
            <Link href="/dashboard" className="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-white/50 dark:hover:bg-slate-800/50 transition-colors">
              <Home className="h-5 w-5 text-gray-600 dark:text-gray-300" />
              <span className="font-medium text-gray-700 dark:text-gray-200">{t('dashboard')}</span>
            </Link>
            <Link href="/dashboard/history" className="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-white/50 dark:hover:bg-slate-800/50 transition-colors">
              <History className="h-5 w-5 text-gray-600 dark:text-gray-300" />
              <span className="font-medium text-gray-700 dark:text-gray-200">{t('viewMyRecordings')}</span>
            </Link>
            <div className="flex items-center space-x-2 px-4 py-2 rounded-lg bg-blue-100 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800">
              <FileAudio className="h-5 w-5 text-blue-600" />
              <span className="font-medium text-blue-600">{t('uploadAudio')}</span>
              <Badge variant="secondary" className="ml-2">
                <Monitor className="h-3 w-3 mr-1" />
                Desktop
              </Badge>
            </div>
          </div>
        </div>
      )}

      <div className="container mx-auto px-4 py-4 md:py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
            {t('uploadAudio')}
          </h1>
          <p className="text-gray-600 dark:text-gray-300 text-lg">
            Record or upload your audio with our enhanced interface
          </p>
          <div className="flex items-center justify-center space-x-4 mt-4">

            <Badge variant="outline" className="flex items-center space-x-1">
              <Cloud className="h-3 w-3" />
              <span>Cloud Storage</span>
            </Badge>
            <Badge variant="outline" className="flex items-center space-x-1">
              <Shield className="h-3 w-3" />
              <span>Secure</span>
            </Badge>
          </div>
        </div>



        {/* Drag and Drop Overlay */}
        {isDragging && (
          <div className="fixed inset-0 z-50 bg-blue-500/20 backdrop-blur-sm flex items-center justify-center">
            <div className="bg-white dark:bg-slate-800 rounded-lg p-8 shadow-xl border-2 border-dashed border-blue-500">
              <div className="text-center">
                <Upload className="h-12 w-12 text-blue-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Drop your audio file here</h3>
                <p className="text-gray-500 dark:text-gray-400">Release to upload</p>
              </div>
            </div>
          </div>
        )}

        {/* Main Upload Interface */}
        <div className="max-w-4xl mx-auto">
          <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-sm dark:bg-slate-800/80">
            <CardHeader>
              <div>
                <CardTitle className="flex items-center space-x-2">
                  <FileAudio className="h-5 w-5" />
                  <span>Audio Upload</span>
                </CardTitle>
                <CardDescription>Choose your preferred method to add audio</CardDescription>
              </div>
            </CardHeader>
            <CardContent>
              {/* Success State */}
              {isSuccess && (
                <div className="text-center py-6 md:py-8">
                  <div className="inline-flex items-center justify-center w-12 h-12 md:w-16 md:h-16 bg-green-100 rounded-full mb-4">
                    <CheckCircle2 className="h-6 w-6 md:h-8 md:w-8 text-green-600" />
                  </div>
                  <h3 className="text-lg md:text-xl font-semibold text-green-600 mb-2">{t('recordingSubmittedSuccess')}</h3>
                  <p className="text-sm md:text-base text-muted-foreground mb-6">{t('recordingSubmittedForReview')}</p>
                  <div className="flex flex-col gap-3 max-w-sm mx-auto">
                    <Button onClick={handleRecordAnother} className="w-full h-11">
                      <RotateCcw className="mr-2 h-4 w-4" />
                      {t('recordAnother')}
                    </Button>
                    <Button onClick={handleViewRecordings} variant="outline" className="w-full h-11">
                      <History className="mr-2 h-4 w-4" />
                      {t('viewMyRecordings')}
                    </Button>
                  </div>
                </div>
              )}

              {/* Form */}
              {!isSuccess && (
                <div className="space-y-4 md:space-y-6">
              <Tabs defaultValue="record" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="record" className="flex items-center space-x-2">
                    <Mic className="h-4 w-4" />
                    <span>Record Audio</span>
                  </TabsTrigger>
                  <TabsTrigger value="upload" className="flex items-center space-x-2">
                    <Upload className="h-4 w-4" />
                    <span>Upload File</span>
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="record" className="space-y-6">
                  {/* Audio Recorder */}
                  <div className="border rounded-lg p-6 bg-gray-50 dark:bg-slate-700/50">
                    <AudioRecorder
                      onRecordingComplete={handleRecordingComplete}
                      className="w-full"
                    />
                  </div>
                </TabsContent>

                <TabsContent value="upload" className="space-y-6">
                  {/* File Upload Area */}
                  <div
                    className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                      isDragging
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-950/20'
                        : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                    }`}
                  >
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="audio/*"
                      onChange={handleFileChange}
                      className="hidden"
                    />
                    <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                      Drop your audio file here
                    </h3>
                    <p className="text-gray-500 dark:text-gray-400 mb-4">
                      or click to browse files
                    </p>
                    <Button
                      onClick={() => fileInputRef.current?.click()}
                      variant="outline"
                      className="mb-4"
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Choose File
                    </Button>
                    <div className="text-xs text-gray-400">
                      Supports: WAV, MP3, OGG • Max size: 50MB
                    </div>
                  </div>
                </TabsContent>
              </Tabs>

              {/* Audio Preview */}
              {(audioFile || recordedBlob) && showPreview && (
                <Card className="border border-blue-200 dark:border-blue-800 bg-blue-50/50 dark:bg-blue-950/20">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center space-x-2 text-blue-700 dark:text-blue-300">
                        <Eye className="h-5 w-5" />
                        <span>Audio Preview</span>
                      </CardTitle>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowPreview(false)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {/* Audio Info */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <Label className="text-xs text-gray-500">Duration</Label>
                          <div className="flex items-center space-x-1">
                            <Clock className="h-3 w-3" />
                            <span>{isCalculatingDuration ? 'Calculating...' : `${duration}s`}</span>
                          </div>
                        </div>
                        <div>
                          <Label className="text-xs text-gray-500">Format</Label>
                          <div className="flex items-center space-x-1">
                            <FileAudio className="h-3 w-3" />
                            <span>{format.split('/')[1]?.toUpperCase() || 'Unknown'}</span>
                          </div>
                        </div>
                        <div>
                          <Label className="text-xs text-gray-500">Size</Label>
                          <div className="flex items-center space-x-1">
                            <Download className="h-3 w-3" />
                            <span>{audioFile ? `${(audioFile.size / 1024 / 1024).toFixed(1)}MB` : 'N/A'}</span>
                          </div>
                        </div>
                        <div>
                          <Label className="text-xs text-gray-500">Source</Label>
                          <div className="flex items-center space-x-1">
                            {audioFile ? <Upload className="h-3 w-3" /> : <Mic className="h-3 w-3" />}
                            <span>{audioFile ? 'Upload' : 'Recording'}</span>
                          </div>
                        </div>
                      </div>

                      {/* Audio Player */}
                      {audioUrlRef.current && (
                        <div className="space-y-3">
                          <audio
                            ref={audioRef}
                            src={audioUrlRef.current}
                            onEnded={() => setIsPlaying(false)}
                            onPlay={() => setIsPlaying(true)}
                            onPause={() => setIsPlaying(false)}
                            className="hidden"
                          />

                          <div className="flex items-center space-x-4">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                if (audioRef.current) {
                                  if (isPlaying) {
                                    audioRef.current.pause()
                                  } else {
                                    audioRef.current.play()
                                  }
                                }
                              }}
                            >
                              {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                            </Button>

                            <div className="flex-1 space-y-2">
                              <div className="flex items-center space-x-2">
                                <Volume2 className="h-4 w-4" />
                                <Slider
                                  value={volume}
                                  onValueChange={(value) => {
                                    setVolume(value)
                                    if (audioRef.current) {
                                      audioRef.current.volume = value[0]
                                    }
                                  }}
                                  max={1}
                                  step={0.1}
                                  className="flex-1"
                                />
                              </div>

                              <div className="flex items-center space-x-2">
                                <Zap className="h-4 w-4" />
                                <Slider
                                  value={playbackRate}
                                  onValueChange={(value) => {
                                    setPlaybackRate(value)
                                    if (audioRef.current) {
                                      audioRef.current.playbackRate = value[0]
                                    }
                                  }}
                                  min={0.5}
                                  max={2}
                                  step={0.1}
                                  className="flex-1"
                                />
                                <span className="text-xs text-gray-500">{playbackRate[0]}x</span>
                              </div>
                            </div>

                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setAudioFile(null)
                                setRecordedBlob(null)
                                setShowPreview(false)
                                if (audioUrlRef.current) {
                                  URL.revokeObjectURL(audioUrlRef.current)
                                  audioUrlRef.current = null
                                }
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Form Fields */}
              {(audioFile || recordedBlob) && (
                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <FileText className="h-5 w-5" />
                      <span>Audio Details</span>
                    </CardTitle>
                    <CardDescription>
                      Provide information about your audio recording
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Title */}
                    <div className="space-y-2">
                      <Label htmlFor="title" className="flex items-center space-x-2">
                        <Edit3 className="h-4 w-4" />
                        <span>Title *</span>
                      </Label>
                      <Input
                        id="title"
                        value={title}
                        onChange={(e) => setTitle(e.target.value)}
                        placeholder="Enter a descriptive title for your recording"
                        className="text-lg"
                      />
                    </div>

                    {/* Gender Selection */}
                    <div className="space-y-2">
                      <Label htmlFor="gender" className="flex items-center space-x-2">
                        <User className="h-4 w-4" />
                        <span>Speaker Gender *</span>
                      </Label>
                      <Select value={gender} onValueChange={setGender}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select speaker gender" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="male">Male</SelectItem>
                          <SelectItem value="female">Female</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Transcription */}
                    <div className="space-y-2">
                      <Label htmlFor="transcription" className="flex items-center space-x-2">
                        <FileText className="h-4 w-4" />
                        <span>Transcription *</span>
                      </Label>
                      <Textarea
                        id="transcription"
                        value={transcription}
                        onChange={(e) => setTranscription(e.target.value)}
                        placeholder="Enter the text transcription of your audio..."
                        className="min-h-[120px] resize-none"
                        rows={isMobile ? 4 : 6}
                      />
                      <div className="flex justify-between text-xs text-gray-500">
                        <span>{transcription.length} characters</span>
                        <span>Be as accurate as possible</span>
                      </div>
                    </div>

                    {/* Error Display */}
                    {error && (
                      <Alert variant="destructive">
                        <AlertDescription>{error}</AlertDescription>
                      </Alert>
                    )}

                    {/* Submit Button */}
                    <div className="flex flex-col space-y-4">
                      <Button
                        onClick={handleSubmit}
                        disabled={isSubmitting || !title || !transcription || !gender}
                        className="w-full h-12 text-lg"
                      >
                        {isSubmitting ? (
                          <>
                            <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                            Uploading...
                          </>
                        ) : (
                          <>
                            <Cloud className="h-5 w-5 mr-2" />
                            Submit Recording
                          </>
                        )}
                      </Button>

                      {/* Upload Progress */}
                      {isSubmitting && uploadProgress > 0 && (
                        <div className="space-y-2">
                          <Progress value={uploadProgress} className="w-full" />
                          <div className="text-center text-sm text-gray-500">
                            {uploadProgress}% uploaded
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Success Dialog */}
          <Dialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle className="flex items-center space-x-2">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  <span>Upload Successful!</span>
                </DialogTitle>
                <DialogDescription>
                  Your audio recording has been successfully uploaded and is now being processed.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter className="flex-col space-y-2 sm:flex-row sm:space-y-0">
                <Button variant="outline" onClick={() => setShowSuccessDialog(false)}>
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Upload Another
                </Button>
                <Button onClick={() => router.push('/dashboard/history')}>
                  <History className="h-4 w-4 mr-2" />
                  View My Recordings
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
  )
}

export default function UploadPage() {
  return (
    <FocusedLanguageProvider>
      <UploadPageContent />
    </FocusedLanguageProvider>
  )
}