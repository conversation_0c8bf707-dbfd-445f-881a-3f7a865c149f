"""
Users API endpoints v2 - Using hierarchical Firestore collections
"""

import logging
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel, EmailStr
from typing import Dict, Any, Optional, List
from datetime import datetime

from backend.services.firebase_clean import clean_firebase_service
from google.cloud import firestore

logger = logging.getLogger(__name__)

router = APIRouter()

class UserProfile(BaseModel):
    email: str
    display_name: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone: Optional[str] = None
    location: Optional[str] = None
    preferred_language: str = "masalit"
    role: str = "user"

class UserPreferences(BaseModel):
    language: str = "masalit"
    theme: str = "light"
    notifications_enabled: bool = True
    email_notifications: bool = True
    auto_save: bool = False
    recording_quality: str = "high"

class UserActivity(BaseModel):
    action: str
    details: Optional[Dict[str, Any]] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None

# ==================== USER PROFILE ENDPOINTS ====================

@router.post("/profile")
async def create_user_profile(user_id: str, profile: UserProfile):
    """Create or update user profile using hierarchical structure"""
    try:
        clean_firebase_service.initialize()
        
        # Prepare profile data
        profile_data = {
            **profile.model_dump(),
            'user_id': user_id,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'email_verified': False,
            'is_active': True
        }
        
        # Save to users collection
        user_ref = clean_firebase_service.db.collection('users').document(user_id)
        user_ref.set(profile_data, merge=True)
        
        # Initialize user preferences
        default_preferences = UserPreferences().model_dump()
        preferences_ref = user_ref.collection('preferences').document('settings')
        preferences_ref.set({
            **default_preferences,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        })
        
        # Initialize user statistics
        stats_ref = user_ref.collection('statistics').document('summary')
        stats_ref.set({
            'total_uploads': 0,
            'approved_uploads': 0,
            'rejected_uploads': 0,
            'total_transcriptions': 0,
            'total_duration_seconds': 0,
            'last_activity': datetime.now().isoformat(),
            'created_at': datetime.now().isoformat()
        })
        
        logger.info(f"User profile created/updated: {user_id}")
        
        return {"message": "User profile created successfully", "user_id": user_id}
        
    except Exception as e:
        logger.error(f"Error creating user profile: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{user_id}/profile")
async def get_user_profile(user_id: str):
    """Get user profile"""
    try:
        clean_firebase_service.initialize()
        
        # Get user document
        user_ref = clean_firebase_service.db.collection('users').document(user_id)
        user_doc = user_ref.get()
        
        if not user_doc.exists:
            raise HTTPException(status_code=404, detail="User not found")
        
        user_data = user_doc.to_dict()
        user_data['user_id'] = user_id
        
        # Get preferences
        preferences_ref = user_ref.collection('preferences').document('settings')
        preferences_doc = preferences_ref.get()
        if preferences_doc.exists:
            user_data['preferences'] = preferences_doc.to_dict()
        
        # Get statistics
        stats_ref = user_ref.collection('statistics').document('summary')
        stats_doc = stats_ref.get()
        if stats_doc.exists:
            user_data['statistics'] = stats_doc.to_dict()
        
        return user_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user profile: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/{user_id}/profile")
async def update_user_profile(user_id: str, profile: UserProfile):
    """Update user profile"""
    try:
        clean_firebase_service.initialize()
        
        # Check if user exists
        user_ref = clean_firebase_service.db.collection('users').document(user_id)
        user_doc = user_ref.get()
        
        if not user_doc.exists:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Update profile
        profile_data = {
            **profile.model_dump(),
            'updated_at': datetime.now().isoformat()
        }
        
        user_ref.update(profile_data)
        
        logger.info(f"User profile updated: {user_id}")
        
        return {"message": "User profile updated successfully", "user_id": user_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user profile: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ==================== USER PREFERENCES ENDPOINTS ====================

@router.get("/{user_id}/preferences")
async def get_user_preferences(user_id: str):
    """Get user preferences"""
    try:
        clean_firebase_service.initialize()
        
        # Get preferences
        preferences_ref = (clean_firebase_service.db.collection('users')
                          .document(user_id)
                          .collection('preferences')
                          .document('settings'))
        
        preferences_doc = preferences_ref.get()
        
        if not preferences_doc.exists:
            # Return default preferences
            return UserPreferences().model_dump()
        
        return preferences_doc.to_dict()
        
    except Exception as e:
        logger.error(f"Error getting user preferences: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/{user_id}/preferences")
async def update_user_preferences(user_id: str, preferences: UserPreferences):
    """Update user preferences"""
    try:
        clean_firebase_service.initialize()
        
        # Update preferences
        preferences_data = {
            **preferences.model_dump(),
            'updated_at': datetime.now().isoformat()
        }
        
        preferences_ref = (clean_firebase_service.db.collection('users')
                          .document(user_id)
                          .collection('preferences')
                          .document('settings'))
        
        preferences_ref.set(preferences_data, merge=True)
        
        logger.info(f"User preferences updated: {user_id}")
        
        return {"message": "User preferences updated successfully", "user_id": user_id}
        
    except Exception as e:
        logger.error(f"Error updating user preferences: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ==================== USER ACTIVITY ENDPOINTS ====================

@router.post("/{user_id}/activity")
async def log_user_activity(user_id: str, activity: UserActivity):
    """Log user activity"""
    try:
        clean_firebase_service.initialize()
        
        # Log activity
        activity_data = {
            **activity.model_dump(),
            'user_id': user_id,
            'timestamp': datetime.now().isoformat()
        }
        
        # Add to activity logs subcollection
        activity_ref = (clean_firebase_service.db.collection('users')
                       .document(user_id)
                       .collection('activity_logs')
                       .document())
        
        activity_ref.set(activity_data)
        
        # Update last activity in statistics
        stats_ref = (clean_firebase_service.db.collection('users')
                    .document(user_id)
                    .collection('statistics')
                    .document('summary'))
        
        stats_ref.update({
            'last_activity': datetime.now().isoformat()
        })
        
        logger.info(f"User activity logged: {user_id} - {activity.action}")
        
        return {"message": "Activity logged successfully", "user_id": user_id}
        
    except Exception as e:
        logger.error(f"Error logging user activity: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{user_id}/activity")
async def get_user_activity(user_id: str, limit: int = 50):
    """Get user activity history"""
    try:
        clean_firebase_service.initialize()
        
        # Get activity logs
        activity_query = (clean_firebase_service.db.collection('users')
                         .document(user_id)
                         .collection('activity_logs')
                         .order_by('timestamp', direction=firestore.Query.DESCENDING)
                         .limit(limit))
        
        activity_docs = list(activity_query.stream())
        
        activities = []
        for doc in activity_docs:
            activity_data = doc.to_dict()
            activity_data['activity_id'] = doc.id
            activities.append(activity_data)
        
        return {
            "activities": activities,
            "total": len(activities),
            "user_id": user_id
        }
        
    except Exception as e:
        logger.error(f"Error getting user activity: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ==================== USER STATISTICS ENDPOINTS ====================

@router.get("/{user_id}/statistics")
async def get_user_statistics(user_id: str):
    """Get user statistics"""
    try:
        clean_firebase_service.initialize()
        
        # Get statistics
        stats_ref = (clean_firebase_service.db.collection('users')
                    .document(user_id)
                    .collection('statistics')
                    .document('summary'))
        
        stats_doc = stats_ref.get()
        
        if not stats_doc.exists:
            # Return default statistics
            return {
                'total_uploads': 0,
                'approved_uploads': 0,
                'rejected_uploads': 0,
                'total_transcriptions': 0,
                'total_duration_seconds': 0,
                'last_activity': None
            }
        
        return stats_doc.to_dict()
        
    except Exception as e:
        logger.error(f"Error getting user statistics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{user_id}/statistics/update")
async def update_user_statistics(user_id: str, stats_update: Dict[str, Any]):
    """Update user statistics"""
    try:
        clean_firebase_service.initialize()
        
        # Update statistics
        stats_data = {
            **stats_update,
            'updated_at': datetime.now().isoformat()
        }
        
        stats_ref = (clean_firebase_service.db.collection('users')
                    .document(user_id)
                    .collection('statistics')
                    .document('summary'))
        
        stats_ref.update(stats_data)
        
        logger.info(f"User statistics updated: {user_id}")
        
        return {"message": "Statistics updated successfully", "user_id": user_id}
        
    except Exception as e:
        logger.error(f"Error updating user statistics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ==================== USER LISTING ENDPOINTS ====================

@router.get("/list")
async def list_users(
    role: Optional[str] = None,
    is_active: Optional[bool] = None,
    limit: int = 50,
    offset: int = 0
):
    """List users with filtering"""
    try:
        clean_firebase_service.initialize()
        
        # Build query
        query = clean_firebase_service.db.collection('users')
        
        if role:
            query = query.where('role', '==', role)
        if is_active is not None:
            query = query.where('is_active', '==', is_active)
        
        # Apply pagination
        query = query.limit(limit).offset(offset)
        
        # Get documents
        docs = list(query.stream())
        
        users = []
        for doc in docs:
            user_data = doc.to_dict()
            user_data['user_id'] = doc.id
            
            # Get basic statistics
            stats_ref = doc.reference.collection('statistics').document('summary')
            stats_doc = stats_ref.get()
            if stats_doc.exists:
                user_data['statistics'] = stats_doc.to_dict()
            
            users.append(user_data)
        
        return {
            "users": users,
            "total": len(users),
            "limit": limit,
            "offset": offset
        }
        
    except Exception as e:
        logger.error(f"Error listing users: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{user_id}")
async def delete_user(user_id: str):
    """Delete user and all associated data"""
    try:
        clean_firebase_service.initialize()
        
        # Check if user exists
        user_ref = clean_firebase_service.db.collection('users').document(user_id)
        user_doc = user_ref.get()
        
        if not user_doc.exists:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Delete all subcollections first
        subcollections = ['preferences', 'statistics', 'activity_logs']
        
        for subcollection in subcollections:
            subcol_ref = user_ref.collection(subcollection)
            docs = list(subcol_ref.stream())
            
            for doc in docs:
                doc.reference.delete()
        
        # Delete main user document
        user_ref.delete()
        
        logger.info(f"User deleted: {user_id}")
        
        return {"message": "User deleted successfully", "user_id": user_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting user: {e}")
        raise HTTPException(status_code=500, detail=str(e))
