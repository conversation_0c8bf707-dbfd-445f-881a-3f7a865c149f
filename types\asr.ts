export interface TrainingStatus {
  status: 'not_started' | 'training' | 'completed' | 'failed' | 'stopped' | 'idle';
  current_epoch: number;
  total_epochs: number;
  progress: number;
  samples_processed?: number;
  total_samples?: number;
  current_accuracy?: number;
  current_confidence?: number;
  current_wer?: number;
  current_cer?: number;
  current_ser?: number;
  error?: string;
  start_time?: string;
  end_time?: string;
  training_progress?: number;
  model_version?: string;
}

export interface TrainingSettings {
  epochs: number
  learning_rate: number
  number_of_samples: number
  model_name: string
  validation_split: number
  early_stopping_patience: number
  use_augmentation: boolean
  augmentation_params?: Record<string, any>
  eval_steps: number
  training_timeout?: number  // Training timeout in seconds (optional)
  use_existing_model?: boolean  // Whether to continue from existing model or start from base model
}

export interface ModelInfo {
  model_id: string | null
  version: string
  last_trained: string
  first_trained: string
  confidence: number
  accuracy: number
  wer: number
  cer: number
  ser: number
  samples_trained: number
  total_training_sessions: number
  status: 'active' | 'inactive' | 'training' | 'error'
  base_model: string
  model_path: string
  gcs_url: string
}

export interface TrainingSchedule {
  interval: 'daily' | 'weekly' | 'monthly'
  time: string
  enabled: boolean
}

export interface TrainingHistory {
  id: string
  trained_at: string
  model_version: string
  accuracy: number
  confidence: number
  epochs_completed: number
  samples_trained: number
}

export interface TrainingStats {
  total_approved: number
  untrained_samples: number
  trained_samples: number
  gender_distribution: {
    male: number
    female: number
    trained: {
      male: number
      female: number
    }
  }
  last_training: {
    date?: string
    accuracy?: number
    confidence?: number
    model_version?: string
  }
}

export interface ValidationResults {
  wer: number
  cer: number
  ser: number
  confidence: number
  samples_tested: number
  error_analysis: Array<{
    audio_id: string
    reference: string
    hypothesis: string
    metrics: {
      wer: number
      cer: number
      ser: number
      confidence: number
    }
  }>
  summary: {
    total_samples: number
    successful_samples: number
    failed_samples: number
    average_confidence: number
  }
}

export type ValidationType = 'full' | 'quick' | 'custom';

export interface ValidationConfig {
  language?: string;
  dialect?: string;
  max_samples?: number;
  min_confidence?: number;
}

export interface ValidationMetrics {
  wer: number;
  cer: number;
  ser: number;
  confidence: number;
  accuracy: number;
  perplexity?: number;
  latency?: number;
  memory_usage?: number;
  duration: number;
}

export interface ErrorAnalysis {
  audio_id: string;
  reference: string;
  hypothesis: string;
  metrics: ValidationMetrics;
  error_type: string;
  error_context: string;
  metadata: Record<string, any>;
}

export interface ValidationSummary {
  total_samples: number;
  successful_samples: number;
  failed_samples: number;
  average_confidence: number;
  error_distribution: Record<string, number>;
  performance_metrics: Record<string, number>;
}

export interface ValidationMetadata {
  validation_type: ValidationType;
  validation_method: string;
  validation_duration: string;
  model_version: string;
  dataset_version?: string;
  validation_config: Record<string, any>;
}

export interface ValidationResponse {
  id: string;
  timestamp: string;
  status: string;
  metrics: ValidationMetrics;
  error_analysis: ErrorAnalysis[];
  summary: ValidationSummary;
  metadata: ValidationMetadata;
  recommendations?: string[];
} 