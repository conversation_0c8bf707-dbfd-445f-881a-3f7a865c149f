"""
System monitoring API endpoints
"""

import psutil
import GPUtil
from fastapi import APIRouter, HTTPException
from datetime import datetime
from typing import Dict, Any

router = APIRouter()

@router.get("/system/status")
async def get_system_status():
    """Get current system status and resource usage"""
    try:
        # CPU information
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        cpu_freq = psutil.cpu_freq()
        
        # Memory information
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()
        
        # Disk information
        disk = psutil.disk_usage('/')
        
        # Network information
        network = psutil.net_io_counters()
        
        # GPU information
        gpus = []
        try:
            gpu_list = GPUtil.getGPUs()
            for gpu in gpu_list:
                gpus.append({
                    "id": gpu.id,
                    "name": gpu.name,
                    "load": round(gpu.load * 100, 1),
                    "memory_used": gpu.memoryUsed,
                    "memory_total": gpu.memoryTotal,
                    "memory_percent": round((gpu.memoryUsed / gpu.memoryTotal) * 100, 1),
                    "temperature": gpu.temperature
                })
        except Exception:
            gpus = []
        
        return {
            "timestamp": datetime.now().isoformat(),
            "cpu": {
                "usage_percent": cpu_percent,
                "count": cpu_count,
                "frequency": {
                    "current": cpu_freq.current if cpu_freq else None,
                    "min": cpu_freq.min if cpu_freq else None,
                    "max": cpu_freq.max if cpu_freq else None
                }
            },
            "memory": {
                "total": memory.total,
                "available": memory.available,
                "used": memory.used,
                "percent": memory.percent,
                "swap": {
                    "total": swap.total,
                    "used": swap.used,
                    "percent": swap.percent
                }
            },
            "disk": {
                "total": disk.total,
                "used": disk.used,
                "free": disk.free,
                "percent": round((disk.used / disk.total) * 100, 1)
            },
            "network": {
                "bytes_sent": network.bytes_sent,
                "bytes_recv": network.bytes_recv,
                "packets_sent": network.packets_sent,
                "packets_recv": network.packets_recv
            },
            "gpus": gpus
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get system status: {str(e)}")

@router.get("/system/processes")
async def get_system_processes():
    """Get information about running processes"""
    try:
        processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'status']):
            try:
                proc_info = proc.info
                if proc_info['cpu_percent'] > 0 or proc_info['memory_percent'] > 1:
                    processes.append({
                        "pid": proc_info['pid'],
                        "name": proc_info['name'],
                        "cpu_percent": round(proc_info['cpu_percent'], 2),
                        "memory_percent": round(proc_info['memory_percent'], 2),
                        "status": proc_info['status']
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        # Sort by CPU usage
        processes.sort(key=lambda x: x['cpu_percent'], reverse=True)
        
        return {
            "timestamp": datetime.now().isoformat(),
            "process_count": len(processes),
            "processes": processes[:20]  # Return top 20 processes
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get process information: {str(e)}")

@router.get("/system/disk")
async def get_disk_usage():
    """Get detailed disk usage information"""
    try:
        disk_usage = []
        
        # Get all disk partitions
        partitions = psutil.disk_partitions()
        
        for partition in partitions:
            try:
                usage = psutil.disk_usage(partition.mountpoint)
                disk_usage.append({
                    "device": partition.device,
                    "mountpoint": partition.mountpoint,
                    "fstype": partition.fstype,
                    "total": usage.total,
                    "used": usage.used,
                    "free": usage.free,
                    "percent": round((usage.used / usage.total) * 100, 1)
                })
            except PermissionError:
                continue
        
        return {
            "timestamp": datetime.now().isoformat(),
            "disk_usage": disk_usage
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get disk usage: {str(e)}")

@router.get("/system/temperature")
async def get_system_temperature():
    """Get system temperature information"""
    try:
        temperatures = {}
        
        # CPU temperature (if available)
        try:
            temps = psutil.sensors_temperatures()
            if temps:
                temperatures["sensors"] = {}
                for name, entries in temps.items():
                    temperatures["sensors"][name] = []
                    for entry in entries:
                        temperatures["sensors"][name].append({
                            "label": entry.label or "Unknown",
                            "current": entry.current,
                            "high": entry.high,
                            "critical": entry.critical
                        })
        except Exception:
            temperatures["sensors"] = "Not available"
        
        # GPU temperature (already included in GPU info)
        try:
            gpu_list = GPUtil.getGPUs()
            temperatures["gpus"] = []
            for gpu in gpu_list:
                temperatures["gpus"].append({
                    "id": gpu.id,
                    "name": gpu.name,
                    "temperature": gpu.temperature
                })
        except Exception:
            temperatures["gpus"] = "Not available"
        
        return {
            "timestamp": datetime.now().isoformat(),
            "temperatures": temperatures
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get temperature information: {str(e)}")
