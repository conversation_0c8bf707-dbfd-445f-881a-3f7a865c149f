import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { useASR } from "@/lib/asr"
import { Badge } from "@/components/ui/badge"
import { formatDistanceToNow } from "date-fns"

export function RecentPredictions() {
  const { stats } = useASR()

  if (!stats?.last_prediction) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Predictions</CardTitle>
          <CardDescription>Latest ASR model predictions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground py-4">
            No predictions available
          </div>
        </CardContent>
      </Card>
    )
  }

  const { text, confidence, segments, language, created_at } = stats.last_prediction

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Predictions</CardTitle>
        <CardDescription>Latest ASR model predictions</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Badge variant="outline">
              {language.toUpperCase()}
            </Badge>
            <span className="text-sm text-muted-foreground">
              {formatDistanceToNow(new Date(created_at), { addSuffix: true })}
            </span>
          </div>

          <div className="space-y-2">
            <div className="text-sm font-medium">Transcription</div>
            <div className="text-sm text-muted-foreground">
              {text}
            </div>
          </div>

          <div className="space-y-2">
            <div className="text-sm font-medium">Confidence</div>
            <div className="text-sm text-muted-foreground">
              {Math.round(confidence * 100)}%
            </div>
          </div>

          {segments && segments.length > 0 && (
            <div className="space-y-2">
              <div className="text-sm font-medium">Segments</div>
              <div className="space-y-2">
                {segments.map((segment: any, index: number) => (
                  <div key={index} className="text-sm text-muted-foreground">
                    <div className="flex items-center justify-between">
                      <span>{segment.text}</span>
                      <span className="text-xs">
                        {Math.round(segment.confidence * 100)}%
                      </span>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {segment.start.toFixed(2)}s - {segment.end.toFixed(2)}s
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
} 