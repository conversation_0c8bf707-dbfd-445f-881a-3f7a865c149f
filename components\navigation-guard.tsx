"use client"

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/auth-provider'
import { Card, CardContent } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Shield, Loader2 } from 'lucide-react'

interface NavigationGuardProps {
  children: React.ReactNode
  requireAuth?: boolean
  requireAdmin?: boolean
  redirectTo?: string
  fallbackComponent?: React.ReactNode
}

export function NavigationGuard({
  children,
  requireAuth = false,
  requireAdmin = false,
  redirectTo,
  fallbackComponent
}: NavigationGuardProps) {
  const { user, isLoading } = useAuth()
  const router = useRouter()
  const [hasAccess, setHasAccess] = useState(false)
  const [isChecking, setIsChecking] = useState(true)

  useEffect(() => {
    if (!isLoading) {
      setIsChecking(true)
      
      // Check authentication requirement
      if (requireAuth && !user) {
        const callbackUrl = encodeURIComponent(window.location.pathname)
        router.push(redirectTo || `/auth/signin?callbackUrl=${callbackUrl}`)
        return
      }

      // Check admin requirement
      if (requireAdmin && (!user || user.role !== 'admin')) {
        router.push(redirectTo || '/dashboard?error=admin-required')
        return
      }

      // Check if user account is disabled
      if (user && user.isDisabled) {
        router.push('/auth/signin?error=account-disabled')
        return
      }

      // All checks passed
      setHasAccess(true)
      setIsChecking(false)
    }
  }, [user, isLoading, requireAuth, requireAdmin, redirectTo, router])

  // Show loading state while checking authentication
  if (isLoading || isChecking) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex h-64 w-full items-center justify-center">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">Verifying access...</p>
          </div>
        </div>
      </div>
    )
  }

  // Show fallback component if provided and access denied
  if (!hasAccess && fallbackComponent) {
    return <>{fallbackComponent}</>
  }

  // Show default access denied message
  if (!hasAccess) {
    return (
      <div className="container mx-auto py-6">
        <Card className="max-w-md mx-auto">
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
                <Shield className="h-8 w-8 text-red-600" />
              </div>
              <h3 className="text-lg font-semibold text-red-600 mb-2">
                {requireAdmin ? 'Admin Access Required' : 'Authentication Required'}
              </h3>
              <p className="text-muted-foreground mb-4">
                {requireAdmin 
                  ? 'This page is only available to administrators.'
                  : 'You need to be logged in to access this page.'
                }
              </p>
              <Alert variant="destructive">
                <AlertDescription>
                  {requireAdmin 
                    ? 'You do not have permission to access this page. Please contact an administrator if you need access.'
                    : 'Please log in to continue.'
                  }
                </AlertDescription>
              </Alert>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Render children if access is granted
  return <>{children}</>
}

// Convenience components for common use cases
export function AdminGuard({ children, ...props }: Omit<NavigationGuardProps, 'requireAdmin'>) {
  return (
    <NavigationGuard requireAuth requireAdmin {...props}>
      {children}
    </NavigationGuard>
  )
}

export function AuthGuard({ children, ...props }: Omit<NavigationGuardProps, 'requireAuth'>) {
  return (
    <NavigationGuard requireAuth {...props}>
      {children}
    </NavigationGuard>
  )
}
