import { NextRequest, NextResponse } from 'next/server'
import { collection, getDocs, doc, updateDoc, writeBatch, query, limit, getDoc } from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { cookies } from 'next/headers'
import { auth } from '@/lib/firebase-admin'

interface MigrationResult {
  success: boolean
  totalProcessed: number
  updated: number
  errors: string[]
  details: {
    mainDocuments: number
    transcriptions: number
    metadata: number
    reviews: number
    training: number
    analytics: number
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get('session')?.value

    if (!sessionCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the session cookie
    const decodedClaims = await auth.verifySessionCookie(sessionCookie, true)
    const userId = decodedClaims.uid

    // Get user data from Firestore to check role
    const userDoc = await getDoc(doc(db, 'users', userId))
    if (!userDoc.exists()) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const userData = userDoc.data()
    if (userData.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden: Admin access required' }, { status: 403 })
    }

    const { dryRun = false, batchSize = 50 } = await request.json()
    
    console.log(`Starting timestamp migration (dryRun: ${dryRun}, batchSize: ${batchSize})`)
    
    const result: MigrationResult = {
      success: true,
      totalProcessed: 0,
      updated: 0,
      errors: [],
      details: {
        mainDocuments: 0,
        transcriptions: 0,
        metadata: 0,
        reviews: 0,
        training: 0,
        analytics: 0
      }
    }

    // Get all audio documents
    const audioCollection = collection(db, 'audio')
    const audioSnapshot = await getDocs(audioCollection)
    
    console.log(`Found ${audioSnapshot.size} audio documents`)
    result.totalProcessed = audioSnapshot.size

    // Process documents in batches
    const documents = audioSnapshot.docs
    for (let i = 0; i < documents.length; i += batchSize) {
      const batch = documents.slice(i, i + batchSize)
      
      if (!dryRun) {
        const writeBatchRef = writeBatch(db)
        
        for (const audioDoc of batch) {
          const audioId = audioDoc.id
          const audioData = audioDoc.data()
          
          try {
            // Update main document if needed
            const mainUpdates = await checkAndPrepareMainDocumentUpdates(audioData)
            if (Object.keys(mainUpdates).length > 0) {
              writeBatchRef.update(doc(db, 'audio', audioId), mainUpdates)
              result.details.mainDocuments++
            }

            // Update subcollections
            const subcollectionUpdates = await checkSubcollections(audioId)
            
            for (const update of subcollectionUpdates) {
              if (Object.keys(update.updates).length > 0) {
                writeBatchRef.update(doc(db, 'audio', audioId, update.collection, update.document), update.updates)
                result.details[update.type as keyof typeof result.details]++
              }
            }
            
          } catch (error) {
            result.errors.push(`Error processing ${audioId}: ${error}`)
          }
        }
        
        // Commit the batch
        if (result.details.mainDocuments > 0 || 
            result.details.transcriptions > 0 || 
            result.details.metadata > 0 || 
            result.details.reviews > 0 || 
            result.details.training > 0 || 
            result.details.analytics > 0) {
          await writeBatchRef.commit()
          result.updated += batch.length
        }
      } else {
        // Dry run - just check what would be updated
        for (const audioDoc of batch) {
          const audioData = audioDoc.data()
          const mainUpdates = await checkAndPrepareMainDocumentUpdates(audioData)
          if (Object.keys(mainUpdates).length > 0) {
            result.details.mainDocuments++
          }
          
          const subcollectionUpdates = await checkSubcollections(audioDoc.id)
          for (const update of subcollectionUpdates) {
            if (Object.keys(update.updates).length > 0) {
              result.details[update.type as keyof typeof result.details]++
            }
          }
        }
        result.updated = result.totalProcessed // In dry run, assume all would be updated
      }
      
      console.log(`Processed batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(documents.length / batchSize)}`)
    }

    return NextResponse.json({
      success: true,
      message: dryRun ? 'Dry run completed' : 'Migration completed successfully',
      result
    })

  } catch (error) {
    console.error('Migration error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Migration failed', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}

async function checkAndPrepareMainDocumentUpdates(audioData: any): Promise<any> {
  const updates: any = {}
  const currentTime = new Date().toISOString()

  // Check created_at
  if (needsTimestampUpdate(audioData.created_at)) {
    updates.created_at = audioData.created_at ? convertToISO(audioData.created_at) : currentTime
  }

  // Check updated_at
  if (needsTimestampUpdate(audioData.updated_at)) {
    updates.updated_at = audioData.updated_at ? convertToISO(audioData.updated_at) : currentTime
  }

  return updates
}

async function checkSubcollections(audioId: string): Promise<Array<{
  collection: string
  document: string
  type: string
  updates: any
}>> {
  const results = []
  const currentTime = new Date().toISOString()

  const subcollections = [
    { collection: 'transcriptions', document: 'primary', type: 'transcriptions' },
    { collection: 'metadata', document: 'details', type: 'metadata' },
    { collection: 'review', document: 'status', type: 'reviews' },
    { collection: 'training', document: 'status', type: 'training' },
    { collection: 'analytics', document: 'metrics', type: 'analytics' }
  ]

  for (const sub of subcollections) {
    try {
      const subDoc = await getDocs(query(collection(db, 'audio', audioId, sub.collection), limit(1)))
      if (!subDoc.empty) {
        const data = subDoc.docs[0].data()
        const updates: any = {}

        if (needsTimestampUpdate(data.created_at)) {
          updates.created_at = data.created_at ? convertToISO(data.created_at) : currentTime
        }

        if (needsTimestampUpdate(data.updated_at)) {
          updates.updated_at = data.updated_at ? convertToISO(data.updated_at) : currentTime
        }

        if (data.reviewed_at && needsTimestampUpdate(data.reviewed_at)) {
          updates.reviewed_at = convertToISO(data.reviewed_at)
        }

        results.push({
          collection: sub.collection,
          document: sub.document,
          type: sub.type,
          updates
        })
      }
    } catch (error) {
      // Subcollection doesn't exist, skip
    }
  }

  return results
}

function needsTimestampUpdate(timestamp: any): boolean {
  if (!timestamp) return true
  
  // Check if it's already in ISO format
  if (typeof timestamp === 'string' && timestamp.includes('T') && timestamp.includes('Z')) {
    return false
  }
  
  // Check if it's a Firestore timestamp
  if (timestamp && typeof timestamp === 'object' && timestamp.seconds) {
    return true
  }
  
  // Check if it's in other formats
  return true
}

function convertToISO(timestamp: any): string {
  if (!timestamp) return new Date().toISOString()
  
  // If it's already ISO, return as is
  if (typeof timestamp === 'string' && timestamp.includes('T') && timestamp.includes('Z')) {
    return timestamp
  }
  
  // If it's a Firestore timestamp
  if (timestamp && typeof timestamp === 'object' && timestamp.seconds) {
    return new Date(timestamp.seconds * 1000).toISOString()
  }
  
  // Try to parse as date
  try {
    return new Date(timestamp).toISOString()
  } catch {
    return new Date().toISOString()
  }
}
