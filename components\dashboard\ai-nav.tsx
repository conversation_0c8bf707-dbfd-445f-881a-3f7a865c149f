"use client"

import { Mic, Volume2, Activity } from "lucide-react"
import { DashboardNav } from "./nav"

const navItems = [
  {
    title: "ASR Training",
    href: "/dashboard/ai/asr",
    iconType: "mic",
  },
  {
    title: "TTS Training",
    href: "/dashboard/ai/tts",
    iconType: "volume",
  },
  {
    title: "Backend Logs",
    href: "/dashboard/ai/logs",
    iconType: "activity",
  },
]

export function AINav() {
  return <DashboardNav items={navItems} />
} 