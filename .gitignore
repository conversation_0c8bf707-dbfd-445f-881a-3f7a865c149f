# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Python virtual environment
venv/
env/
.env
.venv/
ENV/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Node
node_modules/
.next/
out/
build/
.DS_Store
*.pem
.env*.local

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Testing
coverage/
.coverage
htmlcov/

# Misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
backend/.env
!backend/.env.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# =============================================================================
# AI/ML MODELS & DATA (Added for Masalit AI Platform)
# =============================================================================
# Model files and directories (created automatically by the application)
ai_models/

# Training data and audio files
training_data/
datasets/
*.wav
*.mp3
*.m4a
*.ogg
*.webm
*.flac

# Model weights and checkpoints
*.bin
*.safetensors
*.h5
*.ckpt
*.pth
*.pt

# Hugging Face cache
.cache/
transformers_cache/

# Service account keys and secrets
model-uploader.json
serviceAccountKey.json

# Backend virtual environment
backend/venv/

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Archive files
*.zip
*.tar.gz
*.rar

# Database files
*.db
*.sqlite
*.sqlite3

# Test files and directories (keep for development)
# test/
# tests/
# *test*.py
# *test*.js
# *test*.ts
# *.test.*

# SSL certificates and keys
ssl/
*.key
*.crt
*.csr

# Process IDs
*.pid
backend.pid
frontend.pid

# Documentation drafts
docs/drafts/
*.draft.md

# Backup files
*.bak
*.backup
*.old
