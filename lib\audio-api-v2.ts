// New Audio API Library for Hierarchical Firebase Structure
// This library provides functions to interact with the new hierarchical audio structure

export interface HierarchicalAudioData {
  // Main document
  id: string
  title: string
  audio_url: string
  duration: number
  format: string
  created_at: string
  user_id: string
  source: string

  // Subcollections (loaded on demand)
  transcriptions?: {
    primary?: {
      content: string
      language: string
      transcription_source: string
      created_at: string
      type: string
      speaker_count: number
    }
  }
  metadata?: {
    details?: {
      gender: string
      language: string
      recording_context?: string
    }
  }
  review?: {
    status?: {
      action: 'pending' | 'approved' | 'rejected'
      reviewed_by?: string
      reviewed_at?: string
      feedback?: string
      is_flagged: boolean
    }
  }
  training?: {
    status?: {
      trained_asr: boolean
      tts_trained: boolean
      training_sessions: string[]
      last_trained_at?: string
    }
  }
}

export interface HierarchicalUserData {
  // Main document
  email: string
  name: string
  username: string
  role: string
  created_at: string

  // Subcollections (loaded on demand)
  profile?: {
    details?: {
      avatar_url?: string
      bio?: string
      language_preferences: string[]
      email_verified: boolean
    }
  }
  statistics?: {
    summary?: {
      contribution_count: number
      audio_uploads: number
      transcriptions_created: number
      last_activity: string
    }
  }
  preferences?: {
    settings?: {
      theme: 'light' | 'dark'
      language: string
      notifications: object
    }
  }
}

// Audio API Functions
export async function createAudioWithTranscription(audioData: {
  title: string
  duration: number
  format: string
  user_id: string
  source: string
  audio_url: string
  transcription: {
    content: string
    language: string
    transcription_source: string
    type: string
  }
  metadata: {
    gender: string
    language: string
    recording_context?: string
  }
}): Promise<{ audio_id: string }> {
  try {
    const response = await fetch('/api/audio/create-with-transcription', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(audioData),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to create audio record')
    }

    const result = await response.json()
    return result
  } catch (error) {
    console.error('Error creating audio with transcription:', error)
    throw error
  }
}

export async function getAudioWithSubcollections(
  audioId: string, 
  include: string[] = ['transcriptions', 'metadata', 'review', 'training']
): Promise<HierarchicalAudioData | null> {
  try {
    const includeParam = include.join(',')
    const response = await fetch(`/api/audio/${audioId}?include=${includeParam}`)
    
    if (!response.ok) {
      if (response.status === 404) return null
      throw new Error('Failed to fetch audio data')
    }
    
    const result = await response.json()
    return result.data
  } catch (error) {
    console.error('Error fetching audio data:', error)
    throw error
  }
}

export async function listUserAudio(
  userId: string,
  options: {
    trained_asr?: boolean
    tts_trained?: boolean
    action?: string
    limit?: number
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  } = {}
): Promise<HierarchicalAudioData[]> {
  try {
    const params = new URLSearchParams({
      user_id: userId,
      limit: (options.limit || 50).toString(),
      sortBy: options.sortBy || 'created_at',
      sortOrder: options.sortOrder || 'desc'
    })

    if (options.trained_asr !== undefined) {
      params.append('trained_asr', options.trained_asr.toString())
    }
    if (options.tts_trained !== undefined) {
      params.append('tts_trained', options.tts_trained.toString())
    }
    if (options.action) {
      params.append('action', options.action)
    }

    console.log('Fetching user audio with params:', params.toString())

    const response = await fetch(`/api/audio/?${params}`)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('API Error Response:', errorText)
      throw new Error(`Failed to fetch user audio: ${response.status} ${response.statusText}`)
    }

    const result = await response.json()
    return result.audio_records
  } catch (error) {
    console.error('Error fetching user audio:', error)
    throw error
  }
}

export async function listAllAudio(
  options: {
    trained_asr?: boolean
    tts_trained?: boolean
    action?: string
    limit?: number
    page?: number
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  } = {}
): Promise<{
  audio_records: HierarchicalAudioData[]
  total: number
  page: number
  limit: number
  hasMore: boolean
}> {
  try {
    const params = new URLSearchParams({
      limit: (options.limit || 50).toString(),
      page: (options.page || 1).toString(),
      sortBy: options.sortBy || 'created_at',
      sortOrder: options.sortOrder || 'desc'
    })

    if (options.trained_asr !== undefined) {
      params.append('trained_asr', options.trained_asr.toString())
    }
    if (options.tts_trained !== undefined) {
      params.append('tts_trained', options.tts_trained.toString())
    }
    if (options.action) {
      params.append('action', options.action)
    }

    console.log('Fetching audio with params:', params.toString())

    const response = await fetch(`/api/audio/all?${params}`)

    if (!response.ok) {
      throw new Error('Failed to fetch all audio')
    }

    const result = await response.json()
    return {
      audio_records: result.audio_records,
      total: result.total,
      page: result.page,
      limit: result.limit,
      hasMore: result.hasMore
    }
  } catch (error) {
    console.error('Error fetching all audio:', error)
    throw error
  }
}

// Legacy function for backward compatibility
export async function listAllAudioLegacy(
  options: {
    trained_asr?: boolean
    tts_trained?: boolean
    action?: string
    limit?: number
  } = {}
): Promise<HierarchicalAudioData[]> {
  const result = await listAllAudio(options)
  return result.audio_records
}

export async function updateTrainingStatus(
  audioId: string,
  updates: {
    trained_asr?: boolean
    tts_trained?: boolean
    training_session_id?: string
  }
): Promise<void> {
  try {
    const response = await fetch(`/api/audio/${audioId}/training/status`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    })
    
    if (!response.ok) {
      throw new Error('Failed to update training status')
    }
  } catch (error) {
    console.error('Error updating training status:', error)
    throw error
  }
}

export async function deleteAudioRecord(audioId: string): Promise<void> {
  try {
    const response = await fetch(`/api/audio/${audioId}`, {
      method: 'DELETE',
    })
    
    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to delete audio record')
    }
  } catch (error) {
    console.error('Error deleting audio record:', error)
    throw error
  }
}

export async function updateAudioRecord(
  audioId: string,
  updates: {
    title?: string
    transcription_content?: string
    metadata?: {
      gender?: string
      language?: string
      recording_context?: string
    }
  }
): Promise<void> {
  try {
    const response = await fetch(`/api/audio/${audioId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to update audio record')
    }
  } catch (error) {
    console.error('Error updating audio record:', error)
    throw error
  }
}

export async function updateReviewStatus(
  audioId: string,
  updates: {
    action: 'pending' | 'approved' | 'rejected'
    reviewed_by?: string
    reviewed_at?: string
    feedback?: string
    is_flagged?: boolean
  }
): Promise<void> {
  try {
    const response = await fetch(`/api/audio/${audioId}/review/status`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to update review status')
    }
  } catch (error) {
    console.error('Error updating review status:', error)
    throw error
  }
}

export async function bulkUpdateReviewStatus(
  audioIds: string[],
  updates: {
    action: 'pending' | 'approved' | 'rejected'
    reviewed_by?: string
    reviewed_at?: string
    feedback?: string
    is_flagged?: boolean
  }
): Promise<void> {
  try {
    const promises = audioIds.map(audioId => updateReviewStatus(audioId, updates))
    await Promise.all(promises)
  } catch (error) {
    console.error('Error bulk updating review status:', error)
    throw error
  }
}

// User API Functions
export async function getUserWithSubcollections(
  userId: string,
  include: string[] = ['profile', 'statistics', 'preferences']
): Promise<HierarchicalUserData | null> {
  try {
    const includeParam = include.join(',')
    const response = await fetch(`/api/users/${userId}?include=${includeParam}`)
    
    if (!response.ok) {
      if (response.status === 404) return null
      throw new Error('Failed to fetch user data')
    }
    
    const result = await response.json()
    return result.data
  } catch (error) {
    console.error('Error fetching user data:', error)
    throw error
  }
}

export async function updateUserStatistics(
  userId: string,
  updates: {
    contribution_count?: number
    audio_uploads?: number
    transcriptions_created?: number
  }
): Promise<void> {
  try {
    const response = await fetch(`/api/users/${userId}/statistics`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    })
    
    if (!response.ok) {
      throw new Error('Failed to update user statistics')
    }
  } catch (error) {
    console.error('Error updating user statistics:', error)
    throw error
  }
}

export async function incrementUserStat(
  userId: string,
  statName: string,
  incrementBy: number = 1
): Promise<void> {
  try {
    const response = await fetch(`/api/users/${userId}/statistics/increment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        stat_name: statName,
        increment_by: incrementBy
      }),
    })

    if (!response.ok) {
      throw new Error('Failed to increment user statistic')
    }
  } catch (error) {
    console.error('Error incrementing user statistic:', error)
    throw error
  }
}

// Utility function to get usernames from user IDs
export async function getUsernames(userIds: string[]): Promise<Record<string, string>> {
  try {
    const response = await fetch('/api/users/usernames', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userIds }),
    })

    if (!response.ok) {
      throw new Error('Failed to fetch usernames')
    }

    const result = await response.json()
    return result.usernames || {}
  } catch (error) {
    console.error('Error fetching usernames:', error)
    return {}
  }
}

// Bulk upload function
export async function bulkUploadAudio(
  files: File[],
  metadata: Array<{
    filename: string
    title: string
    transcription: string
    speaker: string
    duration?: number
  }>,
  userId: string
): Promise<{ success: boolean; message?: string; results?: any[]; errors?: string[] }> {
  try {
    const formData = new FormData()

    // Add user ID
    formData.append('user_id', userId)

    // Add files
    files.forEach((file, index) => {
      formData.append(`files`, file)
    })

    // Add metadata
    formData.append('metadata', JSON.stringify(metadata))

    const response = await fetch('/api/audio/bulk-upload', {
      method: 'POST',
      body: formData,
    })
    
    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to upload recordings')
    }
    
    const result = await response.json()
    return result
  } catch (error) {
    console.error('Error bulk uploading audio:', error)
    throw error
  }
}
