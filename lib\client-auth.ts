import { getAuth, onAuthStateChanged, User } from 'firebase/auth'
import { useEffect, useState } from 'react'
import { auth, db } from '@/lib/firebase'
import { doc, getDoc } from 'firebase/firestore'

interface ExtendedUser extends User {
  role?: string
  isDisabled?: boolean
  email_verified?: boolean
}

export function useAuth() {
  const [user, setUser] = useState<ExtendedUser | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        try {
          // Fetch user data from Firestore to get role and other info
          const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid))
          const userData = userDoc.data()

          // Create extended user object with Firestore data
          const extendedUser: ExtendedUser = {
            ...firebaseUser,
            role: userData?.role || 'user',
            isDisabled: userData?.isDisabled || false,
            email_verified: userData?.email_verified || false
          }

          // Check if user is disabled
          if (userData?.isDisabled) {
            console.warn('User account is disabled')
            await auth.signOut()
            setUser(null)
            setLoading(false)
            return
          }

          setUser(extendedUser)
        } catch (error) {
          console.error('Error fetching user data:', error)
          // Fallback to basic Firebase user with default role
          const extendedUser: ExtendedUser = {
            ...firebaseUser,
            role: 'user',
            isDisabled: false,
            email_verified: false
          }
          setUser(extendedUser)
        }
      } else {
        setUser(null)
      }
      setLoading(false)
    })

    return () => unsubscribe()
  }, [])

  // Function to refresh user data (useful after role changes)
  const refreshUser = async () => {
    if (auth.currentUser) {
      try {
        const userDoc = await getDoc(doc(db, 'users', auth.currentUser.uid))
        const userData = userDoc.data()

        const extendedUser: ExtendedUser = {
          ...auth.currentUser,
          role: userData?.role || 'user',
          isDisabled: userData?.isDisabled || false,
          email_verified: userData?.email_verified || false
        }

        setUser(extendedUser)
      } catch (error) {
        console.error('Error refreshing user data:', error)
      }
    }
  }

  return { user, loading, refreshUser }
}