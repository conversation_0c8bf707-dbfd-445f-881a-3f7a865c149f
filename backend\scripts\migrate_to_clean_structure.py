#!/usr/bin/env python3
"""
Firebase Collection Structure Migration Script

Migrates from the current scattered structure to a clean, organized structure:

OLD STRUCTURE:
- training_settings (collection)
- validation_settings (collection) 
- validation_results (collection)
- training_status (collection)
- training_history (collection)
- training_metrics (collection)
- training_schedules (collection)

NEW STRUCTURE:
- settings/{type} (documents)
- validation/{validation_id}/{subcollection} 
- training/{session_id}/{subcollection}
"""

import sys
import os
from datetime import datetime
from typing import Dict, Any, List

# Add backend to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.firebase import firebase_service
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FirebaseStructureMigrator:
    def __init__(self):
        self.db = None
        self.migration_log = []
        
    def initialize(self):
        """Initialize Firebase connection"""
        try:
            firebase_service.initialize()
            self.db = firebase_service.db
            logger.info("✅ Firebase initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Firebase: {e}")
            raise
    
    def log_migration(self, action: str, details: str):
        """Log migration actions"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "action": action,
            "details": details
        }
        self.migration_log.append(log_entry)
        logger.info(f"📝 {action}: {details}")
    
    def migrate_settings(self):
        """Migrate scattered settings to unified settings collection"""
        logger.info("🔄 Starting settings migration...")
        
        # 1. Migrate training_settings
        try:
            training_docs = self.db.collection('training_settings').get()
            for doc in training_docs:
                doc_id = doc.id  # 'asr', 'tts', etc.
                data = doc.to_dict()
                
                # Save to new structure
                new_doc_ref = self.db.collection('settings').document(f'{doc_id}_training')
                new_doc_ref.set({
                    **data,
                    'migrated_at': datetime.now().isoformat(),
                    'migrated_from': f'training_settings/{doc_id}'
                })
                
                self.log_migration("MIGRATE_SETTINGS", f"training_settings/{doc_id} -> settings/{doc_id}_training")
                
        except Exception as e:
            logger.error(f"❌ Error migrating training_settings: {e}")
        
        # 2. Migrate validation_settings  
        try:
            validation_docs = self.db.collection('validation_settings').get()
            for doc in validation_docs:
                doc_id = doc.id
                data = doc.to_dict()
                
                # Save to new structure
                new_doc_ref = self.db.collection('settings').document(f'{doc_id}_validation')
                new_doc_ref.set({
                    **data,
                    'migrated_at': datetime.now().isoformat(),
                    'migrated_from': f'validation_settings/{doc_id}'
                })
                
                self.log_migration("MIGRATE_SETTINGS", f"validation_settings/{doc_id} -> settings/{doc_id}_validation")
                
        except Exception as e:
            logger.error(f"❌ Error migrating validation_settings: {e}")
        
        # 3. Migrate training_schedules
        try:
            schedule_docs = self.db.collection('training_schedules').get()
            for doc in schedule_docs:
                doc_id = doc.id
                data = doc.to_dict()
                
                # Save to new structure
                new_doc_ref = self.db.collection('settings').document(f'{doc_id}_schedule')
                new_doc_ref.set({
                    **data,
                    'migrated_at': datetime.now().isoformat(),
                    'migrated_from': f'training_schedules/{doc_id}'
                })
                
                self.log_migration("MIGRATE_SETTINGS", f"training_schedules/{doc_id} -> settings/{doc_id}_schedule")
                
        except Exception as e:
            logger.error(f"❌ Error migrating training_schedules: {e}")
    
    def migrate_validation_data(self):
        """Migrate validation data to new hierarchical structure"""
        logger.info("🔄 Starting validation data migration...")
        
        try:
            validation_docs = self.db.collection('validation_results').get()
            
            for doc in validation_docs:
                validation_id = doc.id
                data = doc.to_dict()
                
                # Extract different parts of validation data
                config_data = data.get('config', {})
                metrics_data = data.get('metrics', {})
                summary_data = data.get('summary', {})
                error_analysis = data.get('error_analysis', [])
                
                # Create new hierarchical structure
                validation_ref = self.db.collection('validation').document(validation_id)
                
                # Main validation document
                main_data = {
                    'validation_id': validation_id,
                    'model_id': data.get('model_id'),
                    'model_type': data.get('model_type'),
                    'status': data.get('status'),
                    'started_at': data.get('started_at'),
                    'completed_at': data.get('completed_at'),
                    'duration_seconds': data.get('duration_seconds'),
                    'migrated_at': datetime.now().isoformat(),
                    'migrated_from': f'validation_results/{validation_id}'
                }
                validation_ref.set(main_data)
                
                # Config subcollection
                if config_data:
                    validation_ref.collection('config').document('settings').set(config_data)
                
                # Results subcollection
                results_data = {
                    'metrics': metrics_data,
                    'summary': summary_data,
                    'created_at': datetime.now().isoformat()
                }
                validation_ref.collection('results').document('final').set(results_data)
                
                # Error analysis subcollection
                if error_analysis:
                    for i, error in enumerate(error_analysis):
                        validation_ref.collection('error_analysis').document(f'sample_{i}').set(error)
                
                self.log_migration("MIGRATE_VALIDATION", f"validation_results/{validation_id} -> validation/{validation_id}/*")
                
        except Exception as e:
            logger.error(f"❌ Error migrating validation data: {e}")
    
    def migrate_training_data(self):
        """Migrate training data to new hierarchical structure"""
        logger.info("🔄 Starting training data migration...")
        
        # 1. Migrate training_history
        try:
            history_docs = self.db.collection('training_history').get()
            
            for doc in history_docs:
                session_id = doc.id
                data = doc.to_dict()
                
                # Create new hierarchical structure
                training_ref = self.db.collection('training').document(session_id)
                
                # Main training document
                main_data = {
                    'session_id': session_id,
                    'model_type': data.get('model_type'),
                    'model_version': data.get('model_version'),
                    'status': data.get('status'),
                    'start_time': data.get('start_time'),
                    'end_time': data.get('end_time'),
                    'samples_trained': data.get('samples_trained'),
                    'migrated_at': datetime.now().isoformat(),
                    'migrated_from': f'training_history/{session_id}'
                }
                training_ref.set(main_data)
                
                # Config subcollection
                training_settings = data.get('training_settings', {})
                if training_settings:
                    training_ref.collection('config').document('settings').set(training_settings)
                
                # Results subcollection
                results_data = {
                    'model_path': data.get('model_path'),
                    'gcs_url': data.get('gcs_url'),
                    'upload_success': data.get('upload_success'),
                    'epochs': data.get('epochs'),
                    'accuracy': data.get('accuracy', 0.0),
                    'confidence': data.get('confidence', 0.0),
                    'wer': data.get('wer', 0.0),
                    'cer': data.get('cer', 0.0),
                    'ser': data.get('ser', 0.0),
                    'created_at': datetime.now().isoformat()
                }
                training_ref.collection('results').document('final').set(results_data)
                
                self.log_migration("MIGRATE_TRAINING", f"training_history/{session_id} -> training/{session_id}/*")
                
        except Exception as e:
            logger.error(f"❌ Error migrating training_history: {e}")
        
        # 2. Migrate training_metrics
        try:
            metrics_docs = self.db.collection('training_metrics').get()
            
            for doc in metrics_docs:
                metric_id = doc.id
                data = doc.to_dict()
                
                # Try to associate with a training session (this might need manual mapping)
                # For now, create a general metrics document
                session_id = data.get('session_id', f'unknown_{metric_id}')
                
                training_ref = self.db.collection('training').document(session_id)
                training_ref.collection('metrics').document(metric_id).set({
                    **data,
                    'migrated_at': datetime.now().isoformat(),
                    'migrated_from': f'training_metrics/{metric_id}'
                })
                
                self.log_migration("MIGRATE_TRAINING_METRICS", f"training_metrics/{metric_id} -> training/{session_id}/metrics/{metric_id}")
                
        except Exception as e:
            logger.error(f"❌ Error migrating training_metrics: {e}")

    def migrate_asr_specific_data(self):
        """Migrate ASR-specific data and create better organization"""
        logger.info("🔄 Starting ASR-specific data migration...")

        try:
            # 1. Migrate training_status (ASR document) to new structure
            try:
                status_doc = self.db.collection('training_status').document('asr').get()
                if status_doc.exists:
                    status_data = status_doc.to_dict()

                    # Create a current training session if one is active
                    if status_data.get('status') in ['training', 'preparing_data', 'uploading']:
                        session_id = f"asr_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

                        # Create training session with current status
                        training_ref = self.db.collection('training').document(session_id)
                        training_ref.set({
                            'session_id': session_id,
                            'model_type': 'asr',
                            'status': status_data.get('status', 'unknown'),
                            'created_at': datetime.now().isoformat(),
                            'migrated_from': 'training_status/asr'
                        })

                        # Save current progress
                        progress_ref = training_ref.collection('progress').document('current')
                        progress_ref.set({
                            **status_data,
                            'migrated_at': datetime.now().isoformat()
                        })

                        self.log_migration("MIGRATE_ASR_STATUS", f"training_status/asr -> training/{session_id}/progress/current")

            except Exception as e:
                logger.error(f"❌ Error migrating ASR training status: {e}")

            # 2. Create ASR-specific settings organization
            try:
                # Ensure ASR settings are properly organized
                asr_settings_types = ['asr_training', 'asr_validation', 'asr_schedule']

                for setting_type in asr_settings_types:
                    settings_ref = self.db.collection('settings').document(setting_type)
                    settings_doc = settings_ref.get()

                    if not settings_doc.exists:
                        # Create default ASR settings if they don't exist
                        default_settings = self._get_default_asr_settings(setting_type)
                        settings_ref.set({
                            **default_settings,
                            'created_at': datetime.now().isoformat(),
                            'created_by': 'migration_script'
                        })

                        self.log_migration("CREATE_ASR_SETTINGS", f"Created default {setting_type} settings")

            except Exception as e:
                logger.error(f"❌ Error creating ASR settings: {e}")

            # 3. Create ASR analytics/stats structure
            try:
                # Create ASR analytics document for better dashboard data
                analytics_ref = self.db.collection('analytics').document('asr')
                analytics_ref.set({
                    'last_updated': datetime.now().isoformat(),
                    'total_training_sessions': 0,
                    'total_validation_runs': 0,
                    'active_models': 0,
                    'created_by': 'migration_script'
                })

                self.log_migration("CREATE_ASR_ANALYTICS", "Created ASR analytics structure")

            except Exception as e:
                logger.error(f"❌ Error creating ASR analytics: {e}")

        except Exception as e:
            logger.error(f"❌ Error in ASR-specific migration: {e}")

    def _get_default_asr_settings(self, setting_type: str):
        """Get default ASR settings"""
        defaults = {
            'asr_training': {
                'epochs': 5,
                'learning_rate': 1e-5,
                'batch_size': 4,
                'validation_split': 0.2,
                'model_name': 'small',
                'early_stopping_patience': 3,
                'use_augmentation': False,
                'eval_steps': 100,
                'training_timeout': 7200
            },
            'asr_validation': {
                'validation_type': 'quick',
                'max_samples': 20,
                'min_confidence': 0.0,
                'model': 'asr_current_model'
            },
            'asr_schedule': {
                'enabled': False,
                'interval': 'weekly',
                'time': '00:00'
            }
        }
        return defaults.get(setting_type, {})

    def create_backup(self):
        """Create backup of current structure before migration"""
        logger.info("💾 Creating backup of current structure...")
        
        backup_data = {
            'backup_created_at': datetime.now().isoformat(),
            'collections_backed_up': [
                'training_settings',
                'validation_settings',
                'validation_results',
                'training_status',
                'training_history',
                'training_metrics',
                'training_schedules'
            ],
            'asr_specific_changes': [
                'Reorganizing ASR training/validation data',
                'Moving ASR settings to unified structure',
                'Creating hierarchical ASR session tracking'
            ],
            'collections_not_touched': [
                'audio',
                'transcription',
                'models',
                'users'
            ]
        }
        
        self.db.collection('_migration_backup').document('structure_backup').set(backup_data)
        self.log_migration("BACKUP", "Created backup record")
    
    def save_migration_log(self):
        """Save migration log to Firebase"""
        try:
            log_doc = {
                'migration_completed_at': datetime.now().isoformat(),
                'total_actions': len(self.migration_log),
                'actions': self.migration_log
            }
            
            self.db.collection('_migration_logs').document(f'migration_{datetime.now().strftime("%Y%m%d_%H%M%S")}').set(log_doc)
            logger.info(f"✅ Migration log saved with {len(self.migration_log)} actions")
            
        except Exception as e:
            logger.error(f"❌ Error saving migration log: {e}")
    
    def run_migration(self, create_backup: bool = True):
        """Run the complete migration"""
        try:
            self.initialize()
            
            if create_backup:
                self.create_backup()
            
            logger.info("🚀 Starting Firebase structure migration...")
            
            # Run migrations
            self.migrate_settings()
            self.migrate_validation_data()
            self.migrate_training_data()
            self.migrate_asr_specific_data()
            
            # Save log
            self.save_migration_log()
            
            logger.info("✅ Migration completed successfully!")
            logger.info(f"📊 Total actions performed: {len(self.migration_log)}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Migration failed: {e}")
            return False

def main():
    """Main migration function"""
    migrator = FirebaseStructureMigrator()
    
    print("🔄 Firebase Structure Migration")
    print("=" * 50)
    print("This will migrate to a cleaner, more organized structure.")
    print("A backup will be created before migration.")
    print()
    
    confirm = input("Do you want to proceed? (y/N): ").lower().strip()
    
    if confirm == 'y':
        success = migrator.run_migration()
        if success:
            print("\n✅ Migration completed successfully!")
            print("You can now update your code to use the new structure.")
        else:
            print("\n❌ Migration failed. Check logs for details.")
    else:
        print("Migration cancelled.")

if __name__ == "__main__":
    main()
