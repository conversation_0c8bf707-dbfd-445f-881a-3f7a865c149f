"""
Audio API endpoints v2 - Using hierarchical Firestore collections
"""

import logging
from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
from datetime import datetime

from backend.services.firebase_clean import clean_firebase_service
from backend.services.gcs import gcs_service

logger = logging.getLogger(__name__)

router = APIRouter()

class AudioUploadResponse(BaseModel):
    message: str
    audio_id: str
    audio_url: str
    status: str

class AudioMetadata(BaseModel):
    title: Optional[str] = None
    dialect: Optional[str] = None
    gender: Optional[str] = None
    age_group: Optional[str] = None
    recording_context: Optional[str] = None
    quality_notes: Optional[str] = None

class TranscriptionData(BaseModel):
    content: str
    language: str = "masalit"
    confidence: Optional[float] = None
    source: str = "manual"

class ReviewData(BaseModel):
    action: str  # "approved", "rejected", "pending"
    reviewer_id: str
    notes: Optional[str] = None

# ==================== UPLOAD ENDPOINTS ====================

@router.post("/upload", response_model=AudioUploadResponse)
async def upload_audio(
    file: UploadFile = File(...),
    user_id: str = Form(...),
    title: Optional[str] = Form(None),
    dialect: Optional[str] = Form(None),
    gender: Optional[str] = Form(None),
    age_group: Optional[str] = Form(None),
    recording_context: Optional[str] = Form(None),
    quality_notes: Optional[str] = Form(None)
):
    """Upload audio file with metadata using hierarchical structure"""
    try:
        # Validate file type
        if not file.content_type or not file.content_type.startswith('audio/'):
            raise HTTPException(status_code=400, detail="File must be an audio file")
        
        # Initialize services
        clean_firebase_service.initialize()
        gcs_service.initialize()
        
        # Generate audio ID
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        audio_id = f"audio_{timestamp}_{user_id}"
        
        # Upload to GCS
        file_content = await file.read()
        gcs_url = gcs_service.upload_audio(file_content, audio_id, file.content_type)
        
        if not gcs_url:
            raise HTTPException(status_code=500, detail="Failed to upload audio to storage")
        
        # Create audio document in hierarchical structure
        audio_data = {
            'audio_id': audio_id,
            'user_id': user_id,
            'filename': file.filename,
            'content_type': file.content_type,
            'file_size': len(file_content),
            'audio_url': gcs_url,
            'upload_timestamp': datetime.now().isoformat(),
            'status': 'uploaded'
        }
        
        # Save main audio document
        audio_ref = clean_firebase_service.db.collection('audio').document(audio_id)
        audio_ref.set(audio_data)
        
        # Save metadata if provided
        if any([title, dialect, gender, age_group, recording_context, quality_notes]):
            metadata = {
                'title': title,
                'dialect': dialect,
                'gender': gender,
                'age_group': age_group,
                'recording_context': recording_context,
                'quality_notes': quality_notes,
                'created_at': datetime.now().isoformat()
            }
            # Remove None values
            metadata = {k: v for k, v in metadata.items() if v is not None}
            
            metadata_ref = audio_ref.collection('metadata').document('details')
            metadata_ref.set(metadata)
        
        # Initialize review status
        review_ref = audio_ref.collection('review').document('status')
        review_ref.set({
            'action': 'pending',
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        })
        
        # Initialize training status
        training_ref = audio_ref.collection('training').document('status')
        training_ref.set({
            'trained_asr': False,
            'tts_trained': False,
            'created_at': datetime.now().isoformat()
        })
        
        logger.info(f"Audio uploaded successfully: {audio_id}")
        
        return AudioUploadResponse(
            message="Audio uploaded successfully",
            audio_id=audio_id,
            audio_url=gcs_url,
            status="uploaded"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading audio: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ==================== TRANSCRIPTION ENDPOINTS ====================

@router.post("/{audio_id}/transcription")
async def add_transcription(audio_id: str, transcription: TranscriptionData):
    """Add transcription to audio using hierarchical structure"""
    try:
        clean_firebase_service.initialize()
        
        # Check if audio exists
        audio_ref = clean_firebase_service.db.collection('audio').document(audio_id)
        audio_doc = audio_ref.get()
        
        if not audio_doc.exists:
            raise HTTPException(status_code=404, detail="Audio not found")
        
        # Add transcription
        transcription_data = {
            **transcription.model_dump(),
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
        
        transcription_ref = audio_ref.collection('transcriptions').document('primary')
        transcription_ref.set(transcription_data)
        
        logger.info(f"Transcription added for audio: {audio_id}")
        
        return {"message": "Transcription added successfully", "audio_id": audio_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding transcription: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{audio_id}/transcription")
async def get_transcription(audio_id: str):
    """Get transcription for audio"""
    try:
        clean_firebase_service.initialize()
        
        # Get transcription
        transcription_ref = (clean_firebase_service.db.collection('audio')
                           .document(audio_id)
                           .collection('transcriptions')
                           .document('primary'))
        
        transcription_doc = transcription_ref.get()
        
        if not transcription_doc.exists:
            raise HTTPException(status_code=404, detail="Transcription not found")
        
        return transcription_doc.to_dict()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting transcription: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ==================== REVIEW ENDPOINTS ====================

@router.post("/{audio_id}/review")
async def update_review_status(audio_id: str, review: ReviewData):
    """Update review status for audio"""
    try:
        clean_firebase_service.initialize()
        
        # Check if audio exists
        audio_ref = clean_firebase_service.db.collection('audio').document(audio_id)
        audio_doc = audio_ref.get()
        
        if not audio_doc.exists:
            raise HTTPException(status_code=404, detail="Audio not found")
        
        # Update review status
        review_data = {
            **review.model_dump(),
            'updated_at': datetime.now().isoformat()
        }
        
        review_ref = audio_ref.collection('review').document('status')
        review_ref.set(review_data, merge=True)
        
        # Add to review history
        history_ref = audio_ref.collection('review').document('history').collection('entries').document()
        history_ref.set({
            **review_data,
            'timestamp': datetime.now().isoformat()
        })
        
        logger.info(f"Review status updated for audio: {audio_id} - {review.action}")
        
        return {"message": "Review status updated successfully", "audio_id": audio_id, "action": review.action}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating review status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{audio_id}/review")
async def get_review_status(audio_id: str):
    """Get review status for audio"""
    try:
        clean_firebase_service.initialize()
        
        # Get review status
        review_ref = (clean_firebase_service.db.collection('audio')
                     .document(audio_id)
                     .collection('review')
                     .document('status'))
        
        review_doc = review_ref.get()
        
        if not review_doc.exists:
            return {"action": "pending", "message": "No review status found"}
        
        return review_doc.to_dict()
        
    except Exception as e:
        logger.error(f"Error getting review status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ==================== LISTING ENDPOINTS ====================

@router.get("/list")
async def list_audio(
    user_id: Optional[str] = None,
    status: Optional[str] = None,
    review_action: Optional[str] = None,
    limit: int = 50,
    offset: int = 0
):
    """List audio files with filtering"""
    try:
        clean_firebase_service.initialize()
        
        # Build query
        query = clean_firebase_service.db.collection('audio')
        
        if user_id:
            query = query.where('user_id', '==', user_id)
        if status:
            query = query.where('status', '==', status)
        
        # Apply pagination
        query = query.limit(limit).offset(offset)
        
        # Get documents
        docs = list(query.stream())
        
        audio_list = []
        for doc in docs:
            audio_data = doc.to_dict()
            audio_id = doc.id
            
            # Get review status if filtering by review action
            if review_action:
                review_ref = doc.reference.collection('review').document('status')
                review_doc = review_ref.get()
                if review_doc.exists:
                    review_data = review_doc.to_dict()
                    if review_data.get('action') != review_action:
                        continue
                else:
                    if review_action != 'pending':
                        continue
            
            # Get metadata
            metadata_ref = doc.reference.collection('metadata').document('details')
            metadata_doc = metadata_ref.get()
            if metadata_doc.exists:
                audio_data['metadata'] = metadata_doc.to_dict()
            
            # Get transcription
            transcription_ref = doc.reference.collection('transcriptions').document('primary')
            transcription_doc = transcription_ref.get()
            if transcription_doc.exists:
                audio_data['transcription'] = transcription_doc.to_dict()
            
            # Get review status
            review_ref = doc.reference.collection('review').document('status')
            review_doc = review_ref.get()
            if review_doc.exists:
                audio_data['review'] = review_doc.to_dict()
            
            audio_data['audio_id'] = audio_id
            audio_list.append(audio_data)
        
        return {
            "audio_files": audio_list,
            "total": len(audio_list),
            "limit": limit,
            "offset": offset
        }
        
    except Exception as e:
        logger.error(f"Error listing audio: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{audio_id}")
async def get_audio_details(audio_id: str):
    """Get detailed information about a specific audio file"""
    try:
        clean_firebase_service.initialize()
        
        # Get main audio document
        audio_ref = clean_firebase_service.db.collection('audio').document(audio_id)
        audio_doc = audio_ref.get()
        
        if not audio_doc.exists:
            raise HTTPException(status_code=404, detail="Audio not found")
        
        audio_data = audio_doc.to_dict()
        audio_data['audio_id'] = audio_id
        
        # Get all subcollection data
        # Metadata
        metadata_ref = audio_ref.collection('metadata').document('details')
        metadata_doc = metadata_ref.get()
        if metadata_doc.exists:
            audio_data['metadata'] = metadata_doc.to_dict()
        
        # Transcription
        transcription_ref = audio_ref.collection('transcriptions').document('primary')
        transcription_doc = transcription_ref.get()
        if transcription_doc.exists:
            audio_data['transcription'] = transcription_doc.to_dict()
        
        # Review status
        review_ref = audio_ref.collection('review').document('status')
        review_doc = review_ref.get()
        if review_doc.exists:
            audio_data['review'] = review_doc.to_dict()
        
        # Training status
        training_ref = audio_ref.collection('training').document('status')
        training_doc = training_ref.get()
        if training_doc.exists:
            audio_data['training'] = training_doc.to_dict()
        
        return audio_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting audio details: {e}")
        raise HTTPException(status_code=500, detail=str(e))
