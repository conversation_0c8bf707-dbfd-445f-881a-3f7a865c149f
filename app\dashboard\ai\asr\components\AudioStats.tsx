import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useASR } from "@/lib/asr"

export function AudioStats() {
  const { stats } = useASR()

  if (!stats) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Audio Statistics</CardTitle>
          <CardDescription>Statistics about audio files and training data</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground py-4">
            Loading statistics...
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Audio Statistics</CardTitle>
        <CardDescription>Statistics about audio files and training data</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="text-sm font-medium">Total Audio Files</div>
              <div className="text-2xl font-bold">
                {stats.total_audio_files}
              </div>
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium">Approved Files</div>
              <div className="text-2xl font-bold">
                {stats.approved_audio_files}
              </div>
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium">Trained Samples</div>
              <div className="text-2xl font-bold">
                {stats.trained_samples}
              </div>
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium">Untrained Samples</div>
              <div className="text-2xl font-bold">
                {stats.untrained_samples}
              </div>
            </div>
          </div>

          <div className="pt-4">
            <div className="text-sm font-medium mb-2">Gender Distribution</div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">Male</div>
                <div className="text-lg font-semibold">
                  {stats.gender_distribution.male} total
                  <span className="text-sm text-muted-foreground ml-2">
                    ({stats.gender_distribution.trained.male} trained)
                  </span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">Female</div>
                <div className="text-lg font-semibold">
                  {stats.gender_distribution.female} total
                  <span className="text-sm text-muted-foreground ml-2">
                    ({stats.gender_distribution.trained.female} trained)
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 