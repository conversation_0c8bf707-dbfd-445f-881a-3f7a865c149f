# Nginx Configuration for Masalit AI Platform
# Place this in /etc/nginx/sites-available/buragatechnologies.com
# Then create symlink: sudo ln -s /etc/nginx/sites-available/buragatechnologies.com /etc/nginx/sites-enabled/

server {
    listen 80;
    server_name buragatechnologies.com www.buragatechnologies.com;
    
    # Redirect HTTP to HTTPS (Cloudflare will handle this)
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name buragatechnologies.com www.buragatechnologies.com;

    # SSL Configuration (if using origin certificates from Cloudflare)
    # ssl_certificate /path/to/your/cloudflare-origin.pem;
    # ssl_certificate_key /path/to/your/cloudflare-origin.key;
    
    # If using Let's Encrypt or other certificates
    # ssl_certificate /etc/letsencrypt/live/buragatechnologies.com/fullchain.pem;
    # ssl_certificate_key /etc/letsencrypt/live/buragatechnologies.com/privkey.pem;

    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Real IP from Cloudflare
    set_real_ip_from ************/20;
    set_real_ip_from ************/22;
    set_real_ip_from ************/22;
    set_real_ip_from **********/22;
    set_real_ip_from ************/18;
    set_real_ip_from *************/18;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from *************/22;
    set_real_ip_from ************/17;
    set_real_ip_from ***********/15;
    set_real_ip_from **********/13;
    set_real_ip_from **********/14;
    set_real_ip_from **********/13;
    set_real_ip_from **********/22;
    real_ip_header CF-Connecting-IP;

    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=api:10m rate=30r/m;
    limit_req_zone $binary_remote_addr zone=general:10m rate=60r/m;

    # Main application (Next.js frontend)
    location / {
        limit_req zone=general burst=10 nodelay;
        
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # API routes (handled by Next.js, but with stricter rate limiting)
    location /api/ {
        limit_req zone=api burst=5 nodelay;
        
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Longer timeout for API calls
        proxy_connect_timeout 120s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;
    }

    # Auth routes with stricter rate limiting
    location ~ ^/api/auth/(signin|signup|login) {
        limit_req zone=login burst=3 nodelay;
        
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Static files caching
    location /_next/static/ {
        proxy_pass http://127.0.0.1:3000;
        proxy_cache_valid 200 1y;
        add_header Cache-Control "public, immutable";
    }

    # Favicon and robots
    location = /favicon.ico {
        proxy_pass http://127.0.0.1:3000;
        access_log off;
    }

    location = /robots.txt {
        proxy_pass http://127.0.0.1:3000;
        access_log off;
    }

    # Block access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ \.(env|git|svn) {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Logging
    access_log /var/log/nginx/buragatechnologies.com.access.log;
    error_log /var/log/nginx/buragatechnologies.com.error.log;
}
