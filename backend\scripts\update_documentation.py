#!/usr/bin/env python3
"""
Update Documentation for New Firebase Structure

Updates all documentation files to reflect the new hierarchical Firebase structure.
"""

import os
import re
from datetime import datetime

def update_readme():
    """Update README.md with new structure"""
    readme_path = "../README.md"
    
    try:
        with open(readme_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Update Data Structure section
        old_structure = """## Data Structure

### Audio Collection
```typescript
{
  id: string;              // Format: audio_[timestamp]
  audio_url: string;       // Firebase Storage URL
  title: string;
  duration: number;        // In seconds
  format: string;         // WAV, MP3, OGG, WebM
  gender: string;
  created_at: Date;
  updated_at: Date;
  user_id: string;
  action: 'pending' | 'approved' | 'rejected';
  reviewed_by?: string;
  reviewed_at?: Date;
  feedback?: string;
  trained_asr: boolean;
  tts_trained: boolean;
  is_flagged: boolean;
  source: 'direct_upload';
}"""

        new_structure = """## Data Structure

### Hierarchical Audio Collection
```typescript
📁 audio/
  └── {audio_id}/
      ├── (main document)           // Core audio metadata
      │   ├── id: string
      │   ├── title: string
      │   ├── audio_url: string
      │   ├── duration: number
      │   ├── format: string
      │   ├── created_at: Date
      │   ├── user_id: string
      │   └── source: string
      │
      ├── transcriptions/
      │   └── primary/              // Main transcription
      │       ├── content: string
      │       ├── language: string
      │       ├── transcription_source: string
      │       └── created_at: Date
      │
      ├── metadata/
      │   └── details/              // Audio metadata
      │       ├── gender: string
      │       ├── language: string
      │       └── recording_context?: string
      │
      ├── review/
      │   └── status/               // Review status
      │       ├── action: 'pending' | 'approved' | 'rejected'
      │       ├── reviewed_by?: string
      │       ├── reviewed_at?: Date
      │       └── feedback?: string
      │
      ├── training/
      │   └── status/               // Unified training status
      │       ├── trained_asr: boolean
      │       ├── tts_trained: boolean
      │       ├── training_sessions: string[]
      │       └── last_trained_at?: Date
      │
      └── analytics/
          └── metrics/              // Usage metrics
              ├── play_count: number
              └── last_accessed: Date
}"""

        content = content.replace(old_structure, new_structure)
        
        # Update transcription section
        old_transcription = """### Transcription Collection
```typescript
{
  audio_id: string;        // Reference to audio document
  content: string;         // Transcription text
  language: string;        // Language code
  created_at: Date;
  updated_at: Date;
  user_id: string;
  trained_asr: boolean;
  tts_trained: boolean;
  is_flagged: boolean;
  transcription_source: 'human/manual' | 'ai/automatic';
  type: 'txt';
}
```"""

        new_transcription = """### Hierarchical Users Collection
```typescript
📁 users/
  └── {user_id}/
      ├── (main document)           // Core user info
      │   ├── email: string
      │   ├── name: string
      │   ├── username: string
      │   ├── role: string
      │   └── created_at: Date
      │
      ├── profile/
      │   └── details/              // Extended profile
      │       ├── avatar_url?: string
      │       ├── bio?: string
      │       ├── language_preferences: string[]
      │       └── email_verified: boolean
      │
      ├── statistics/
      │   └── summary/              // User statistics
      │       ├── contribution_count: number
      │       ├── audio_uploads: number
      │       ├── transcriptions_created: number
      │       └── last_activity: Date
      │
      ├── preferences/
      │   └── settings/             // User preferences
      │       ├── theme: 'light' | 'dark'
      │       ├── language: string
      │       └── notifications: object
      │
      └── security/
          └── status/               // Security settings
              ├── last_login?: Date
              ├── login_count: number
              └── two_factor_enabled: boolean
}
```

### Key Benefits of New Structure
- ✅ **Single Query**: Get audio + transcription + training status in one call
- ✅ **Unified Training Status**: Audio and transcription training managed together
- ✅ **Hierarchical Organization**: Related data grouped logically
- ✅ **Better Performance**: Optimized queries and caching
- ✅ **Future-Ready**: Easy to extend with new features"""

        content = content.replace(old_transcription, new_transcription)
        
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ Updated README.md")
        return True
        
    except Exception as e:
        print(f"❌ Error updating README.md: {e}")
        return False

def update_firestore_collections():
    """Update firestore_collections.txt with new structure"""
    firestore_path = "../firestore_collections.txt"
    
    new_content = f"""# Masalit AI Platform - Firebase Collections Structure

This file contains the NEW HIERARCHICAL structure of the Firestore collections used in the Masalit AI Platform.
Updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 📁 Hierarchical Audio Collection

### Structure:
```
📁 audio/
  └── {{audio_id}}/                    # e.g., audio_1747011480759
      ├── (main document)            # Core audio metadata
      ├── transcriptions/
      │   └── primary/               # Main transcription
      ├── metadata/
      │   └── details/               # Audio metadata
      ├── review/
      │   └── status/                # Review status
      ├── training/
      │   └── status/                # Unified training status
      └── analytics/
          └── metrics/               # Usage metrics
```

### Example Document: audio/audio_1747011480759/

**Main Document:**
```
id: "audio_1747011480759"
title: "Masalit Audio Sample"
audio_url: "gs://masalit-ai.appspot.com/audio/..."
duration: 15.5
format: "wav"
created_at: 2025-06-18T...
user_id: "GkFeiG7JyIg3Ru7EnnWlMLxCp323"
source: "direct_upload"
```

**transcriptions/primary:**
```
content: "Sîgâl gi ŋgârîyê? Gi amara ye. Amara tii ŋgonndaaye?..."
language: "masalit"
transcription_source: "human/manual"
type: "txt"
speaker_count: 1
created_at: 2025-06-18T...
```

**metadata/details:**
```
gender: "male"
language: "masalit"
dialect: null
quality_rating: 4.5
recording_context: "studio"
```

**review/status:**
```
action: "approved"
reviewed_by: "admin_user_id"
reviewed_at: 2025-06-18T...
feedback: "Good quality recording"
is_flagged: false
```

**training/status:** (UNIFIED for audio + transcription)
```
trained_asr: true
tts_trained: false
training_sessions: ["asr_session_20250618_001"]
last_trained_at: 2025-06-18T...
note: "Audio and transcription training status unified"
```

**analytics/metrics:**
```
play_count: 15
download_count: 3
validation_uses: 2
last_accessed: 2025-06-18T...
```

## 📁 Hierarchical Users Collection

### Structure:
```
📁 users/
  └── {{user_id}}/                     # e.g., GkFeiG7JyIg3Ru7EnnWlMLxCp323
      ├── (main document)            # Core user info
      ├── profile/
      │   └── details/               # Extended profile
      ├── statistics/
      │   └── summary/               # User statistics
      ├── preferences/
      │   └── settings/              # User preferences
      └── security/
          └── status/                # Security settings
```

### Example Document: users/GkFeiG7JyIg3Ru7EnnWlMLxCp323/

**Main Document:**
```
email: "<EMAIL>"
name: "John Doe"
username: "johndoe"
role: "user"
created_at: 2025-06-18T...
```

**profile/details:**
```
avatar_url: "https://..."
bio: "Masalit language contributor"
location: "Sudan"
language_preferences: ["masalit", "en"]
email_verified: true
phone_verified: false
```

**statistics/summary:**
```
contribution_count: 25
audio_uploads: 15
transcriptions_created: 10
reviews_completed: 5
training_sessions: 2
total_audio_duration: 450.5
last_activity: 2025-06-18T...
```

**preferences/settings:**
```
theme: "light"
language: "en"
notifications: {{
  email: true,
  push: true,
  training_updates: true
}}
privacy: {{
  profile_public: false,
  stats_public: false
}}
```

**security/status:**
```
last_login: 2025-06-18T...
login_count: 45
failed_login_attempts: 0
account_locked: false
two_factor_enabled: false
is_disabled: false
```

## 📁 Other Collections (Clean Structure)

### settings/
```
📁 settings/
  ├── asr_training/          # ASR training configuration
  ├── asr_validation/        # ASR validation configuration
  ├── tts_training/          # TTS training configuration
  └── system/                # System settings
```

### validation/
```
📁 validation/
  └── {{validation_id}}/
      ├── (main document)    # Basic validation info
      ├── config/
      │   └── settings       # Validation configuration
      ├── progress/
      │   └── current        # Real-time progress
      └── results/
          └── final          # Final validation results
```

### training/
```
📁 training/
  └── {{session_id}}/
      ├── (main document)    # Basic training info
      ├── config/
      │   └── settings       # Training configuration
      ├── progress/
      │   └── current        # Real-time progress
      └── results/
          └── final          # Final training results
```

### system/
```
📁 system/
  └── config/
      └── settings/
          ├── logging        # Log configuration
          ├── security       # Security settings
          ├── performance    # Performance settings
          └── features       # Feature toggles
```

### logs/
```
📁 logs/
  ├── system/
  │   └── entries/           # System log entries
  ├── training/
  │   └── entries/           # Training log entries
  └── validation/
      └── entries/           # Validation log entries
```

### monitoring/
```
📁 monitoring/
  ├── health                 # System health data
  ├── metrics               # Performance metrics
  └── alerts                # System alerts
```

## 🎯 Key Benefits of New Structure

1. **Single Query Performance**: Get audio + transcription + training status in one call
2. **Unified Training Status**: Audio and transcription training managed together (no duplication)
3. **Hierarchical Organization**: Related data grouped logically in subcollections
4. **Better Caching**: Logical data boundaries improve caching efficiency
5. **Future-Ready**: Easy to extend with new features (alternative transcriptions, AI-generated content)
6. **Clean Collection Names**: No "_v2" or temporary suffixes
7. **Consistent Patterns**: All collections follow the same hierarchical approach

## 🔄 Migration Completed

- ✅ All 35 audio records migrated to hierarchical structure
- ✅ All 35 transcriptions moved to audio subcollections
- ✅ All 4 user records migrated to hierarchical structure
- ✅ Training status unified (single source of truth)
- ✅ Old collections cleaned up
- ✅ Collection names cleaned (no "_v2" suffixes)

This structure provides a solid foundation for the Masalit AI Platform's continued growth and development.
"""
    
    try:
        with open(firestore_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ Updated firestore_collections.txt")
        return True
        
    except Exception as e:
        print(f"❌ Error updating firestore_collections.txt: {e}")
        return False

def update_firebase_clean_proposal():
    """Update the Firebase clean structure proposal"""
    proposal_path = "../docs/firebase_clean_structure_proposal.md"
    
    try:
        with open(proposal_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Add completion notice at the top
        completion_notice = f"""# ✅ COMPLETED: Firebase Clean Structure Implementation

**Status**: ✅ **FULLY IMPLEMENTED AND DEPLOYED**
**Completion Date**: {datetime.now().strftime('%Y-%m-%d')}
**Migration Status**: All collections successfully migrated to clean hierarchical structure

## 🎉 Implementation Results

- ✅ **35 audio records** migrated to hierarchical `audio/` collection
- ✅ **35 transcriptions** integrated as `audio/{{id}}/transcriptions/primary/`
- ✅ **4 user records** migrated to hierarchical `users/` collection
- ✅ **Unified training status** - single source of truth for audio + transcription
- ✅ **Clean collection names** - no "_v2" suffixes
- ✅ **Old collections cleaned up** - redundant data removed
- ✅ **Code updated** - all APIs and services use new structure
- ✅ **Documentation updated** - reflects current implementation

---

# Original Proposal (Now Implemented)

"""
        
        # Add the completion notice at the beginning
        content = completion_notice + content
        
        # Update the existing collections section
        old_existing = """### **5. Existing Collections (Unchanged)**
```
📁 models/          # Model metadata and info
📁 audio/           # Audio files and metadata
📁 transcription/   # Transcription data
📁 users/           # User data and preferences
```"""

        new_existing = """### **5. Core Collections (Now Hierarchical)**
```
📁 audio/           # ✅ IMPLEMENTED: Hierarchical audio + transcriptions
📁 users/           # ✅ IMPLEMENTED: Hierarchical user data
📁 models/          # Model metadata and info (unchanged)
```

**Note**: The `transcription/` collection has been eliminated - transcriptions are now stored as subcollections within `audio/{{id}}/transcriptions/`."""

        content = content.replace(old_existing, new_existing)
        
        with open(proposal_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ Updated firebase_clean_structure_proposal.md")
        return True
        
    except Exception as e:
        print(f"❌ Error updating firebase_clean_structure_proposal.md: {e}")
        return False

def main():
    """Main function to update all documentation"""
    print("📝 Updating Documentation for New Firebase Structure")
    print("=" * 60)
    
    success_count = 0
    total_files = 3
    
    # Update README.md
    if update_readme():
        success_count += 1
    
    # Update firestore_collections.txt
    if update_firestore_collections():
        success_count += 1
    
    # Update Firebase clean proposal
    if update_firebase_clean_proposal():
        success_count += 1
    
    print(f"\n📊 Documentation Update Results:")
    print(f"✅ Successfully updated: {success_count}/{total_files} files")
    
    if success_count == total_files:
        print("\n🎉 All documentation updated successfully!")
        print("📚 Updated files:")
        print("   - README.md (new hierarchical structure)")
        print("   - firestore_collections.txt (complete new structure)")
        print("   - firebase_clean_structure_proposal.md (marked as completed)")
    else:
        print(f"\n⚠️ Some files failed to update. Check the errors above.")

if __name__ == "__main__":
    main()
