// Direct Firebase Service for Hierarchical Structure
// All operations handled client-side with Firebase SDK

import { 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  setDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit as firestoreLimit,
  writeBatch,
  serverTimestamp,
  Timestamp
} from "firebase/firestore"
import { ref, uploadBytes, getDownloadURL, deleteObject } from "firebase/storage"
import { db, storage } from "@/lib/firebase"

// Types for hierarchical structure
export interface HierarchicalAudioData {
  id: string
  title: string
  audio_url: string
  duration: number
  format: string
  created_at: string
  user_id: string
  source: string

  // Subcollections
  transcriptions?: {
    primary?: {
      content: string
      language: string
      transcription_source: string
      created_at: string
      type: string
      speaker_count: number
    }
  }
  metadata?: {
    details?: {
      gender: string
      language: string
      recording_context?: string
    }
  }
  review?: {
    status?: {
      action: 'pending' | 'approved' | 'rejected'
      reviewed_by?: string
      reviewed_at?: string
      feedback?: string
      is_flagged: boolean
    }
  }
  training?: {
    status?: {
      trained_asr: boolean
      tts_trained: boolean
      training_sessions: string[]
      last_trained_at?: string
    }
  }
  analytics?: {
    metrics?: {
      play_count: number
      download_count: number
      last_accessed: string
    }
  }
}

export interface HierarchicalUserData {
  email: string
  name: string
  username: string
  role: string
  created_at: string

  // Subcollections
  profile?: {
    details?: {
      avatar_url?: string
      bio?: string
      language_preferences: string[]
      email_verified: boolean
    }
  }
  statistics?: {
    summary?: {
      contribution_count: number
      audio_uploads: number
      transcriptions_created: number
      last_activity: string
    }
  }
  preferences?: {
    settings?: {
      theme: 'light' | 'dark'
      language: string
      notifications: object
    }
  }
}

// ==================== AUDIO OPERATIONS ====================

export async function createAudioWithTranscription(audioData: {
  title: string
  duration: number
  format: string
  user_id: string
  source: string
  audio_url: string
  transcription: {
    content: string
    language: string
    transcription_source: string
    type: string
  }
  metadata: {
    gender: string
    language: string
    recording_context?: string
  }
}): Promise<{ audio_id: string }> {
  const audio_id = `audio_${Date.now()}`
  const timestamp = new Date().toISOString()

  try {
    console.log('Creating audio with transcription:', audio_id)

    // Main audio document
    const audioRef = doc(db, 'audio', audio_id)
    await setDoc(audioRef, {
      id: audio_id,
      title: audioData.title,
      audio_url: audioData.audio_url,
      duration: audioData.duration,
      format: audioData.format,
      created_at: timestamp,
      user_id: audioData.user_id,
      source: audioData.source
    })

    // Transcription subcollection
    const transcriptionRef = doc(db, 'audio', audio_id, 'transcriptions', 'primary')
    await setDoc(transcriptionRef, {
      ...audioData.transcription,
      created_at: timestamp,
      speaker_count: 1
    })

    // Metadata subcollection
    const metadataRef = doc(db, 'audio', audio_id, 'metadata', 'details')
    await setDoc(metadataRef, audioData.metadata)

    // Review status subcollection (default to pending)
    const reviewRef = doc(db, 'audio', audio_id, 'review', 'status')
    await setDoc(reviewRef, {
      action: 'pending',
      is_flagged: false,
      created_at: timestamp
    })

    // Training status subcollection (default to not trained)
    const trainingRef = doc(db, 'audio', audio_id, 'training', 'status')
    await setDoc(trainingRef, {
      trained_asr: false,
      tts_trained: false,
      training_sessions: [],
      created_at: timestamp
    })

    // Analytics subcollection
    const analyticsRef = doc(db, 'audio', audio_id, 'analytics', 'metrics')
    await setDoc(analyticsRef, {
      play_count: 0,
      download_count: 0,
      validation_uses: 0,
      created_at: timestamp
    })

    console.log('Audio record created successfully:', audio_id)

    // Update user statistics (don't fail if this fails)
    try {
      await incrementUserStatistic(audioData.user_id, 'audio_uploads', 1)
      await incrementUserStatistic(audioData.user_id, 'transcriptions_created', 1)
      await incrementUserStatistic(audioData.user_id, 'contribution_count', 1)
    } catch (statsError) {
      console.warn('Failed to update user statistics:', statsError)
    }

    return { audio_id }
  } catch (error) {
    console.error('Error creating audio with transcription:', error)
    throw error
  }
}

export async function getAudioWithSubcollections(
  audioId: string,
  includeSubcollections: string[] = ['transcriptions', 'metadata', 'review', 'training', 'analytics']
): Promise<HierarchicalAudioData | null> {
  try {
    // Get main document
    const audioDoc = await getDoc(doc(db, 'audio', audioId))
    if (!audioDoc.exists()) {
      return null
    }

    const audioData = audioDoc.data() as HierarchicalAudioData
    audioData.id = audioDoc.id

    // Get subcollections
    for (const subcollection of includeSubcollections) {
      try {
        switch (subcollection) {
          case 'transcriptions':
            const transcriptionDoc = await getDoc(doc(db, 'audio', audioId, 'transcriptions', 'primary'))
            if (transcriptionDoc.exists()) {
              audioData.transcriptions = { primary: transcriptionDoc.data() as any }
            }
            break

          case 'metadata':
            const metadataDoc = await getDoc(doc(db, 'audio', audioId, 'metadata', 'details'))
            if (metadataDoc.exists()) {
              audioData.metadata = { details: metadataDoc.data() as any }
            }
            break

          case 'review':
            const reviewDoc = await getDoc(doc(db, 'audio', audioId, 'review', 'status'))
            if (reviewDoc.exists()) {
              audioData.review = { status: reviewDoc.data() as any }
            }
            break

          case 'training':
            const trainingDoc = await getDoc(doc(db, 'audio', audioId, 'training', 'status'))
            if (trainingDoc.exists()) {
              audioData.training = { status: trainingDoc.data() as any }
            }
            break

          case 'analytics':
            const analyticsDoc = await getDoc(doc(db, 'audio', audioId, 'analytics', 'metrics'))
            if (analyticsDoc.exists()) {
              audioData.analytics = { metrics: analyticsDoc.data() as any }
            }
            break
        }
      } catch (error) {
        console.warn(`Error loading ${subcollection} for audio ${audioId}:`, error)
      }
    }

    return audioData
  } catch (error) {
    console.error('Error getting audio with subcollections:', error)
    throw error
  }
}

export async function listUserAudio(
  userId: string,
  options: {
    action?: string
    trained_asr?: boolean
    tts_trained?: boolean
    limit?: number
  } = {}
): Promise<HierarchicalAudioData[]> {
  try {
    console.log('Listing user audio for:', userId)

    // Get user's audio records (without orderBy to avoid index issues)
    let audioQuery = query(
      collection(db, 'audio'),
      where('user_id', '==', userId)
    )

    if (options.limit) {
      audioQuery = query(audioQuery, firestoreLimit(options.limit))
    }

    const audioSnapshot = await getDocs(audioQuery)
    console.log('Found audio documents:', audioSnapshot.docs.length)

    const audioRecords: HierarchicalAudioData[] = []

    for (const audioDoc of audioSnapshot.docs) {
      try {
        const audioData = await getAudioWithSubcollections(audioDoc.id)
        if (audioData) {
          // Apply filters
          if (options.action) {
            const action = audioData.review?.status?.action || 'pending'
            if (action !== options.action) continue
          }

          if (options.trained_asr !== undefined) {
            const trained = audioData.training?.status?.trained_asr || false
            if (trained !== options.trained_asr) continue
          }

          if (options.tts_trained !== undefined) {
            const trained = audioData.training?.status?.tts_trained || false
            if (trained !== options.tts_trained) continue
          }

          audioRecords.push(audioData)
        }
      } catch (docError) {
        console.warn('Error processing audio document:', audioDoc.id, docError)
      }
    }

    // Sort by created_at client-side
    audioRecords.sort((a, b) => {
      const dateA = new Date(a.created_at || 0).getTime()
      const dateB = new Date(b.created_at || 0).getTime()
      return dateB - dateA // Descending order
    })

    console.log('Returning audio records:', audioRecords.length)
    return audioRecords
  } catch (error) {
    console.error('Error listing user audio:', error)
    throw error
  }
}

export async function updateAudioRecord(
  audioId: string,
  updates: {
    title?: string
    transcription_content?: string
    metadata?: {
      gender?: string
      language?: string
      recording_context?: string
    }
  }
): Promise<void> {
  const batch = writeBatch(db)

  try {
    // Update main document
    if (updates.title) {
      const audioRef = doc(db, 'audio', audioId)
      batch.update(audioRef, { title: updates.title })
    }

    // Update transcription
    if (updates.transcription_content) {
      const transcriptionRef = doc(db, 'audio', audioId, 'transcriptions', 'primary')
      batch.update(transcriptionRef, { content: updates.transcription_content })
    }

    // Update metadata
    if (updates.metadata) {
      const metadataRef = doc(db, 'audio', audioId, 'metadata', 'details')
      batch.update(metadataRef, updates.metadata)
    }

    await batch.commit()
  } catch (error) {
    console.error('Error updating audio record:', error)
    throw error
  }
}

export async function deleteAudioRecord(audioId: string): Promise<void> {
  const batch = writeBatch(db)

  try {
    // Delete main document
    const audioRef = doc(db, 'audio', audioId)
    batch.delete(audioRef)

    // Delete subcollections
    const subcollections = [
      'transcriptions/primary',
      'metadata/details', 
      'review/status',
      'training/status',
      'analytics/metrics'
    ]

    for (const subcollectionPath of subcollections) {
      const [subcollection, docId] = subcollectionPath.split('/')
      const subRef = doc(db, 'audio', audioId, subcollection, docId)
      batch.delete(subRef)
    }

    await batch.commit()
  } catch (error) {
    console.error('Error deleting audio record:', error)
    throw error
  }
}

// ==================== REVIEW OPERATIONS ====================

export async function getPendingAudioRecords(
  filters: {
    source?: string
    userSearch?: string
    limit?: number
  } = {}
): Promise<HierarchicalAudioData[]> {
  try {
    console.log('Getting pending audio records with filters:', filters)

    // Get all audio records (without orderBy to avoid index issues)
    let audioQuery = query(collection(db, 'audio'))

    if (filters.limit) {
      audioQuery = query(audioQuery, firestoreLimit(filters.limit))
    }

    const audioSnapshot = await getDocs(audioQuery)
    console.log('Found total audio documents:', audioSnapshot.docs.length)

    const pendingRecords: HierarchicalAudioData[] = []

    for (const audioDoc of audioSnapshot.docs) {
      try {
        const audioData = await getAudioWithSubcollections(audioDoc.id)
        if (audioData) {
          // Check if pending
          const action = audioData.review?.status?.action || 'pending'
          if (action !== 'pending') continue

          // Apply source filter
          if (filters.source && filters.source !== 'all' && audioData.source !== filters.source) {
            continue
          }

          // Add user information
          if (audioData.user_id) {
            try {
              const userData = await getUserWithSubcollections(audioData.user_id, ['profile'])
              if (userData) {
                (audioData as any).email = userData.email
                (audioData as any).username = userData.username
                (audioData as any).name = userData.name
              }
            } catch (userError) {
              console.warn('Error getting user data for:', audioData.user_id, userError)
            }
          }

          // Apply user search filter
          if (filters.userSearch) {
            const search = filters.userSearch.toLowerCase()
            const email = (audioData as any).email?.toLowerCase() || ''
            const username = (audioData as any).username?.toLowerCase() || ''
            const name = (audioData as any).name?.toLowerCase() || ''

            if (!email.includes(search) && !username.includes(search) && !name.includes(search)) {
              continue
            }
          }

          pendingRecords.push(audioData)
        }
      } catch (docError) {
        console.warn('Error processing audio document:', audioDoc.id, docError)
      }
    }

    // Sort by created_at client-side
    pendingRecords.sort((a, b) => {
      const dateA = new Date(a.created_at || 0).getTime()
      const dateB = new Date(b.created_at || 0).getTime()
      return dateB - dateA // Descending order
    })

    console.log('Returning pending records:', pendingRecords.length)
    return pendingRecords
  } catch (error) {
    console.error('Error getting pending audio records:', error)
    throw error
  }
}

export async function updateReviewStatus(
  audioId: string,
  reviewData: {
    action: 'pending' | 'approved' | 'rejected'
    reviewed_by?: string
    reviewed_at?: string
    feedback?: string
  }
): Promise<void> {
  try {
    const reviewRef = doc(db, 'audio', audioId, 'review', 'status')
    await updateDoc(reviewRef, {
      ...reviewData,
      reviewed_at: reviewData.reviewed_at || new Date().toISOString(),
      is_flagged: false
    })
  } catch (error) {
    console.error('Error updating review status:', error)
    throw error
  }
}

// ==================== USER OPERATIONS ====================

export async function getUserWithSubcollections(
  userId: string,
  includeSubcollections: string[] = ['profile', 'statistics', 'preferences']
): Promise<HierarchicalUserData | null> {
  try {
    // Get main document
    const userDoc = await getDoc(doc(db, 'users', userId))
    if (!userDoc.exists()) {
      return null
    }

    const userData = userDoc.data() as HierarchicalUserData

    // Get subcollections
    for (const subcollection of includeSubcollections) {
      try {
        switch (subcollection) {
          case 'profile':
            const profileDoc = await getDoc(doc(db, 'users', userId, 'profile', 'details'))
            if (profileDoc.exists()) {
              userData.profile = { details: profileDoc.data() as any }
            }
            break

          case 'statistics':
            const statsDoc = await getDoc(doc(db, 'users', userId, 'statistics', 'summary'))
            if (statsDoc.exists()) {
              userData.statistics = { summary: statsDoc.data() as any }
            }
            break

          case 'preferences':
            const prefsDoc = await getDoc(doc(db, 'users', userId, 'preferences', 'settings'))
            if (prefsDoc.exists()) {
              userData.preferences = { settings: prefsDoc.data() as any }
            }
            break
        }
      } catch (error) {
        console.warn(`Error loading ${subcollection} for user ${userId}:`, error)
      }
    }

    return userData
  } catch (error) {
    console.error('Error getting user with subcollections:', error)
    throw error
  }
}

export async function checkUsernameAvailability(username: string): Promise<boolean> {
  try {
    const usersQuery = query(
      collection(db, 'users'),
      where('username', '==', username),
      firestoreLimit(1)
    )

    const snapshot = await getDocs(usersQuery)
    return snapshot.empty
  } catch (error) {
    console.error('Error checking username availability:', error)
    throw error
  }
}

export async function incrementUserStatistic(
  userId: string,
  statName: string,
  incrementBy: number = 1
): Promise<void> {
  try {
    const statsRef = doc(db, 'users', userId, 'statistics', 'summary')
    const statsDoc = await getDoc(statsRef)

    let currentStats = {}
    if (statsDoc.exists()) {
      currentStats = statsDoc.data()
    }

    const currentValue = (currentStats as any)[statName] || 0
    const updatedStats = {
      ...currentStats,
      [statName]: currentValue + incrementBy,
      last_activity: new Date().toISOString()
    }

    await setDoc(statsRef, updatedStats)
  } catch (error) {
    console.error('Error incrementing user statistic:', error)
    throw error
  }
}

// ==================== BULK OPERATIONS ====================

export async function bulkUploadAudio(
  files: File[],
  metadata: Array<{
    filename: string
    title: string
    transcription: string
    speaker: string
    duration?: number
  }>,
  userId: string
): Promise<{ success: boolean; message: string; results?: any[] }> {
  try {
    const results = []

    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      const meta = metadata[i]

      try {
        // Upload file to storage
        const timestamp = Date.now()
        const fileExtension = file.name.split('.').pop()
        const fileName = `${userId}/${timestamp}_${meta.title}.${fileExtension}`
        const storageRef = ref(storage, `audio/${fileName}`)

        const snapshot = await uploadBytes(storageRef, file)
        const downloadURL = await getDownloadURL(snapshot.ref)

        // Create audio record
        const audioData = {
          title: meta.title,
          duration: meta.duration || 0,
          format: file.type,
          user_id: userId,
          source: "bulk_upload",
          audio_url: downloadURL,
          transcription: {
            content: meta.transcription,
            language: "masalit",
            transcription_source: "human/manual",
            type: "txt"
          },
          metadata: {
            gender: meta.speaker,
            language: "masalit",
            recording_context: "bulk_upload"
          }
        }

        const result = await createAudioWithTranscription(audioData)
        results.push({ filename: file.name, audio_id: result.audio_id, status: 'success' })

      } catch (error) {
        console.error(`Error uploading ${file.name}:`, error)
        results.push({ filename: file.name, status: 'error', error: error.message })
      }
    }

    const successCount = results.filter(r => r.status === 'success').length

    return {
      success: successCount > 0,
      message: `Successfully uploaded ${successCount}/${files.length} files`,
      results
    }

  } catch (error) {
    console.error('Error in bulk upload:', error)
    throw error
  }
}
