# Masalit AI Platform

A comprehensive platform for managing and processing Masalit language audio recordings and transcriptions.

## Project Overview

The Masalit AI Platform is a full-stack application that provides a robust system for managing audio recordings and transcriptions in the Masalit language. The platform enables users to upload, review, and manage audio recordings with their transcriptions, supporting both individual and bulk uploads.

## Features

### Audio Management
- **Single Upload**
  - Direct file upload or browser-based recording
  - Support for WAV, MP3, OGG, and WebM formats
  - File size limit: 50MB
  - Automatic duration calculation
  - Gender classification
  - Source tracking (direct upload/recording)

- **Bulk Upload**
  - Multiple file upload with metadata spreadsheet
  - Excel/CSV template for metadata
  - Automatic file-metadata matching
  - Batch processing with progress tracking
  - Validation for required fields

### Transcription Management
- Manual transcription creation
- Bulk transcription upload
- Language and dialect support
- Training status tracking
- Flagging system for quality control

### Review System
- Pending recordings review
- Approval/rejection workflow
- Feedback system
- Bulk review capabilities
- Filtering and search
- Pagination support
- Audio playback in browser

### User Interface
- Modern, responsive dashboard
- Real-time status monitoring
- User authentication and authorization
- Role-based access control
- Activity feed
- User statistics and metrics
- Top contributors tracking

## Tech Stack

### Frontend
- **Framework**: Next.js 15.2.4
- **Language**: TypeScript
- **Package Manager**: pnpm
- **Styling**: Tailwind CSS with Tailwind Animate
- **UI Components**: Radix UI (comprehensive component library)
- **State Management**: Zustand, React Hooks
- **Forms**: React Hook Form with Hookform Resolvers
- **Validation**: Zod
- **Authentication**: NextAuth.js v4
- **Charts**: Chart.js, Recharts, React Chart.js 2
- **File Processing**: Web Audio API, XLSX, Music Metadata, Get Audio Duration
- **Database**: Firebase/Firestore
- **Storage**: Firebase Storage, Google Cloud Storage
- **Additional Libraries**:
  - Lucide React (icons)
  - Date-fns (date utilities)
  - React Hot Toast & Sonner (notifications)
  - Tesseract.js (OCR)
  - YTDL Core (YouTube downloads)
  - Input OTP (one-time passwords)
  - Embla Carousel React
  - React Resizable Panels

### Backend
- **Framework**: FastAPI
- **Language**: Python 3.9+
- **AI/ML**: OpenAI Whisper, Transformers, PyTorch
- **Audio Processing**: Librosa, SoundFile, TorchAudio
- **Database**: Firebase/Firestore
- **Storage**: Google Cloud Storage
- **Deployment**: Gunicorn, Uvicorn
- **Monitoring**: psutil, GPUtil

## Project Structure

```
masalit-ai/
├── app/                    # Next.js 15 application
│   ├── api/               # API routes
│   │   ├── audio/        # Audio management
│   │   ├── auth/         # Authentication
│   │   ├── health/       # Health checks
│   │   ├── recordings/   # Recording management
│   │   ├── save-transcription/ # Transcription
│   │   └── training/     # Training
│   │
│   ├── dashboard/        # Dashboard pages
│   │   ├── admin/       # Admin management
│   │   ├── ai/         # AI features & backend status
│   │   ├── bulk-upload/ # Bulk upload
│   │   ├── history/    # Recording history
│   │   ├── managed/    # Managed recordings
│   │   ├── profile/    # User profile
│   │   ├── review/     # Review interface
│   │   └── upload/     # Single upload
│   │
│   ├── auth/            # Authentication pages
│   ├── signup/          # Registration pages
│   ├── globals.css      # Global styles
│   ├── layout.tsx       # Root layout
│   └── page.tsx         # Home page
│
├── backend/             # Backend directory
│   ├── README.md        # Backend documentation
│   ├── requirements.txt # Python dependencies
│   └── venv/           # Python virtual environment
│
├── components/          # Reusable UI components
│   ├── ui/             # Base UI components (Radix UI)
│   ├── dashboard/      # Dashboard-specific components
│   ├── ai/            # AI-related components
│   └── auth/          # Authentication components
│
├── hooks/              # Custom React hooks
├── lib/                # Core functionality & utilities
│   ├── audio-upload.ts # Audio upload logic
│   ├── firebase.ts     # Firebase client config
│   ├── firebase-admin.ts # Firebase admin config
│   ├── auth.ts         # Authentication logic
│   ├── db.ts          # Database utilities
│   └── utils.ts       # General utilities
│
├── types/              # TypeScript type definitions
├── config/             # Configuration files
├── public/             # Static assets
├── styles/             # Additional styles
│
├── package.json        # Node.js dependencies
├── pnpm-lock.yaml     # pnpm lock file
├── render.yaml        # Render.com deployment config
├── Procfile           # Process file for deployment
├── vercel.json        # Vercel deployment config
├── start-backend.py   # Backend development startup script
└── SETUP.md          # Detailed setup guide
```

## Data Structure

### Hierarchical Audio Collection
```typescript
📁 audio/
  └── {audio_id}/
      ├── (main document)           // Core audio metadata
      │   ├── id: string
      │   ├── title: string
      │   ├── audio_url: string
      │   ├── duration: number
      │   ├── format: string
      │   ├── created_at: Date
      │   ├── user_id: string
      │   └── source: string
      │
      ├── transcriptions/
      │   └── primary/              // Main transcription
      │       ├── content: string
      │       ├── language: string
      │       ├── transcription_source: string
      │       └── created_at: Date
      │
      ├── metadata/
      │   └── details/              // Audio metadata
      │       ├── gender: string
      │       ├── language: string
      │       └── recording_context?: string
      │
      ├── review/
      │   └── status/               // Review status
      │       ├── action: 'pending' | 'approved' | 'rejected'
      │       ├── reviewed_by?: string
      │       ├── reviewed_at?: Date
      │       └── feedback?: string
      │
      ├── training/
      │   └── status/               // Unified training status
      │       ├── trained_asr: boolean
      │       ├── tts_trained: boolean
      │       ├── training_sessions: string[]
      │       └── last_trained_at?: Date
      │
      └── analytics/
          └── metrics/              // Usage metrics
              ├── play_count: number
              └── last_accessed: Date
}
```

### Transcription Collection
```typescript
{
  id: string;              // Format: transcription_[audio_id]
  audio_id: string;        // Reference to audio document
  content: string;         // Transcription text
  language: string;        // e.g., "masalit"
  dialect?: string;
  created_at: Date;
  updated_at: Date;
  user_id: string;
  transcription_source: 'human/manual' | 'ai';
  type: 'txt';
  trained_asr: boolean;
  tts_trained: boolean;
  is_flagged: boolean;
}
```

## Quick Start

### 🚀 Backend Deployment (One Command)
```bash
# Deploy backend locally (from project root)
python deploy-backend.py local

# Or for production mode
python deploy-backend.py local --mode production
```

### 📱 Frontend Development
```bash
# Install and start frontend
pnpm install
pnpm dev
```

### ☁️ Cloud Deployment
```bash
# Generate cloud deployment configurations
cd backend
python deploy.py cloud-config
```

## Getting Started

### Prerequisites
- Node.js 18+ (recommended: Node.js 20+)
- Python 3.9+ (for backend)
- pnpm (recommended package manager)
- Firebase account and project setup
- Google Cloud Storage account (for model storage)
- FFmpeg (for audio processing)

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/iabakar/masalit-ai.git
   cd masalit-ai
   ```

2. Install frontend dependencies:
   ```bash
   # Install pnpm if not already installed
   npm install -g pnpm

   # Install dependencies
   pnpm install
   ```

3. Set up Python backend:
   ```bash
   # Create virtual environment
   python -m venv backend/venv

   # Activate virtual environment
   # On Windows:
   backend\venv\Scripts\activate
   # On macOS/Linux:
   source backend/venv/bin/activate

   # Install Python dependencies
   pip install -r requirements.txt
   ```

4. Set up environment variables:
   - Create `.env.local` for development or `.env` for production
   - Add Firebase configuration
   - Add Google Cloud Storage configuration
   - Add authentication settings
   - See [Environment Configuration](#environment-configuration) section below

5. Start the development servers:
   ```bash
   # Start frontend (in one terminal)
   pnpm dev

   # Start backend (in another terminal, with venv activated)
   python asr.py
   ```

6. Access the application:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

## Package Management

This project uses **pnpm** as the primary package manager for better performance and disk efficiency.

### Why pnpm?
- Faster installation and updates
- Efficient disk space usage through hard linking
- Strict dependency resolution
- Better monorepo support

### Common Commands
```bash
# Install dependencies
pnpm install

# Add a dependency
pnpm add <package-name>

# Add a dev dependency
pnpm add -D <package-name>

# Remove a dependency
pnpm remove <package-name>

# Update dependencies
pnpm update

# Run scripts
pnpm dev
pnpm build
pnpm start
```

### Alternative Package Managers
While pnpm is recommended, you can also use npm or yarn:
```bash
# npm
npm install
npm run dev

# yarn
yarn install
yarn dev
```

## Troubleshooting

### Common Issues

1. **Backend Connection Issues**
   - Ensure the backend server is running on port 8000
   - Check `NEXT_PUBLIC_BACKEND_URL` environment variable
   - Verify firewall settings allow connections to port 8000

2. **Firebase Authentication Issues**
   - Verify Firebase configuration in environment variables
   - Check Firebase project settings and authentication methods
   - Ensure Firebase rules allow read/write access

3. **Audio Upload Issues**
   - Check file size limits (50MB max)
   - Verify supported formats: WAV, MP3, OGG, WebM
   - Ensure Firebase Storage is properly configured

4. **Build Issues**
   - Clear node_modules and reinstall: `rm -rf node_modules && pnpm install`
   - Clear Next.js cache: `pnpm build --clean` or `rm -rf .next`
   - Check for TypeScript errors: `pnpm type-check`

5. **Python Backend Issues**
   - Ensure Python 3.9+ is installed
   - Activate virtual environment before running
   - Install FFmpeg for audio processing
   - Check GPU availability for CUDA operations

### Performance Optimization

1. **Frontend**
   - Use Next.js Image component for optimized images
   - Implement proper caching strategies
   - Minimize bundle size with tree shaking

2. **Backend**
   - Use GPU acceleration when available
   - Implement proper model caching
   - Monitor memory usage during training

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Create a Pull Request

### Development Guidelines
- Follow TypeScript best practices
- Use ESLint and Prettier for code formatting
- Write unit tests for new features
- Update documentation for API changes
- Follow semantic versioning for releases

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, please:
1. Check the [Troubleshooting](#troubleshooting) section
2. Search existing [GitHub Issues](https://github.com/iabakar/masalit-ai/issues)
3. Create a new issue with detailed information
4. Contact the development team

## Acknowledgments

- OpenAI for the Whisper model
- The Masalit language community
- All contributors to the project
- Radix UI for the component library
- Vercel for Next.js framework

## Environment Configuration

### Development Environment
Create a `.env.local` file in the root directory with the following variables:

```env
# API Configuration
NEXT_PUBLIC_BACKEND_URL=http://127.0.0.1:8000
NEXT_PUBLIC_APP_URL=http://127.0.0.1:3000
NEXT_PUBLIC_API_URL=http://127.0.0.1:8000

# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_auth_domain
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_storage_bucket
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000
```

### Production Environment
Create a `.env` file in the root directory with the following variables:

```env
# API Configuration
NEXT_PUBLIC_BACKEND_URL=https://api.buragatechnologies.com
NEXT_PUBLIC_APP_URL=https://buragatechnologies.com
NEXT_PUBLIC_API_URL=https://buragatechnologies.com/api

# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_auth_domain
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_storage_bucket
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=https://buragatechnologies.com
```

### Environment Differences

1. **Development**
   - Local development server (localhost:3000)
   - Local backend server (localhost:8000)
   - Debug mode enabled
   - Development Firebase project
   - Local API endpoints
   - Hot reloading enabled
   - Development-specific features

2. **Production**
   - Live domain (buragatechnologies.com)
   - Backend API (api.buragatechnologies.com)
   - Production optimizations
   - Production Firebase project
   - Production API endpoints
   - SSL/TLS enabled
   - Caching enabled
   - Minified assets

### Switching Environments

1. **Development**
   ```bash
   # Start frontend development server
   pnpm dev

   # Start backend development server (separate terminal)
   python asr.py
   ```

2. **Production**
   ```bash
   # Build frontend for production
   pnpm build

   # Start production frontend server
   pnpm start

   # Backend is deployed separately on Render.com
   ```

## Backend Integration

The frontend integrates with the Python FastAPI backend for AI/ML operations:

### API Integration
- **Health Checks**: `/api/health` endpoint for backend status monitoring
- **ASR Training**: Real-time training status and progress tracking
- **Model Management**: Upload, download, and manage AI models
- **Audio Processing**: Transcription and audio analysis

### Real-time Features
- WebSocket connections for training progress
- Live status updates in the dashboard
- Real-time resource monitoring (CPU, GPU, memory)

### Configuration
Backend URL is configured via environment variables:
- Development: `NEXT_PUBLIC_BACKEND_URL=http://127.0.0.1:8000`
- Production: `NEXT_PUBLIC_BACKEND_URL=https://api.buragatechnologies.com`

## Deployment

### Render.com Deployment
The application is configured for deployment on Render.com with the following setup:

1. **Frontend Service**:
   - Environment: Node.js
   - Build Command: `npm install && npm run build`
   - Start Command: `npm start`
   - Domain: buragatechnologies.com

2. **Backend Service**:
   - Environment: Python
   - Build Command: `pip install -r backend/requirements.txt`
   - Start Command: `cd backend && gunicorn main:app -c gunicorn.conf.py`
   - Domain: api.buragatechnologies.com

### Deployment Files
- `render.yaml`: Render.com service configuration
- `Procfile`: Process configuration for Heroku-style deployments
- `vercel.json`: Vercel deployment configuration (alternative)

### Environment Variables for Production
Ensure all required environment variables are set in your deployment platform:
- Firebase configuration
- Google Cloud Storage credentials
- NextAuth secrets
- API URLs
