import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const source = searchParams.get('source');
    const limit = searchParams.get('limit') || '100';
    
    // Build the URL with query parameters using environment variable
    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://127.0.0.1:8000';
    let url = `${backendUrl}/api/audio/recordings`;
    const params = new URLSearchParams();
    if (source) params.append('source', source);
    if (limit) params.append('limit', limit);
    
    const queryString = params.toString();
    if (queryString) {
      url += `?${queryString}`;
    }
    
    // Forward the request to the Python backend
    const response = await fetch(url, {
      method: 'GET',
    });

    // Get the response data
    const data = await response.json();

    // Return the response from the Python backend
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Error getting audio recordings:', error);
    return NextResponse.json(
      { error: 'Failed to get audio recordings' },
      { status: 500 }
    );
  }
} 