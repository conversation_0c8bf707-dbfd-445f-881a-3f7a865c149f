import { NextRequest, NextResponse } from 'next/server'
import { doc, getDoc, updateDoc, deleteDoc, setDoc } from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { FileDeletionService } from '@/lib/file-deletion-service'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ audioId: string }> }
) {
  try {
    const { audioId } = await params
    const { searchParams } = new URL(request.url)
    const include = searchParams.get('include')?.split(',') || ['transcriptions', 'metadata', 'review', 'training']

    // Get main audio document
    const audioDoc = await getDoc(doc(db, 'audio', audioId))
    if (!audioDoc.exists()) {
      return NextResponse.json({ error: 'Audio record not found' }, { status: 404 })
    }

    const audioData = audioDoc.data()
    const result: any = {
      id: audioId,
      ...audioData
    }

    // Get subcollections based on include parameter
    if (include.includes('transcriptions')) {
      const transcriptionDoc = await getDoc(doc(db, 'audio', audioId, 'transcriptions', 'primary'))
      if (transcriptionDoc.exists()) {
        result.transcriptions = { primary: transcriptionDoc.data() }
      }
    }

    if (include.includes('metadata')) {
      const metadataDoc = await getDoc(doc(db, 'audio', audioId, 'metadata', 'details'))
      if (metadataDoc.exists()) {
        result.metadata = { details: metadataDoc.data() }
      }
    }

    if (include.includes('review')) {
      const reviewDoc = await getDoc(doc(db, 'audio', audioId, 'review', 'status'))
      if (reviewDoc.exists()) {
        result.review = { status: reviewDoc.data() }
      }
    }

    if (include.includes('training')) {
      const trainingDoc = await getDoc(doc(db, 'audio', audioId, 'training', 'status'))
      if (trainingDoc.exists()) {
        result.training = { status: trainingDoc.data() }
      }
    }

    return NextResponse.json({
      success: true,
      data: result
    })

  } catch (error) {
    console.error('Error fetching audio record:', error)
    return NextResponse.json(
      { error: 'Failed to fetch audio record' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ audioId: string }> }
) {
  try {
    const { audioId } = await params
    const updates = await request.json()

    // Check if audio exists
    const audioDoc = await getDoc(doc(db, 'audio', audioId))
    if (!audioDoc.exists()) {
      return NextResponse.json({ error: 'Audio record not found' }, { status: 404 })
    }

    const timestamp = new Date().toISOString()

    // Update main document if needed
    if (updates.title) {
      await updateDoc(doc(db, 'audio', audioId), {
        title: updates.title,
        updated_at: timestamp
      })
    }

    // Update transcription if needed
    if (updates.transcription_content) {
      await setDoc(doc(db, 'audio', audioId, 'transcriptions', 'primary'), {
        content: updates.transcription_content,
        updated_at: timestamp
      }, { merge: true })
    }

    // Update metadata if needed
    if (updates.metadata) {
      await setDoc(doc(db, 'audio', audioId, 'metadata', 'details'), {
        ...updates.metadata,
        updated_at: timestamp
      }, { merge: true })
    }

    return NextResponse.json({
      success: true,
      message: 'Audio record updated successfully'
    })

  } catch (error) {
    console.error('Error updating audio record:', error)
    return NextResponse.json(
      { error: 'Failed to update audio record' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ audioId: string }> }
) {
  try {
    const { audioId } = await params

    // Check if audio exists
    const audioDoc = await getDoc(doc(db, 'audio', audioId))
    if (!audioDoc.exists()) {
      return NextResponse.json({ error: 'Audio record not found' }, { status: 404 })
    }

    const audioData = audioDoc.data()
    const audioUrl = audioData?.audio_url

    // Delete the actual audio file if it exists
    if (audioUrl) {
      const deletionResult = await FileDeletionService.deleteAudioFile(audioUrl)
      if (deletionResult.success) {
        console.log(deletionResult.message)
      } else {
        console.error('File deletion failed:', deletionResult.error)
        // Continue with database deletion even if file deletion fails
      }
    }

    // Delete subcollections first
    try {
      await deleteDoc(doc(db, 'audio', audioId, 'transcriptions', 'primary'))
    } catch (e) { /* subcollection might not exist */ }

    try {
      await deleteDoc(doc(db, 'audio', audioId, 'metadata', 'details'))
    } catch (e) { /* subcollection might not exist */ }

    try {
      await deleteDoc(doc(db, 'audio', audioId, 'review', 'status'))
    } catch (e) { /* subcollection might not exist */ }

    try {
      await deleteDoc(doc(db, 'audio', audioId, 'training', 'status'))
    } catch (e) { /* subcollection might not exist */ }

    try {
      await deleteDoc(doc(db, 'audio', audioId, 'analytics', 'metrics'))
    } catch (e) { /* subcollection might not exist */ }

    // Delete main document
    await deleteDoc(doc(db, 'audio', audioId))

    return NextResponse.json({
      success: true,
      message: 'Audio record and file deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting audio record:', error)
    return NextResponse.json(
      { error: 'Failed to delete audio record' },
      { status: 500 }
    )
  }
}
