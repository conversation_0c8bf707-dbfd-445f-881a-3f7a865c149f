"""
Centralized logging service that writes logs to Firestore
"""

import logging
import uuid
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from backend.services.firebase import firebase_service

class FirestoreLogHandler(logging.Handler):
    """Custom logging handler that writes logs to Firestore"""
    
    def __init__(self, source: str = "BACKEND"):
        super().__init__()
        self.source = source
        self.firebase_service = firebase_service
        
    def emit(self, record: logging.LogRecord):
        """Emit a log record to Firestore"""
        try:
            # Ensure Firebase is initialized
            if not self.firebase_service._initialized:
                self.firebase_service.initialize()

            # Check if this log level is enabled
            if not self._is_level_enabled(record.levelname):
                return  # Skip logging if level is disabled

            # Create log entry
            log_entry = {
                'id': str(uuid.uuid4()),
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'level': record.levelname,
                'message': record.getMessage(),
                'source': self.source,
                'logger_name': record.name,
                'module': record.module,
                'function': record.funcName,
                'line_number': record.lineno,
                'details': {
                    'pathname': record.pathname,
                    'process_id': record.process,
                    'thread_id': record.thread,
                }
            }

            # Add exception info if present
            if record.exc_info:
                log_entry['details']['exception'] = self.format(record)

            # Write to Firestore
            doc_ref = self.firebase_service.db.collection('system_logs').document()
            doc_ref.set(log_entry)

        except Exception as e:
            # Fallback to console logging if Firestore fails
            print(f"Failed to write log to Firestore: {e}")
            print(f"Original log: {record.getMessage()}")

    def _is_level_enabled(self, level: str) -> bool:
        """Check if a log level is enabled in Firestore settings"""
        try:
            # Get log settings from Firestore
            settings_doc = self.firebase_service.db.collection('system_settings').document('log_levels').get()

            if settings_doc.exists:
                data = settings_doc.to_dict()
                enabled_levels = data.get('enabled_levels', {})
                return enabled_levels.get(level, True)  # Default to True if not specified
            else:
                return True  # Default to enabled if no settings exist

        except Exception as e:
            print(f"Failed to check log level settings: {e}")
            return True  # Default to enabled on error

class LoggingService:
    """Centralized logging service for the backend"""
    
    def __init__(self):
        self.loggers = {}
        self.setup_root_logger()
    
    def setup_root_logger(self):
        """Setup the root logger with Firestore handler"""
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.INFO)
        
        # Remove existing handlers to avoid duplicates
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # Add console handler
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
        
        # Add Firestore handler
        firestore_handler = FirestoreLogHandler("BACKEND")
        firestore_handler.setLevel(logging.INFO)  # Only log INFO and above to Firestore
        root_logger.addHandler(firestore_handler)
    
    def get_logger(self, name: str, source: str = None) -> logging.Logger:
        """Get a logger for a specific component"""
        if name not in self.loggers:
            logger = logging.getLogger(name)
            
            # Add component-specific Firestore handler if source is provided
            if source:
                firestore_handler = FirestoreLogHandler(source)
                firestore_handler.setLevel(logging.INFO)
                logger.addHandler(firestore_handler)
            
            self.loggers[name] = logger
        
        return self.loggers[name]
    
    def log_training_event(self, model_type: str, event: str, details: Dict[str, Any] = None):
        """Log training-specific events"""
        try:
            firebase_service.initialize()

            # Check if INFO level is enabled
            if not self._is_level_enabled('INFO'):
                return

            log_entry = {
                'id': str(uuid.uuid4()),
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'level': 'INFO',
                'message': f"{model_type.upper()} training {event}",
                'source': f"{model_type.upper()}_TRAINER",
                'event_type': 'training',
                'model_type': model_type,
                'details': details or {}
            }

            # Write to both system_logs and training_logs collections
            firebase_service.db.collection('system_logs').document().set(log_entry)
            firebase_service.db.collection('training_logs').document().set(log_entry)

        except Exception as e:
            logging.error(f"Failed to log training event: {e}")
    
    def log_api_request(self, endpoint: str, method: str, status_code: int,
                       duration_ms: float, details: Dict[str, Any] = None):
        """Log API request events"""
        try:
            firebase_service.initialize()

            level = 'ERROR' if status_code >= 400 else 'INFO'

            # Check if this level is enabled
            if not self._is_level_enabled(level):
                return

            log_entry = {
                'id': str(uuid.uuid4()),
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'level': level,
                'message': f"{method} {endpoint} - {status_code} ({duration_ms:.1f}ms)",
                'source': 'API_SERVER',
                'event_type': 'api_request',
                'details': {
                    'endpoint': endpoint,
                    'method': method,
                    'status_code': status_code,
                    'duration_ms': duration_ms,
                    **(details or {})
                }
            }

            firebase_service.db.collection('system_logs').document().set(log_entry)

        except Exception as e:
            logging.error(f"Failed to log API request: {e}")
    
    def log_system_event(self, event: str, level: str = 'INFO',
                        source: str = 'SYSTEM', details: Dict[str, Any] = None):
        """Log general system events"""
        try:
            firebase_service.initialize()

            level_upper = level.upper()

            # Check if this level is enabled
            if not self._is_level_enabled(level_upper):
                return

            log_entry = {
                'id': str(uuid.uuid4()),
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'level': level_upper,
                'message': event,
                'source': source,
                'event_type': 'system',
                'details': details or {}
            }

            firebase_service.db.collection('system_logs').document().set(log_entry)

        except Exception as e:
            logging.error(f"Failed to log system event: {e}")
    
    def log_validation_event(self, model_id: str, status: str, metrics: Dict[str, Any] = None):
        """Log model validation events"""
        try:
            firebase_service.initialize()
            
            level = 'INFO' if status == 'completed' else 'ERROR'
            
            log_entry = {
                'id': str(uuid.uuid4()),
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'level': level,
                'message': f"Model validation {status} for {model_id}",
                'source': 'VALIDATION',
                'event_type': 'validation',
                'model_id': model_id,
                'details': {
                    'status': status,
                    'metrics': metrics or {}
                }
            }
            
            firebase_service.db.collection('system_logs').document().set(log_entry)
            
        except Exception as e:
            logging.error(f"Failed to log validation event: {e}")
    
    def log_audio_event(self, audio_id: str, action: str, details: Dict[str, Any] = None):
        """Log audio processing events"""
        try:
            firebase_service.initialize()
            
            level = 'INFO' if action in ['approved', 'uploaded'] else 'WARNING'
            
            log_entry = {
                'id': str(uuid.uuid4()),
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'level': level,
                'message': f"Audio {audio_id} {action}",
                'source': 'AUDIO_MANAGER',
                'event_type': 'audio',
                'audio_id': audio_id,
                'details': {
                    'action': action,
                    **(details or {})
                }
            }
            
            firebase_service.db.collection('system_logs').document().set(log_entry)
            
        except Exception as e:
            logging.error(f"Failed to log audio event: {e}")

    def _is_level_enabled(self, level: str) -> bool:
        """Check if a log level is enabled in Firestore settings"""
        try:
            firebase_service.initialize()

            # Get log settings from Firestore
            settings_doc = firebase_service.db.collection('system_settings').document('log_levels').get()

            if settings_doc.exists:
                data = settings_doc.to_dict()
                enabled_levels = data.get('enabled_levels', {})
                return enabled_levels.get(level, True)  # Default to True if not specified
            else:
                return True  # Default to enabled if no settings exist

        except Exception as e:
            print(f"Failed to check log level settings: {e}")
            return True  # Default to enabled on error

# Global logging service instance
logging_service = LoggingService()

# Convenience functions for easy access
def get_logger(name: str, source: str = None) -> logging.Logger:
    """Get a logger instance"""
    return logging_service.get_logger(name, source)

def log_training_event(model_type: str, event: str, details: Dict[str, Any] = None):
    """Log training events"""
    logging_service.log_training_event(model_type, event, details)

def log_api_request(endpoint: str, method: str, status_code: int, 
                   duration_ms: float, details: Dict[str, Any] = None):
    """Log API requests"""
    logging_service.log_api_request(endpoint, method, status_code, duration_ms, details)

def log_system_event(event: str, level: str = 'INFO', 
                    source: str = 'SYSTEM', details: Dict[str, Any] = None):
    """Log system events"""
    logging_service.log_system_event(event, level, source, details)

def log_validation_event(model_id: str, status: str, metrics: Dict[str, Any] = None):
    """Log validation events"""
    logging_service.log_validation_event(model_id, status, metrics)

def log_audio_event(audio_id: str, action: str, details: Dict[str, Any] = None):
    """Log audio events"""
    logging_service.log_audio_event(audio_id, action, details)
