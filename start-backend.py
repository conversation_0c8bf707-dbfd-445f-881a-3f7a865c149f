#!/usr/bin/env python3
"""
Development startup script for the Masalit AI Platform Backend
"""
import os
import sys
import subprocess
import argparse
from pathlib import Path

def check_requirements():
    """Check if required dependencies are installed"""
    try:
        import fastapi
        import uvicorn
        import torch
        import transformers
        print("✓ Core dependencies found")
        return True
    except ImportError as e:
        print(f"✗ Missing dependency: {e}")
        return False

def check_environment():
    """Check if environment is properly configured"""
    env_file = Path(".env")
    if not env_file.exists():
        print("✗ .env file not found. Please copy .env.example to .env and configure it.")
        return False
    
    print("✓ .env file found")
    return True

def check_ffmpeg():
    """Check if FFmpeg is installed"""
    try:
        subprocess.run(["ffmpeg", "-version"], capture_output=True, check=True)
        print("✓ FFmpeg is installed")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("✗ FFmpeg not found. Please install FFmpeg for audio processing.")
        return False

def start_backend(mode="development", host="127.0.0.1", port=8000, ssl=False):
    """Start the backend server"""
    print(f"Starting backend in {mode} mode...")

    if mode == "development":
        # Start with uvicorn for development
        cmd = [
            sys.executable, "-m", "uvicorn",
            "backend.main:app",
            "--host", host,
            "--port", str(port),
            "--reload",
            "--log-level", "info"
        ]

        # Add SSL support if requested
        if ssl:
            ssl_cert = Path("ssl/cert.pem")
            ssl_key = Path("ssl/key.pem")

            if ssl_cert.exists() and ssl_key.exists():
                cmd.extend([
                    "--ssl-certfile", str(ssl_cert),
                    "--ssl-keyfile", str(ssl_key)
                ])
                print(f"✓ SSL enabled - server will use HTTPS")
            else:
                print("✗ SSL certificates not found. Run 'python generate-ssl.py' first.")
                sys.exit(1)
    else:
        # Start with gunicorn for production
        os.chdir("backend")
        cmd = [
            sys.executable, "-m", "gunicorn",
            "main:app",
            "-c", "gunicorn.conf.py"
        ]

    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\nShutting down backend server...")

def main():
    parser = argparse.ArgumentParser(description="Start Masalit AI Platform Backend")
    parser.add_argument(
        "--mode", 
        choices=["development", "production"], 
        default="development",
        help="Server mode (default: development)"
    )
    parser.add_argument(
        "--host", 
        default="127.0.0.1",
        help="Host to bind to (default: 127.0.0.1)"
    )
    parser.add_argument(
        "--port", 
        type=int, 
        default=8000,
        help="Port to bind to (default: 8000)"
    )
    parser.add_argument(
        "--skip-checks",
        action="store_true",
        help="Skip environment checks"
    )
    parser.add_argument(
        "--ssl",
        action="store_true",
        help="Enable HTTPS with SSL certificates"
    )
    
    args = parser.parse_args()
    
    print("Masalit AI Platform Backend Startup")
    print("=" * 40)
    
    if not args.skip_checks:
        print("Checking environment...")
        
        checks = [
            check_requirements(),
            check_environment(),
            check_ffmpeg()
        ]
        
        if not all(checks):
            print("\n✗ Environment checks failed. Please fix the issues above.")
            sys.exit(1)
        
        print("\n✓ All checks passed!")
    
    print(f"\nStarting server at http://{args.host}:{args.port}")
    print("API documentation will be available at http://{args.host}:{args.port}/docs")
    print("\nPress Ctrl+C to stop the server\n")
    
    start_backend(args.mode, args.host, args.port, args.ssl)

if __name__ == "__main__":
    main()
