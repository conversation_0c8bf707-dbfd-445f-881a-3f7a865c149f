"""
ASR API endpoints
"""

import logging
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import Dict, Any, Optional
from backend.core.asr.trainer import asr_trainer
from backend.services.firebase_clean import clean_firebase_service
from backend.services.firebase import firebase_service  # Keep for models collection temporarily

logger = logging.getLogger(__name__)

router = APIRouter()

class TrainingRequest(BaseModel):
    epochs: int = 5
    learning_rate: float = 0.001
    number_of_samples: int = 100
    model_name: str = "small"
    validation_split: float = 0.2
    early_stopping_patience: int = 3
    use_augmentation: bool = False
    eval_steps: int = 100
    training_timeout: Optional[int] = None  # Training timeout in seconds (optional)
    use_existing_model: bool = True  # Whether to continue from existing model or start from base model
    # upload_to_gcs is now controlled by environment variable UPLOAD_TO_GCS

class TrainingResponse(BaseModel):
    message: str
    status: str
    error: Optional[str] = None

@router.post("/training/start", response_model=TrainingResponse)
async def start_training(request: TrainingRequest, background_tasks: BackgroundTasks):
    """Start ASR model training"""
    try:
        # Convert request to dict
        training_settings = request.model_dump()
        
        # Save settings to clean Firebase structure
        from datetime import datetime
        clean_firebase_service.initialize()
        clean_firebase_service.update_settings('asr_training', {
            **training_settings,
            'updated_at': datetime.now().isoformat()
        })
        
        # Start training
        result = await asr_trainer.start_training(training_settings)
        
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return TrainingResponse(
            message=result["message"],
            status=result["status"]
        )
        
    except Exception as e:
        logger.error(f"Error starting training: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/training/stop", response_model=TrainingResponse)
async def stop_training():
    """Stop current ASR training"""
    try:
        result = await asr_trainer.stop_training()
        
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return TrainingResponse(
            message=result["message"],
            status="stopped"
        )
        
    except Exception as e:
        logger.error(f"Error stopping training: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/training/status")
async def get_training_status():
    """Get current training status from clean structure"""
    try:
        # Get status from ASR trainer (which now uses clean structure)
        status = await asr_trainer.get_training_status()
        return status

    except Exception as e:
        logger.error(f"Error getting training status: {e}")
        return {
            "status": "error",
            "progress": 0,
            "current_epoch": 0,
            "total_epochs": 0,
            "error": str(e)
        }

@router.get("/model/info")
async def get_model_info():
    """Get current ASR model information"""
    try:
        # Use old service for now until models are migrated to clean structure
        firebase_service.initialize()
        model_info = firebase_service.get_model_info("asr")
        return model_info
    except Exception as e:
        logger.error(f"Error getting model info: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/models/list")
async def list_asr_models(status: str = None, limit: int = 20):
    """List ASR models with optional status filter"""
    try:
        # Use old service for now until models are migrated to clean structure
        firebase_service.initialize()
        models = firebase_service.list_models(model_type="asr", status=status, limit=limit)
        return {"models": models, "total": len(models)}
    except Exception as e:
        logger.error(f"Error listing ASR models: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/models/{model_id}")
async def get_model_details(model_id: str):
    """Get detailed information about a specific model"""
    try:
        # Use old service for now until models are migrated to clean structure
        firebase_service.initialize()
        doc = firebase_service.db.collection('models').document(model_id).get()

        if not doc.exists:
            raise HTTPException(status_code=404, detail="Model not found")

        model_data = doc.to_dict()
        model_data['model_id'] = doc.id
        return model_data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting model details: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/debug/training-data")
async def debug_training_data():
    """Debug endpoint to check approved audio data retrieval"""
    try:
        clean_firebase_service.initialize()

        # Get training data with debugging
        training_data = clean_firebase_service.get_training_data(model_type='asr', max_samples=10)

        # Also check the raw audio collection
        audio_query = clean_firebase_service.db.collection('audio')
        audio_docs = list(audio_query.limit(5).stream())

        raw_audio_sample = []
        for doc in audio_docs:
            audio_data = doc.to_dict()
            audio_id = doc.id

            # Check subcollections
            review_ref = doc.reference.collection('review').document('status')
            review_doc = review_ref.get()
            review_data = review_doc.to_dict() if review_doc.exists else None

            transcription_ref = doc.reference.collection('transcriptions').document('primary')
            transcription_doc = transcription_ref.get()
            transcription_data = transcription_doc.to_dict() if transcription_doc.exists else None

            training_ref = doc.reference.collection('training').document('status')
            training_doc = training_ref.get()
            training_data_doc = training_doc.to_dict() if training_doc.exists else None

            raw_audio_sample.append({
                'audio_id': audio_id,
                'audio_data': audio_data,
                'review_status': review_data,
                'transcription': transcription_data,
                'training_status': training_data_doc
            })

        return {
            "training_data_count": len(training_data),
            "training_data_sample": training_data[:3] if training_data else [],
            "raw_audio_sample": raw_audio_sample,
            "message": "Check logs for detailed debugging information"
        }

    except Exception as e:
        logger.error(f"Error in debug endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/training/settings")
async def get_training_settings():
    """Get current training settings from clean structure"""
    try:
        clean_firebase_service.initialize()
        settings = clean_firebase_service.get_settings('asr_training')
        return settings

    except Exception as e:
        logger.error(f"Error getting training settings: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/training/settings")
async def update_training_settings(request: TrainingRequest):
    """Update training settings"""
    try:
        settings = request.model_dump()
        
        # Save to clean Firebase structure
        from datetime import datetime
        clean_firebase_service.initialize()
        clean_firebase_service.update_settings('asr_training', {
            **settings,
            'updated_at': datetime.now().isoformat()
        })
        
        return {"message": "Training settings updated successfully", "settings": settings}
        
    except Exception as e:
        logger.error(f"Error updating training settings: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/data/stats")
async def get_training_data_stats():
    """Get statistics about available training data"""
    try:
        # Get training data using clean service
        clean_firebase_service.initialize()
        training_data = clean_firebase_service.get_training_data(model_type='asr')

        # Calculate statistics
        total_samples = len(training_data)
        total_duration = sum(item.get('duration', 0) for item in training_data)

        # Gender distribution
        gender_stats = {}
        for item in training_data:
            gender = item.get('gender', 'unknown')
            gender_stats[gender] = gender_stats.get(gender, 0) + 1

        # Language/dialect distribution
        language_stats = {}
        for item in training_data:
            language = item.get('language', 'unknown')
            language_stats[language] = language_stats.get(language, 0) + 1

        return {
            "total_samples": total_samples,
            "total_duration_seconds": total_duration,
            "total_duration_hours": round(total_duration / 3600, 2),
            "gender_distribution": gender_stats,
            "language_distribution": language_stats,
            "average_duration": round(total_duration / total_samples, 2) if total_samples > 0 else 0
        }

    except Exception as e:
        logger.error(f"Error getting training data stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/models/upload")
async def upload_model_to_gcs():
    """Upload latest model to Google Cloud Storage"""
    try:
        # This would be implemented to upload the latest trained model
        # For now, return a placeholder response
        return {"message": "Model upload functionality will be implemented"}
        
    except Exception as e:
        logger.error(f"Error uploading model: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/models/list")
async def list_models():
    """List available models"""
    try:
        # This would list models from both local storage and GCS
        # For now, return a placeholder response
        return {"models": [], "message": "Model listing functionality will be implemented"}
        
    except Exception as e:
        logger.error(f"Error listing models: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/validate")
async def validate_model(request: Dict[str, Any]):
    """Validate trained ASR model"""
    try:
        # Use the new modular validation system
        from backend.core.validation.validator import model_validator
        from backend.core.validation.types import ValidationConfig, ValidationType
        from backend.core.models.types import ModelType

        validation_type = request.get('validation_type', 'quick')
        config_data = request.get('config', {})

        # Create validation configuration
        config = ValidationConfig(
            validation_type=ValidationType(validation_type),
            model_type=ModelType.ASR,
            max_samples=config_data.get('max_samples', 10),
            min_confidence=config_data.get('min_confidence', 0.0),
            save_predictions=True,
            save_error_analysis=True
        )

        # Get the actual model ID from request or use a real model
        model_id = request.get('model_id', 'asr_current_model')

        # Note: This will now fail if no real models exist, which is the intended behavior

        # Run validation
        results = await model_validator.validate_model(model_id, config)

        # Convert to legacy format for compatibility
        legacy_result = {
            "wer": results.metrics.wer or 0.0,
            "cer": results.metrics.cer or 0.0,
            "ser": results.metrics.ser or 0.0,
            "confidence": results.metrics.confidence,
            "samples_tested": results.summary.successful_samples,
            "error_analysis": [
                {
                    "audio_id": error.sample_id,
                    "reference": error.reference,
                    "hypothesis": error.prediction,
                    "metrics": {
                        "wer": error.sample_metrics.wer or 0.0,
                        "cer": error.sample_metrics.cer or 0.0,
                        "ser": error.sample_metrics.ser or 0.0,
                        "confidence": error.sample_metrics.confidence
                    }
                }
                for error in results.error_analysis
            ],
            "summary": {
                "total_samples": results.summary.total_samples,
                "successful_samples": results.summary.successful_samples,
                "failed_samples": results.summary.failed_samples,
                "average_confidence": results.metrics.confidence
            }
        }

        return legacy_result

    except Exception as e:
        logger.error(f"Error validating model: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/transcribe")
async def transcribe_audio():
    """Transcribe audio using trained model"""
    try:
        # This would implement audio transcription
        # For now, return a placeholder response
        return {"message": "Transcription functionality will be implemented"}

    except Exception as e:
        logger.error(f"Error transcribing audio: {e}")
        raise HTTPException(status_code=500, detail=str(e))
