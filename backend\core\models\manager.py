"""
Model Manager

Central management system for all AI models
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from .storage import ModelStorage
from .versioning import ModelVersioning
from .types import (
    ModelType, ModelMetadata, ModelInfo, ModelComparison, 
    CleanupConfig, CleanupResult, ModelStatus
)

logger = logging.getLogger(__name__)


class ModelManager:
    """Central model management system"""
    
    def __init__(self):
        self.storage = ModelStorage()
        self.versioning = ModelVersioning()
    
    async def register_model(
        self,
        model_type: ModelType,
        name: str,
        version: str,
        model_files: Dict[str, Any],
        training_config: Optional[Dict[str, Any]] = None,
        description: Optional[str] = None,
        tags: Optional[List[str]] = None
    ) -> ModelMetadata:
        """Register a new model"""
        try:
            # Generate model ID
            model_id = f"{model_type.value}_{name}_{version}_{int(datetime.now().timestamp())}"
            
            # Create metadata
            metadata = ModelMetadata(
                id=model_id,
                name=name,
                type=model_type,
                version=version,
                status=ModelStatus.TRAINED,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                tags=tags or [],
                description=description,
                training_config=training_config
            )
            
            # Save model
            local_path = await self.storage.save_model(
                model_type, model_id, model_files, metadata
            )
            
            # Register version
            await self.versioning.register_version(model_id, version, metadata)
            
            logger.info(f"Model {model_id} registered successfully")
            return metadata
            
        except Exception as e:
            logger.error(f"Error registering model: {e}")
            raise
    
    async def get_model(self, model_id: str) -> Optional[ModelMetadata]:
        """Get model metadata by ID"""
        try:
            # Extract model type from ID
            model_type_str = model_id.split('_')[0]
            model_type = ModelType(model_type_str)
            
            return await self.storage.load_metadata(model_type, model_id)
            
        except Exception as e:
            logger.error(f"Error getting model {model_id}: {e}")
            return None
    
    async def list_models(
        self, 
        model_type: Optional[ModelType] = None,
        status: Optional[ModelStatus] = None,
        tags: Optional[List[str]] = None,
        limit: Optional[int] = None
    ) -> List[ModelInfo]:
        """List models with optional filtering"""
        try:
            models = await self.storage.list_models(model_type)
            
            # Apply filters
            filtered_models = []
            for model in models:
                # Status filter
                if status and model.status != status:
                    continue
                
                # Tags filter
                if tags and not any(tag in model.tags for tag in tags):
                    continue
                
                # Convert to ModelInfo
                model_info = ModelInfo(
                    id=model.id,
                    name=model.name,
                    type=model.type,
                    version=model.version,
                    status=model.status,
                    created_at=model.created_at,
                    size_mb=model.size_bytes / (1024 * 1024) if model.size_bytes else None,
                    tags=model.tags,
                    metrics=model.metrics
                )
                filtered_models.append(model_info)
            
            # Apply limit
            if limit:
                filtered_models = filtered_models[:limit]
            
            return filtered_models
            
        except Exception as e:
            logger.error(f"Error listing models: {e}")
            raise
    
    async def compare_models(self, model_id1: str, model_id2: str) -> ModelComparison:
        """Compare two models"""
        try:
            model1 = await self.get_model(model_id1)
            model2 = await self.get_model(model_id2)
            
            if not model1 or not model2:
                raise ValueError("One or both models not found")
            
            # Convert to ModelInfo
            info1 = ModelInfo(
                id=model1.id,
                name=model1.name,
                type=model1.type,
                version=model1.version,
                status=model1.status,
                created_at=model1.created_at,
                size_mb=model1.size_bytes / (1024 * 1024) if model1.size_bytes else None,
                tags=model1.tags,
                metrics=model1.metrics
            )
            
            info2 = ModelInfo(
                id=model2.id,
                name=model2.name,
                type=model2.type,
                version=model2.version,
                status=model2.status,
                created_at=model2.created_at,
                size_mb=model2.size_bytes / (1024 * 1024) if model2.size_bytes else None,
                tags=model2.tags,
                metrics=model2.metrics
            )
            
            # Calculate comparison metrics
            comparison = {}
            summary_parts = []
            
            if model1.metrics and model2.metrics:
                for metric in model1.metrics:
                    if metric in model2.metrics:
                        diff = model2.metrics[metric] - model1.metrics[metric]
                        comparison[f"{metric}_diff"] = diff
                        
                        if metric in ['accuracy', 'confidence']:
                            if diff > 0:
                                summary_parts.append(f"{metric} improved by {diff:.3f}")
                            elif diff < 0:
                                summary_parts.append(f"{metric} decreased by {abs(diff):.3f}")
                        elif metric in ['wer', 'cer', 'ser']:
                            if diff < 0:
                                summary_parts.append(f"{metric} improved by {abs(diff):.3f}")
                            elif diff > 0:
                                summary_parts.append(f"{metric} worsened by {diff:.3f}")
            
            # Size comparison
            if model1.size_bytes and model2.size_bytes:
                size_diff_mb = (model2.size_bytes - model1.size_bytes) / (1024 * 1024)
                comparison["size_diff_mb"] = size_diff_mb
                if abs(size_diff_mb) > 1:
                    summary_parts.append(f"Size difference: {size_diff_mb:.1f}MB")
            
            summary = "; ".join(summary_parts) if summary_parts else "No significant differences found"
            
            # Generate recommendation
            recommendation = None
            if model1.metrics and model2.metrics:
                if 'accuracy' in model1.metrics and 'accuracy' in model2.metrics:
                    if model2.metrics['accuracy'] > model1.metrics['accuracy']:
                        recommendation = f"Model {model2.name} shows better accuracy"
                    elif model1.metrics['accuracy'] > model2.metrics['accuracy']:
                        recommendation = f"Model {model1.name} shows better accuracy"
            
            return ModelComparison(
                model1=info1,
                model2=info2,
                comparison=comparison,
                summary=summary,
                recommendation=recommendation
            )
            
        except Exception as e:
            logger.error(f"Error comparing models: {e}")
            raise
    
    async def tag_model(self, model_id: str, tags: List[str]) -> bool:
        """Add tags to a model"""
        try:
            # Extract model type from ID
            model_type_str = model_id.split('_')[0]
            model_type = ModelType(model_type_str)
            
            metadata = await self.storage.load_metadata(model_type, model_id)
            if not metadata:
                return False
            
            # Add new tags (avoid duplicates)
            existing_tags = set(metadata.tags)
            new_tags = set(tags)
            metadata.tags = list(existing_tags.union(new_tags))
            metadata.updated_at = datetime.now()
            
            await self.storage.save_metadata(model_type, model_id, metadata)
            
            logger.info(f"Tags added to model {model_id}: {tags}")
            return True
            
        except Exception as e:
            logger.error(f"Error tagging model {model_id}: {e}")
            raise
    
    async def cleanup_models(self, config: CleanupConfig) -> CleanupResult:
        """Clean up old models based on configuration"""
        try:
            cutoff_date = datetime.now() - timedelta(days=config.days_to_keep)
            
            # Get all models
            all_models = await self.storage.list_models()
            
            # Filter models to consider for deletion
            models_to_check = []
            for model in all_models:
                # Filter by model type if specified
                if config.model_types and model.type not in config.model_types:
                    continue
                
                # Skip if created after cutoff date
                if model.created_at > cutoff_date:
                    continue
                
                models_to_check.append(model)
            
            # Group models by name and type for keeping latest N
            model_groups = {}
            for model in models_to_check:
                key = f"{model.type.value}_{model.name}"
                if key not in model_groups:
                    model_groups[key] = []
                model_groups[key].append(model)
            
            # Sort each group by creation date (newest first)
            for key in model_groups:
                model_groups[key].sort(key=lambda x: x.created_at, reverse=True)
            
            deleted_models = []
            skipped_models = []
            skipped_reasons = {}
            freed_space = 0
            
            for model in models_to_check:
                model_key = f"{model.type.value}_{model.name}"
                group = model_groups[model_key]
                model_index = group.index(model)
                
                should_delete = True
                skip_reason = None
                
                # Check if should keep tagged models
                if config.keep_tagged and model.tags:
                    should_delete = False
                    skip_reason = "tagged"
                
                # Check if should keep deployed models
                elif config.keep_deployed and model.status == ModelStatus.DEPLOYED:
                    should_delete = False
                    skip_reason = "deployed"
                
                # Check if should keep latest N models
                elif model_index < config.keep_latest_n:
                    should_delete = False
                    skip_reason = "latest_n"
                
                if should_delete and not config.dry_run:
                    # Delete the model
                    success = await self.storage.delete_model(model.type, model.id)
                    if success:
                        deleted_models.append(model.id)
                        if model.size_bytes:
                            freed_space += model.size_bytes
                elif should_delete and config.dry_run:
                    # Dry run - just count what would be deleted
                    deleted_models.append(model.id)
                    if model.size_bytes:
                        freed_space += model.size_bytes
                else:
                    # Model was skipped
                    skipped_models.append(model.id)
                    if skip_reason:
                        if skip_reason not in skipped_reasons:
                            skipped_reasons[skip_reason] = []
                        skipped_reasons[skip_reason].append(model.id)
            
            result = CleanupResult(
                total_models=len(models_to_check),
                deleted_models=len(deleted_models),
                skipped_models=len(skipped_models),
                freed_space_mb=freed_space / (1024 * 1024),
                deleted_model_ids=deleted_models,
                skipped_reasons=skipped_reasons
            )
            
            logger.info(f"Model cleanup completed: {result.deleted_models} deleted, {result.skipped_models} skipped")
            return result
            
        except Exception as e:
            logger.error(f"Error during model cleanup: {e}")
            raise
