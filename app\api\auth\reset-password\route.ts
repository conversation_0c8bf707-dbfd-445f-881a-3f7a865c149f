import { NextResponse } from 'next/server';
import { auth, db } from '@/lib/firebase-admin';

export async function POST(request: Request) {
  try {
    const { email, ip_address, user_agent } = await request.json();
    console.log('Starting password reset process for:', email);

    if (!email) {
      console.error('Email is missing from request');
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Check if email exists in Firestore using admin SDK
    const usersRef = db.collection("users");
    const querySnapshot = await usersRef.where("email", "==", email).get();

    if (querySnapshot.empty) {
      console.log('Email not found in database:', email);
      return NextResponse.json(
        { error: 'Account not found with this email address' },
        { status: 404 }
      );
    }

    // Get the base URL from environment variable
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL;
    if (!baseUrl) {
      console.error('NEXT_PUBLIC_APP_URL is not configured');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Ensure the base URL is properly formatted
    const formattedBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
    const continueUrl = `${formattedBaseUrl}/auth/reset-password`;

    // Generate password reset link
    const link = await auth.generatePasswordResetLink(email, {
      url: continueUrl,
      handleCodeInApp: true,
    });
    console.log('Password reset link generated successfully');

    // Extract the oobCode from the generated link
    const url = new URL(link);
    const oobCode = url.searchParams.get('oobCode');
    
    // Create our custom reset password link
    const customLink = `${formattedBaseUrl}/auth/reset-password?oobCode=${oobCode}`;
    console.log('Custom reset password link generated:', customLink);

    // Format device and location information
    const deviceInfo = user_agent ? `Device: ${user_agent}` : '';
    const locationInfo = ip_address ? `Location: ${ip_address}` : '';
    const securityInfo = [deviceInfo, locationInfo].filter(Boolean).join('\n');

    // Send email using Brevo API
    console.log('Sending password reset email via Brevo API...');
    const response = await fetch('https://api.brevo.com/v3/smtp/email', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'api-key': process.env.BREVO_API_KEY || '',
      },
      body: JSON.stringify({
        sender: {
          name: 'Masalit Platform',
          email: '<EMAIL>',
        },
        to: [{ email }],
        subject: 'Reset your password',
        htmlContent: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <h1 style="color: #4F46E5; text-align: center;">Password Reset</h1>
            <p style="font-size: 16px; line-height: 1.5; color: #374151;">Click the button below to reset your password:</p>
            <div style="text-align: center; margin: 30px 0;">
              <a href="${customLink}" style="display: inline-block; padding: 12px 24px; background-color: #4F46E5; color: white; text-decoration: none; border-radius: 4px; font-weight: bold;">Reset Password</a>
            </div>
            ${securityInfo ? `
            <div style="background-color: #F3F4F6; padding: 15px; border-radius: 4px; margin: 20px 0;">
              <h3 style="color: #1F2937; margin-top: 0;">Security Information</h3>
              <p style="color: #4B5563; margin: 0; white-space: pre-line;">${securityInfo}</p>
            </div>
            ` : ''}
            <p style="font-size: 14px; color: #6B7280; text-align: center;">If you didn't request a password reset, you can safely ignore this email.</p>
            <hr style="border: none; border-top: 1px solid #E5E7EB; margin: 20px 0;">
            <p style="font-size: 12px; color: #9CA3AF; text-align: center;">This is an automated message, please do not reply to this email.</p>
          </div>
        `,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      console.error('Brevo API error:', {
        status: response.status,
        statusText: response.statusText,
        error: errorData,
        apiKey: process.env.BREVO_API_KEY ? 'Present' : 'Missing',
        email: email
      });
      throw new Error('Failed to send password reset email');
    }

    const responseData = await response.json();
    console.log('Password reset email sent successfully:', {
      messageId: responseData.messageId,
      email: email
    });

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error sending password reset email:', {
      error: error.message,
      stack: error.stack,
      email: error.email
    });
    
    // Return a more user-friendly error message
    const errorMessage = error.message === 'Account not found with this email address'
      ? 'Account not found with this email address'
      : 'Failed to send password reset email. Please try again.';
      
    return NextResponse.json(
      { error: errorMessage },
      { status: error.message === 'Account not found with this email address' ? 404 : 500 }
    );
  }
} 