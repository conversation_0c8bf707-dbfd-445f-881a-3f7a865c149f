import { NextRequest, NextResponse } from 'next/server'
import { collection, query, getDocs, doc, getDoc, limit as firestoreLimit, orderBy, startAfter, where } from 'firebase/firestore'
import { db } from '@/lib/firebase'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const trained_asr = searchParams.get('trained_asr')
    const tts_trained = searchParams.get('tts_trained')
    const action = searchParams.get('action')
    const limit = parseInt(searchParams.get('limit') || '50')
    const page = parseInt(searchParams.get('page') || '1')
    const sortBy = searchParams.get('sortBy') || 'created_at'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    console.log(`Fetching audio records: limit=${limit}, page=${page}, sortBy=${sortBy}`)

    // Build query without orderBy to avoid index requirements
    // We'll sort client-side and handle pagination after sorting
    const q = query(
      collection(db, 'audio'),
      firestoreLimit(limit * Math.max(page, 2)) // Get enough records for pagination
    )

    const querySnapshot = await getDocs(q)
    console.log(`Found ${querySnapshot.size} audio documents`)

    // Process documents in parallel batches
    const batchSize = 10
    const audioRecords = []
    const docs = querySnapshot.docs

    for (let i = 0; i < docs.length; i += batchSize) {
      const batch = docs.slice(i, i + batchSize)

      const batchPromises = batch.map(async (audioDoc) => {
        const audioData = audioDoc.data()
        const audioId = audioDoc.id

        try {
          // Get subcollections in parallel
          const [transcriptionDoc, metadataDoc, reviewDoc, trainingDoc] = await Promise.all([
            getDoc(doc(db, 'audio', audioId, 'transcriptions', 'primary')),
            getDoc(doc(db, 'audio', audioId, 'metadata', 'details')),
            getDoc(doc(db, 'audio', audioId, 'review', 'status')),
            getDoc(doc(db, 'audio', audioId, 'training', 'status'))
          ])

          const record = {
            id: audioId,
            ...audioData,
            transcriptions: transcriptionDoc.exists() ? {
              primary: transcriptionDoc.data()
            } : undefined,
            metadata: metadataDoc.exists() ? {
              details: metadataDoc.data()
            } : undefined,
            review: reviewDoc.exists() ? {
              status: reviewDoc.data()
            } : undefined,
            training: trainingDoc.exists() ? {
              status: trainingDoc.data()
            } : undefined
          }

          // Apply filters
          if (trained_asr !== null && record.training?.status?.trained_asr !== (trained_asr === 'true')) return null
          if (tts_trained !== null && record.training?.status?.tts_trained !== (tts_trained === 'true')) return null
          if (action && record.review?.status?.action !== action) return null

          return record
        } catch (error) {
          console.error(`Error processing audio ${audioId}:`, error)
          return null
        }
      })

      const batchResults = await Promise.all(batchPromises)
      audioRecords.push(...batchResults.filter(record => record !== null))
    }

    console.log(`Processed ${audioRecords.length} records after filtering`)

    // Sort client-side since we can't use orderBy without composite index
    audioRecords.sort((a, b) => {
      let aValue, bValue

      if (sortBy === 'created_at') {
        aValue = new Date(a.created_at || 0).getTime()
        bValue = new Date(b.created_at || 0).getTime()
      } else {
        aValue = a[sortBy] || ''
        bValue = b[sortBy] || ''
      }

      if (sortOrder === 'desc') {
        return bValue > aValue ? 1 : bValue < aValue ? -1 : 0
      } else {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
      }
    })

    // Apply pagination after sorting
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedRecords = audioRecords.slice(startIndex, endIndex)
    const hasMore = audioRecords.length > endIndex

    return NextResponse.json({
      success: true,
      audio_records: paginatedRecords,
      total: audioRecords.length,
      page,
      limit,
      hasMore
    })

  } catch (error) {
    console.error('Error fetching all audio records:', error)
    return NextResponse.json(
      { error: 'Failed to fetch audio records' },
      { status: 500 }
    )
  }
}
