import { TrainingStatus, TrainingSettings, TrainingSchedule } from "@/types/asr"
import { create } from 'zustand'
import { db } from './firebase'
import { collection, query, where, getDocs, orderBy, limit, onSnapshot, doc, setDoc, writeBatch, getDoc, getFirestore } from 'firebase/firestore'

const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:8000"

interface ValidationResults {
  wer: number
  cer: number
  ser: number
  confidence: number
  samples_tested: number
  error_analysis: Array<{
    audio_id: string
    reference: string
    hypothesis: string
    metrics: {
      wer: number
      cer: number
      ser: number
      confidence: number
    }
  }>
  summary: {
    total_samples: number
    successful_samples: number
    failed_samples: number
    average_confidence: number
  }
}

interface ASRStats {
  total_audio_files: number
  approved_audio_files: number
  trained_samples: number
  untrained_samples: number
  gender_distribution: {
    male: number
    female: number
    trained: {
      male: number
      female: number
    }
  }
  last_prediction?: {
    text: string
    confidence: number
    segments: Array<{
      text: string
      start: number
      end: number
      confidence: number
    }>
    language: string
    created_at: string
  }
  last_validation?: {
    accuracy: number
    confidence: number
    wer: number
    cer: number
    ser: number
    samples_tested: number
    created_at: string
  }
}

interface ASRState {
  status: TrainingStatus
  stats: ASRStats
  history: Array<{
    id: string
    type: 'training' | 'validation'
    timestamp: string
    model_version: string
    accuracy: number
    confidence: number
    wer?: number
    cer?: number
    ser?: number
    samples_tested?: number
  }>
  isLoading: boolean
  isTraining: boolean
  setStatus: (status: TrainingStatus) => void
  setStats: (stats: ASRStats) => void
  setHistory: (history: ASRState['history']) => void
  setIsLoading: (isLoading: boolean) => void
  setIsTraining: (isTraining: boolean) => void
}

export const useASR = create<ASRState>((set) => ({
  status: {
    status: 'not_started',
    current_epoch: 0,
    total_epochs: 0,
    progress: 0
  },
  stats: {
    total_audio_files: 0,
    approved_audio_files: 0,
    trained_samples: 0,
    untrained_samples: 0,
    gender_distribution: {
      male: 0,
      female: 0,
      trained: {
        male: 0,
        female: 0
      }
    }
  },
  history: [],
  isLoading: true,
  isTraining: false,
  setStatus: (status) => set((state) => ({ 
    status,
    isTraining: status.status === 'training',
    isLoading: false 
  })),
  setStats: (stats) => set({ stats }),
  setHistory: (history) => set({ history }),
  setIsLoading: (isLoading) => set({ isLoading }),
  setIsTraining: (isTraining) => set({ isTraining })
}))

export async function checkServerStatus(): Promise<boolean> {
  try {
    const response = await fetch(`${API_BASE_URL}/api/health`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    })
    return response.ok
  } catch (error) {
    return false
  }
}

export async function startTraining(settings?: any): Promise<{ status: string; message: string; settings: any }> {
  // Default training settings if none provided
  const defaultSettings = {
    epochs: 5,
    learning_rate: 0.001,
    number_of_samples: 100,
    model_name: "small",
    validation_split: 0.2,
    early_stopping_patience: 3,
    use_augmentation: false,
    eval_steps: 100,
    training_timeout: 7200  // 2 hours default timeout
  }

  const trainingSettings = settings || defaultSettings

  const response = await fetch(`${API_BASE_URL}/api/asr/training/start`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(trainingSettings)
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || 'Failed to start training')
  }

  return response.json()
}

export async function stopTraining(): Promise<{ status: string; message: string }> {
  const response = await fetch(`${API_BASE_URL}/api/asr/training/stop`, {
    method: 'POST',
  })
  
  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || 'Failed to stop training')
  }

  return response.json()
}

export async function resetTraining(): Promise<{ status: string }> {
  const response = await fetch(`${API_BASE_URL}/api/asr/training/reset`, {
    method: 'POST',
  })
  
  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || 'Failed to reset training')
  }

  return response.json()
}

export async function getTrainingStatus(): Promise<TrainingStatus> {
  const response = await fetch(`${API_BASE_URL}/api/asr/training/status`)

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || 'Failed to get training status')
  }

  return response.json()
}

export async function getTrainingSettings(model: string = 'default'): Promise<TrainingSettings> {
  try {
    const settingsRef = doc(db, 'training_settings', 'asr')
    const settingsDoc = await getDoc(settingsRef)
    
    if (!settingsDoc.exists()) {
      // Return defaults if no settings exist
      return {
        epochs: 2,
        learning_rate: 1e-5,
        number_of_samples: 5,
        model_name: 'small',
        validation_split: 0.2,
        early_stopping_patience: 3,
        use_augmentation: false,
        eval_steps: 500,
        training_timeout: 7200  // 2 hours default
      }
    }

    return settingsDoc.data() as TrainingSettings
  } catch (error) {
    console.error('Error getting training settings:', error)
    throw error
  }
}

export async function updateTrainingSettings(
  settings: TrainingSettings,
  model: string = 'default'
): Promise<TrainingSettings> {
  try {
    // Use backend API which now uses clean Firebase structure
    const response = await fetch(`${API_BASE_URL}/api/asr/training/settings`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(settings),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.detail || 'Failed to update training settings')
    }

    const result = await response.json()
    return result.settings
  } catch (error) {
    console.error('Error updating training settings:', error)
    throw error
  }
}

export async function getTrainingStats(model: string = 'default'): Promise<any> {
  const response = await fetch(`${API_BASE_URL}/api/asr/data/stats`)

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || 'Failed to get training stats')
  }

  return response.json()
}

export async function getTrainingHistory(model: string = 'default'): Promise<any[]> {
  const response = await fetch(`${API_BASE_URL}/api/asr/training/${model}/history`)
  
  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || 'Failed to get training history')
  }

  return response.json()
}

export async function rollbackModel(version: string): Promise<void> {
  const response = await fetch(`${API_BASE_URL}/api/asr/models/rollback/${version}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || 'Failed to rollback model')
  }
}

export async function validateTraining(): Promise<ValidationResults> {
  const response = await fetch(`${API_BASE_URL}/api/asr/validate`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      validation_type: 'full',
      config: {
        max_samples: 10
      }
    })
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.detail || 'Failed to validate training')
  }

  return response.json()
}

export async function scheduleTraining(schedule: TrainingSchedule): Promise<void> {
  const response = await fetch(`${API_BASE_URL}/api/asr/training/schedule`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(schedule),
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || 'Failed to schedule training')
  }
}

export async function getTrainingSchedule(model: string = 'default'): Promise<TrainingSchedule> {
  const response = await fetch(`${API_BASE_URL}/api/asr/training/${model}/schedule`)
  
  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || 'Failed to get training schedule')
  }

  return response.json()
}

export async function resetTrainingSettings(): Promise<void> {
  const response = await fetch(`${API_BASE_URL}/api/asr/training/settings/reset`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || 'Failed to reset training settings')
  }
}

interface ModelStatus {
  active: boolean
  last_training: string
  accuracy: string
}

interface SystemMetrics {
  system: {
    cpu_usage: number
    memory_usage: number
    gpu_usage: number
    uptime: number
  }
  models: {
    asr: ModelStatus
    tts: ModelStatus
  }
}

export async function getSystemMetrics(): Promise<SystemMetrics> {
  const response = await fetch(`${API_BASE_URL}/api/asr/status`)
  
  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || 'Failed to get system metrics')
  }

  return response.json()
}

interface ValidationSettings {
  validation_type: 'full' | 'quick' | 'custom';
  max_samples?: number;
  min_confidence?: number;
  model?: string;
  last_updated: string;
}

const DEFAULT_CUSTOM_SETTINGS = {
  max_samples: 10,
  min_confidence: 0.0
};

export async function getValidationSettings(): Promise<ValidationSettings> {
  try {
    const db = getFirestore();
    const docRef = doc(db, 'asr_settings', 'validation');
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      const data = docSnap.data() as ValidationSettings;
      // If it's custom validation but missing default values, add them
      if (data.validation_type === 'custom') {
        return {
          ...data,
          max_samples: data.max_samples ?? DEFAULT_CUSTOM_SETTINGS.max_samples,
          min_confidence: data.min_confidence ?? DEFAULT_CUSTOM_SETTINGS.min_confidence
        };
      }
      return data;
    }
    
    // Return default settings if none exist
    return {
      validation_type: 'full',
      last_updated: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error getting validation settings:', error);
    throw error;
  }
}

export async function saveValidationSettings(settings: {
  validation_type: 'full' | 'quick' | 'custom';
  max_samples?: number;
  min_confidence?: number;
  model?: string;
}): Promise<ValidationSettings> {
  try {
    const db = getFirestore();
    const docRef = doc(db, 'asr_settings', 'validation');

    // Create new settings object based on validation type
    const newSettings: ValidationSettings = {
      validation_type: settings.validation_type,
      last_updated: new Date().toISOString()
    };

    // Only include custom settings if validation type is custom
    if (settings.validation_type === 'custom') {
      newSettings.max_samples = settings.max_samples ?? DEFAULT_CUSTOM_SETTINGS.max_samples;
      newSettings.min_confidence = settings.min_confidence ?? DEFAULT_CUSTOM_SETTINGS.min_confidence;
      newSettings.model = settings.model;
    }

    // Save new settings, overwriting old ones
    await setDoc(docRef, newSettings);
    return newSettings;
  } catch (error) {
    console.error('Error saving validation settings:', error);
    throw error;
  }
}

export async function validateModel(request: {
  validation_type: ValidationType;
  config?: ValidationConfig;
}): Promise<ValidationResponse> {
  try {
    const response = await fetch('/api/asr/validate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Validation failed');
    }

    return response.json();
  } catch (error) {
    console.error('Error validating model:', error);
    throw error;
  }
}

export async function getModels(): Promise<string[]> {
  try {
    console.log('🔍 Fetching models from API...');

    // Try the unified models API first (with trailing slash to avoid redirect)
    const response = await fetch(`${API_BASE_URL}/api/models/?model_type=asr`);
    console.log('📡 API Response status:', response.status, response.statusText);

    if (response.ok) {
      const models = await response.json();
      console.log('✅ Raw API response:', models);
      console.log('📊 Models array length:', models.length);

      if (models && models.length > 0) {
        const modelIds = models.map((model: any) => model.id || model.model_id);
        console.log('🎯 Extracted model IDs:', modelIds);
        return modelIds;
      }
    } else {
      console.warn('⚠️ Unified API request failed:', response.status, response.statusText);
    }

    // Fallback to ASR-specific endpoint
    console.log('🔄 Trying fallback ASR endpoint...');
    const fallbackResponse = await fetch(`${API_BASE_URL}/api/asr/models/list`);
    if (fallbackResponse.ok) {
      const data = await fallbackResponse.json();
      console.log('✅ Fallback API response:', data);
      if (data.models && data.models.length > 0) {
        // Extract model IDs from the models array
        const modelIds = data.models.map((model: any) => model.model_id || model.id);
        console.log('✅ Models fetched from fallback API:', modelIds);
        return modelIds;
      }
    } else {
      console.warn('⚠️ ASR API request failed:', fallbackResponse.status, fallbackResponse.statusText);
    }

    // No fallback to hardcoded models - return empty array to show "no models available"
    console.warn('⚠️ No models found from APIs, no trained models available');
    return [];

  } catch (error) {
    console.error('❌ Error fetching models:', error);
    // Return empty array instead of fallback models to show proper "no models" state
    console.log('🔄 Returning empty array due to error - no trained models available');
    return [];
  }
}

// Model Management Types
export interface Model {
  id: string;
  version: string;
  created_at: string;
  tags?: string[];
  metrics?: {
    accuracy: number;
    wer: number;
    cer: number;
    confidence: number;
  };
}

export interface ModelComparison {
  model1: Model;
  model2: Model;
  comparison: {
    accuracy_diff: number;
    wer_diff: number;
    cer_diff: number;
    confidence_diff: number;
  };
}

// Model Management Functions
export async function listModels(): Promise<Model[]> {
  try {
    // Try the unified models API first (with trailing slash to avoid redirect)
    const response = await fetch(`${API_BASE_URL}/api/models/?model_type=asr`);
    if (response.ok) {
      const models = await response.json();
      return models.map((model: any) => ({
        id: model.id || model.model_id,
        version: model.version || '1.0.0',
        created_at: model.created_at || new Date().toISOString(),
        tags: model.tags || [],
        metrics: model.metrics || {
          accuracy: 0.85,
          wer: 0.15,
          cer: 0.08,
          confidence: 0.90
        }
      }));
    }

    // No fallback to mock data - return empty array if API is not available
    console.warn('Models API not available, no trained models found');
    return [];
  } catch (error) {
    console.error('Error listing models:', error);
    // Return empty array instead of throwing error to show "no models" state
    return [];
  }
}

export async function getModelDetails(modelId: string): Promise<Model> {
  try {
    const response = await fetch(`${API_BASE_URL}/api/models/${modelId}`);
    if (response.ok) {
      const model = await response.json();
      return {
        id: model.id || model.model_id,
        version: model.version || '1.0.0',
        created_at: model.created_at || new Date().toISOString(),
        tags: model.tags || [],
        metrics: model.metrics || {
          accuracy: 0.85,
          wer: 0.15,
          cer: 0.08,
          confidence: 0.90
        }
      };
    }
    throw new Error('Failed to get model details');
  } catch (error) {
    console.error('Error getting model details:', error);
    throw error;
  }
}

export async function compareModels(model1Id: string, model2Id: string): Promise<ModelComparison> {
  try {
    const response = await fetch(`${API_BASE_URL}/api/models/compare`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ model_id1: model1Id, model_id2: model2Id }),
    });
    if (response.ok) {
      return response.json();
    }

    // Fallback to mock comparison
    const models = await listModels();
    const model1 = models.find(m => m.id === model1Id);
    const model2 = models.find(m => m.id === model2Id);

    if (!model1 || !model2) {
      throw new Error('Models not found for comparison');
    }

    return {
      model1,
      model2,
      comparison: {
        accuracy_diff: (model1.metrics?.accuracy || 0) - (model2.metrics?.accuracy || 0),
        wer_diff: (model1.metrics?.wer || 0) - (model2.metrics?.wer || 0),
        cer_diff: (model1.metrics?.cer || 0) - (model2.metrics?.cer || 0),
        confidence_diff: (model1.metrics?.confidence || 0) - (model2.metrics?.confidence || 0),
      }
    };
  } catch (error) {
    console.error('Error comparing models:', error);
    throw error;
  }
}

export async function tagModel(modelId: string, tags: string[]): Promise<void> {
  try {
    const response = await fetch(`${API_BASE_URL}/api/models/tag`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ model_id: modelId, tags }),
    });
    if (!response.ok) {
      throw new Error('Failed to tag model');
    }
  } catch (error) {
    console.error('Error tagging model:', error);
    throw error;
  }
}

export async function cleanupModels(daysToKeep: number, keepTagged: boolean = true): Promise<{ deleted: number; skipped: number; modelsToKeep: Model[] }> {
  const response = await fetch('/api/asr/models/cleanup', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ days_to_keep: daysToKeep, keep_tagged: keepTagged }),
  });
  if (!response.ok) {
    throw new Error('Failed to clean up models');
  }
  return response.json();
}

export async function deleteTrainingData(audioId: string): Promise<void> {
  const response = await fetch(`${API_BASE_URL}/asr/training/${audioId}`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
    },
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.detail || "Failed to delete training data")
  }
}

export async function undoLastTraining(): Promise<void> {
  const response = await fetch(`${API_BASE_URL}/asr/train/undo`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.detail || "Failed to undo last training")
  }
}

export async function getModelVersions(): Promise<any[]> {
  const response = await fetch(`${API_BASE_URL}/api/asr/models`)
  
  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || 'Failed to get model versions')
  }

  return response.json()
}

export async function updateTrainingSchedule(
  schedule: TrainingSchedule,
  model: string = 'default'
): Promise<TrainingSchedule> {
  const response = await fetch(`${API_BASE_URL}/api/asr/training/${model}/schedule`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(schedule),
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || 'Failed to update training schedule')
  }

  return response.json()
}

// Add these type definitions at the top with other interfaces
type ValidationType = 'full' | 'quick' | 'custom';

interface ValidationConfig {
  max_samples?: number;
  min_confidence?: number;
  model?: string;
}

type ValidationResponse = ValidationResults; 