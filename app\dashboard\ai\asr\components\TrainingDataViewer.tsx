"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { RefreshCw, Database, FileAudio, Clock, User, Volume2 } from "lucide-react"
import { collection, query, getDocs, doc, getDoc } from "firebase/firestore"
import { db } from "@/lib/firebase"

interface TrainingDataItem {
  audio_id: string
  title?: string
  duration?: number
  user_id?: string
  username?: string
  transcription?: string
  language?: string
  dialect?: string
  gender?: string
  recording_context?: string
  created_at?: string
  audio_url?: string
  trained_asr?: boolean
}

interface TrainingDataViewerProps {
  isServerAvailable: boolean
}

export function TrainingDataViewer({ isServerAvailable }: TrainingDataViewerProps) {
  const [trainingData, setTrainingData] = useState<TrainingDataItem[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [stats, setStats] = useState({
    total: 0,
    approved: 0,
    untrained: 0,
    totalDuration: 0
  })

  const loadTrainingData = async () => {
    setIsLoading(true)
    try {
      console.log('🔄 Loading training data from hierarchical Firestore structure...')

      // Get all audio documents
      const audioQuery = query(collection(db, 'audio'))
      const audioSnapshot = await getDocs(audioQuery)
      
      const trainingItems: TrainingDataItem[] = []
      let approvedCount = 0
      let untrainedCount = 0
      let totalDuration = 0

      for (const audioDoc of audioSnapshot.docs) {
        const audioId = audioDoc.id
        const audioData = audioDoc.data()
        
        try {
          // Check review status from subcollection
          const reviewStatusRef = doc(db, 'audio', audioId, 'review', 'status')
          const reviewStatusDoc = await getDoc(reviewStatusRef)
          
          if (!reviewStatusDoc.exists()) {
            continue // Skip if no review status
          }
          
          const reviewData = reviewStatusDoc.data()
          if (reviewData.action !== 'approved') {
            continue // Only include approved audio
          }
          
          approvedCount++

          // Get transcription from subcollection
          const transcriptionRef = doc(db, 'audio', audioId, 'transcriptions', 'primary')
          const transcriptionDoc = await getDoc(transcriptionRef)
          
          if (!transcriptionDoc.exists()) {
            continue // Skip if no transcription
          }
          
          const transcriptionData = transcriptionDoc.data()
          if (!transcriptionData.content?.trim()) {
            continue // Skip if empty transcription
          }

          // Get metadata from subcollection
          const metadataRef = doc(db, 'audio', audioId, 'metadata', 'details')
          const metadataDoc = await getDoc(metadataRef)
          const metadataData = metadataDoc.exists() ? metadataDoc.data() : {}

          // Get training status from subcollection
          const trainingRef = doc(db, 'audio', audioId, 'training', 'status')
          const trainingDoc = await getDoc(trainingRef)
          const trainingData = trainingDoc.exists() ? trainingDoc.data() : {}
          
          const isTrainedASR = trainingData.trained_asr === true
          if (!isTrainedASR) {
            untrainedCount++
          }

          // Get user info if available
          let username = 'Unknown User'
          if (audioData.user_id) {
            try {
              const userDoc = await getDoc(doc(db, 'users', audioData.user_id))
              if (userDoc.exists()) {
                const userData = userDoc.data()
                username = userData.username || userData.email || userData.name || 'Unknown User'
              }
            } catch (error) {
              console.warn(`Error getting user data for ${audioData.user_id}:`, error)
            }
          }

          const duration = audioData.duration || 0
          totalDuration += duration

          const trainingItem: TrainingDataItem = {
            audio_id: audioId,
            title: metadataData.title || audioData.title || `Audio ${audioId.slice(-6)}`,
            duration: duration,
            user_id: audioData.user_id,
            username: username,
            transcription: transcriptionData.content,
            language: transcriptionData.language || 'masalit',
            dialect: metadataData.dialect,
            gender: metadataData.gender,
            recording_context: metadataData.recording_context,
            created_at: audioData.created_at,
            audio_url: audioData.audio_url,
            trained_asr: isTrainedASR
          }

          trainingItems.push(trainingItem)

        } catch (error) {
          console.warn(`Error processing audio ${audioId}:`, error)
        }
      }

      // Sort by creation date (newest first)
      trainingItems.sort((a, b) => {
        const dateA = new Date(a.created_at || 0).getTime()
        const dateB = new Date(b.created_at || 0).getTime()
        return dateB - dateA
      })

      setTrainingData(trainingItems)
      setStats({
        total: audioSnapshot.size,
        approved: approvedCount,
        untrained: untrainedCount,
        totalDuration: totalDuration
      })

      console.log('✅ Training data loaded:', {
        total: audioSnapshot.size,
        approved: approvedCount,
        untrained: untrainedCount,
        samples: trainingItems.length
      })

    } catch (error) {
      console.error('❌ Error loading training data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    loadTrainingData()
  }, [])

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString()
    } catch {
      return 'Unknown'
    }
  }

  return (
    <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="rounded-full bg-green-100 p-2">
              <Database className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <CardTitle className="text-xl">Training Data</CardTitle>
              <CardDescription>Approved audio samples ready for ASR training</CardDescription>
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={loadTrainingData}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
            <div className="text-sm text-blue-600">Total Audio</div>
          </div>
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{stats.approved}</div>
            <div className="text-sm text-green-600">Approved</div>
          </div>
          <div className="text-center p-3 bg-orange-50 rounded-lg">
            <div className="text-2xl font-bold text-orange-600">{stats.untrained}</div>
            <div className="text-sm text-orange-600">Untrained</div>
          </div>
          <div className="text-center p-3 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">{formatDuration(stats.totalDuration)}</div>
            <div className="text-sm text-purple-600">Total Duration</div>
          </div>
        </div>

        <Separator />

        {/* Training Data List */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h4 className="font-medium">Available Training Samples ({trainingData.length})</h4>
            {trainingData.length > 0 && (
              <Badge variant="secondary">
                {stats.untrained} untrained
              </Badge>
            )}
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : trainingData.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <FileAudio className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No approved training data found</p>
              <p className="text-sm">Upload and approve some audio files to see training data</p>
            </div>
          ) : (
            <div className="max-h-[400px] overflow-y-auto">
              <div className="space-y-2">
                {trainingData.map((item) => (
                  <div
                    key={item.audio_id}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h5 className="font-medium truncate">{item.title}</h5>
                        {item.trained_asr ? (
                          <Badge variant="secondary" className="bg-green-100 text-green-700">
                            Trained
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="bg-orange-100 text-orange-700">
                            Untrained
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <div className="flex items-center space-x-1">
                          <User className="h-3 w-3" />
                          <span>{item.username}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Clock className="h-3 w-3" />
                          <span>{formatDuration(item.duration || 0)}</span>
                        </div>
                        {item.gender && (
                          <div className="flex items-center space-x-1">
                            <Volume2 className="h-3 w-3" />
                            <span>{item.gender}</span>
                          </div>
                        )}
                      </div>
                      {item.transcription && (
                        <p className="text-sm text-gray-600 mt-1 truncate">
                          "{item.transcription.slice(0, 100)}{item.transcription.length > 100 ? '...' : ''}"
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
