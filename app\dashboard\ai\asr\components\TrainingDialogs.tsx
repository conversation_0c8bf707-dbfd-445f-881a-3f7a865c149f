"use client"

import { useState } from "react"
import { TrainingStatus, TrainingSettings } from "@/types/asr"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { useToast } from "@/hooks/use-toast"
import { ModelManagement } from "./ModelManagement"

import { doc, setDoc, writeBatch, collection, query, where, getDocs, getDoc } from "firebase/firestore"
import { db } from "@/lib/firebase"

interface TrainingDialogsProps {
  status: TrainingStatus
  settings: TrainingSettings
  onSettingsChange: (settings: TrainingSettings) => void
  isAdvancedSettingsOpen: boolean
  setIsAdvancedSettingsOpen: (open: boolean) => void
  isResetConfirmOpen: boolean
  setIsResetConfirmOpen: (open: boolean) => void
  isModelManagementOpen: boolean
  setIsModelManagementOpen: (open: boolean) => void
  isModelCompareOpen: boolean
  setIsModelCompareOpen: (open: boolean) => void
  isScheduleOpen: boolean
  setIsScheduleOpen: (open: boolean) => void
  onStatusChange?: (status: TrainingStatus) => void
  onTrainingDataRefresh?: () => void
}

export function TrainingDialogs({
  status,
  settings,
  onSettingsChange,
  isAdvancedSettingsOpen,
  setIsAdvancedSettingsOpen,
  isResetConfirmOpen,
  setIsResetConfirmOpen,
  isModelManagementOpen,
  setIsModelManagementOpen,
  isModelCompareOpen,
  setIsModelCompareOpen,
  isScheduleOpen,
  setIsScheduleOpen,
  onStatusChange,
  onTrainingDataRefresh
}: TrainingDialogsProps) {
  const { toast } = useToast()
  const [localSettings, setLocalSettings] = useState(settings)
  const [schedule, setSchedule] = useState({
    enabled: false,
    interval: 'weekly' as 'daily' | 'weekly' | 'monthly',
    time: '02:00'
  })

  const handleSaveSettings = () => {
    onSettingsChange(localSettings)
    setIsAdvancedSettingsOpen(false)
    toast({
      title: "Settings saved",
      description: "Advanced training settings have been updated.",
    })
  }

  const handleResetTraining = async () => {
    try {
      // 1. Try to stop any ongoing training first (optional, don't fail if it errors)
      try {
        await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:8000"}/api/asr/training/stop`, {
          method: 'POST',
        })
      } catch (error) {
        console.log("No active training to stop or backend unavailable")
      }

      // 2. Reset status in Firestore
      const statusRef = doc(db, 'training_status', 'asr')
      await setDoc(statusRef, {
        status: "not_started",
        current_epoch: 0,
        total_epochs: 0,
        samples_processed: 0,
        total_samples: 0,
        current_accuracy: 0.0,
        current_confidence: 0.0,
        current_wer: 0.0,
        current_cer: 0.0,
        current_ser: 0.0,
        training_progress: 0.0,
        progress: 0,
        error: null,
        model_version: "",
        start_time: "",
        end_time: "",
        updated_at: new Date().toISOString()
      })

      // 3. Reset audio samples to untrained status using hierarchical structure
      const batch = writeBatch(db)

      // Get all audio documents and check their training status
      const audioRef = collection(db, 'audio')
      const audioSnapshot = await getDocs(audioRef)

      for (const audioDoc of audioSnapshot.docs) {
        const audioId = audioDoc.id

        // Check training status from subcollection
        const trainingStatusRef = doc(db, 'audio', audioId, 'training', 'status')
        const trainingStatusDoc = await getDoc(trainingStatusRef)

        if (trainingStatusDoc.exists()) {
          const trainingData = trainingStatusDoc.data()

          // Reset if currently trained for ASR
          if (trainingData.trained_asr === true) {
            batch.update(trainingStatusRef, {
              trained_asr: false,
              updated_at: new Date().toISOString()
            })
          }
        }
      }

      // Commit the batch update
      await batch.commit()

      // 4. Update local status if callback provided
      if (onStatusChange) {
        onStatusChange({
          status: "not_started",
          current_epoch: 0,
          total_epochs: 0,
          progress: 0,
          current_accuracy: 0,
          current_confidence: 0,
          current_wer: 0,
          current_cer: 0,
          current_ser: 0,
          samples_processed: 0,
          total_samples: 0,
          training_progress: 0,
          error: null,
          model_version: "",
          start_time: "",
          end_time: ""
        })
      }

      toast({
        title: "Training reset",
        description: "All training progress has been reset. Audio data is now available for retraining.",
      })
      setIsResetConfirmOpen(false)

      // Refresh training data viewer
      if (onTrainingDataRefresh) {
        onTrainingDataRefresh()
      }
    } catch (error) {
      console.error("Error resetting training:", error)
      toast({
        title: "Error",
        description: "Failed to reset training. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleScheduleUpdate = async () => {
    try {
      const scheduleRef = doc(db, 'training_schedules', 'asr')
      await setDoc(scheduleRef, {
        enabled: schedule.enabled,
        interval: schedule.interval,
        time: schedule.time,
        updated_at: new Date().toISOString()
      })

      toast({
        title: "Schedule updated",
        description: "ASR training schedule has been updated successfully.",
      })
      setIsScheduleOpen(false)
    } catch (error) {
      console.error("Error updating schedule:", error)
      toast({
        title: "Error",
        description: "Failed to update schedule. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleNumericInput = (value: string, field: keyof TrainingSettings) => {
    const numValue = parseFloat(value)
    if (!isNaN(numValue)) {
      setLocalSettings(prev => ({ ...prev, [field]: numValue }))
    }
  }

  return (
    <>
      {/* Advanced Settings Dialog */}
      <Dialog open={isAdvancedSettingsOpen} onOpenChange={setIsAdvancedSettingsOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Advanced Training Settings</DialogTitle>
            <DialogDescription>
              Configure advanced parameters for ASR model training. These settings will be saved and persist across sessions.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-6 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="epochs">Epochs</Label>
                <Input
                  id="epochs"
                  type="number"
                  min="1"
                  max="100"
                  value={localSettings.epochs}
                  onChange={(e) => handleNumericInput(e.target.value, 'epochs')}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="learning_rate">Learning Rate</Label>
                <Input
                  id="learning_rate"
                  type="number"
                  step="0.0001"
                  min="0.0001"
                  max="1"
                  value={localSettings.learning_rate}
                  onChange={(e) => handleNumericInput(e.target.value, 'learning_rate')}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="model_name">Model Size</Label>
                <Select
                  value={localSettings.model_name}
                  onValueChange={(value) => setLocalSettings(prev => ({ ...prev, model_name: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="tiny">Tiny</SelectItem>
                    <SelectItem value="base">Base</SelectItem>
                    <SelectItem value="small">Small</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="large">Large</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="validation_split">Validation Split</Label>
                <Input
                  id="validation_split"
                  type="number"
                  step="0.1"
                  min="0.1"
                  max="0.5"
                  value={localSettings.validation_split}
                  onChange={(e) => handleNumericInput(e.target.value, 'validation_split')}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="early_stopping_patience">Early Stopping Patience</Label>
                <Input
                  id="early_stopping_patience"
                  type="number"
                  min="1"
                  max="20"
                  value={localSettings.early_stopping_patience}
                  onChange={(e) => handleNumericInput(e.target.value, 'early_stopping_patience')}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="eval_steps">Evaluation Steps</Label>
                <Input
                  id="eval_steps"
                  type="number"
                  min="10"
                  max="1000"
                  value={localSettings.eval_steps}
                  onChange={(e) => handleNumericInput(e.target.value, 'eval_steps')}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="number_of_samples">Number of Samples</Label>
                <Input
                  id="number_of_samples"
                  type="number"
                  min="1"
                  max="10000"
                  value={localSettings.number_of_samples}
                  onChange={(e) => handleNumericInput(e.target.value, 'number_of_samples')}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="training_timeout">Training Timeout (seconds)</Label>
                <Input
                  id="training_timeout"
                  type="number"
                  min="300"
                  max="86400"
                  value={localSettings.training_timeout}
                  onChange={(e) => handleNumericInput(e.target.value, 'training_timeout')}
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="use_augmentation"
                checked={localSettings.use_augmentation}
                onCheckedChange={(checked) => setLocalSettings(prev => ({ ...prev, use_augmentation: checked }))}
              />
              <Label htmlFor="use_augmentation">Use Data Augmentation</Label>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAdvancedSettingsOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveSettings}>
              Save Settings
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reset Training Confirmation Dialog */}
      <AlertDialog open={isResetConfirmOpen} onOpenChange={setIsResetConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Reset Training</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to reset the training? This will stop any ongoing training and clear the current progress. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleResetTraining} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              Reset Training
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Model Management Dialog */}
      <Dialog open={isModelManagementOpen} onOpenChange={setIsModelManagementOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Model Management</DialogTitle>
            <DialogDescription>
              Manage your ASR models, view details, and perform model operations.
            </DialogDescription>
          </DialogHeader>
          <ModelManagement />
        </DialogContent>
      </Dialog>

      {/* Model Compare Dialog */}
      <Dialog open={isModelCompareOpen} onOpenChange={setIsModelCompareOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Model Comparison</DialogTitle>
            <DialogDescription>
              Compare performance metrics between different ASR models.
            </DialogDescription>
          </DialogHeader>
          <ModelManagement showComparisonOnly={true} />
        </DialogContent>
      </Dialog>

      {/* Schedule Training Dialog */}
      <Dialog open={isScheduleOpen} onOpenChange={setIsScheduleOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>ASR Training Schedule</DialogTitle>
            <DialogDescription>
              Configure automatic training schedule for ASR models.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="schedule-enabled"
                checked={schedule.enabled}
                onCheckedChange={(checked) => setSchedule({ ...schedule, enabled: checked })}
              />
              <Label htmlFor="schedule-enabled">Enable automatic training</Label>
            </div>

            {schedule.enabled && (
              <>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="interval" className="text-right">
                    Interval
                  </Label>
                  <Select
                    value={schedule.interval}
                    onValueChange={(value: 'daily' | 'weekly' | 'monthly') => setSchedule({ ...schedule, interval: value })}
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select interval" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="time" className="text-right">
                    Time
                  </Label>
                  <Input
                    id="time"
                    type="time"
                    value={schedule.time}
                    onChange={(e) => setSchedule({ ...schedule, time: e.target.value })}
                    className="col-span-3"
                  />
                </div>
              </>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsScheduleOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleScheduleUpdate}>
              Save Schedule
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
