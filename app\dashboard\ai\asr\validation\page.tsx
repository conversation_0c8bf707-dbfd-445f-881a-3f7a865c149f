'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { validateModel, getModels, getValidationSettings, saveValidationSettings } from '@/lib/asr';
import { ValidationType, ValidationConfig, ValidationResponse } from '@/types/asr';
import { Loader2, AlertCircle, CheckCircle2, XCircle, Settings, BarChart2, ArrowLeft, Play, Square, RotateCcw, History, Trash2, Eye, Download, Calendar, Filter, Search, TrendingUp, Zap, Clock, Users, FileText, RefreshCw, ExternalLink } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useToast } from '@/hooks/use-toast';
import { db } from '@/lib/firebase';
import { doc, onSnapshot, collection, query, orderBy, deleteDoc, addDoc, serverTimestamp, setDoc, getDoc } from 'firebase/firestore';

export default function ValidationPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [validationType, setValidationType] = useState<ValidationType>('quick');
  const [config, setConfig] = useState<ValidationConfig>({
    max_samples: 20,
    min_confidence: 0.0
  });
  const [availableModels, setAvailableModels] = useState<string[]>([]);
  const [selectedModel, setSelectedModel] = useState<string>('');
  const [validationResults, setValidationResults] = useState<ValidationResponse | null>(null);
  const [showSettings, setShowSettings] = useState(false);
  const [progress, setProgress] = useState(0);
  const [isStopConfirmOpen, setIsStopConfirmOpen] = useState(false);
  const [validationSettings, setValidationSettings] = useState<ValidationConfig>({
    max_samples: 20,
    min_confidence: 0.0
  });

  // New state for validation history and management
  const [validationHistory, setValidationHistory] = useState<any[]>([]);
  const [showHistory, setShowHistory] = useState(false);
  const [showResultDetails, setShowResultDetails] = useState(false);
  const [selectedResult, setSelectedResult] = useState<any>(null);
  const [currentValidationId, setCurrentValidationId] = useState<string | null>(null);

  // Enhanced features state
  const [showBatchValidation, setShowBatchValidation] = useState(false);
  const [showScheduler, setShowScheduler] = useState(false);
  const [scheduleEnabled, setScheduleEnabled] = useState(false);
  const [scheduleType, setScheduleType] = useState('daily');
  const [selectedModelsForBatch, setSelectedModelsForBatch] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterType, setFilterType] = useState<string>('all');
  const [validationStats, setValidationStats] = useState<any>(null);
  const [isExporting, setIsExporting] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [backendStatus, setBackendStatus] = useState<'checking' | 'healthy' | 'error' | 'unavailable'>('checking');

  useEffect(() => {
    const initializePage = async () => {
      // Test Firestore connection first
      const firestoreConnected = await testFirestoreConnection();
      if (firestoreConnected) {
        console.log('🔥 Firestore initialized successfully');
      }

      // Load data
      loadModels();
      loadValidationSettings();
      loadValidationHistory();
      checkBackendHealth();
    };

    initializePage();
  }, []);

  const checkBackendHealth = async () => {
    try {
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000';
      const response = await fetch(`${backendUrl}/api/health`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000) // 5 second timeout
      });

      if (response.ok) {
        console.log('✅ Backend is healthy and accessible');
        setBackendStatus('healthy');
      } else {
        console.warn('⚠️ Backend is accessible but returned error:', response.status);
        setBackendStatus('error');
      }
    } catch (error) {
      console.warn('❌ Backend health check failed:', error);
      console.log('💡 Validation will use mock data for demonstration');
      setBackendStatus('unavailable');
    }
  };

  const testFirestoreConnection = async () => {
    try {
      // Test Firestore connectivity by trying to read a document
      const testDoc = await getDoc(doc(db, 'validation_settings', 'asr_validation_config'));
      console.log('✅ Firestore connection successful');
      return true;
    } catch (error) {
      console.error('❌ Firestore connection failed:', error);
      return false;
    }
  };

  const loadModels = async () => {
    try {
      console.log('🔄 Loading models for validation page...');
      const models = await getModels();
      console.log('📋 Models received:', models);

      setAvailableModels(models);
      if (models.length > 0) {
        setSelectedModel(models[0]);
        console.log('✅ Selected default model:', models[0]);
      } else {
        console.warn('⚠️ No models available');
      }
    } catch (err) {
      console.error('❌ Failed to load models:', err);
      setError('Failed to load models');

      // No fallback models - show empty state
      setAvailableModels([]);
      setSelectedModel('');
      console.log('⚠️ No models available - user needs to train models first');
    }
  };

  const loadValidationSettings = async () => {
    try {
      // Load validation settings from Firestore
      const settingsDoc = await getDoc(doc(db, 'validation_settings', 'asr_validation_config'));

      if (settingsDoc.exists()) {
        const settings = settingsDoc.data();
        const validationConfig = {
          max_samples: settings.max_samples || 20,
          min_confidence: settings.min_confidence || 0.0,
          validation_type: settings.validation_type || 'quick',
          model: settings.model || ''
        };

        setValidationSettings(validationConfig);
        setConfig(validationConfig);
        setValidationType(settings.validation_type || 'quick');
        if (settings.model && availableModels.includes(settings.model)) {
          setSelectedModel(settings.model);
        }

        console.log('✅ Loaded validation settings from Firestore:', validationConfig);
      } else {
        console.log('📝 No validation settings found, using defaults');
        // Set default settings
        const defaultSettings = {
          max_samples: 20,
          min_confidence: 0.0,
          validation_type: 'quick',
          model: ''
        };
        setValidationSettings(defaultSettings);
        setConfig(defaultSettings);
      }
    } catch (err) {
      console.error('Failed to load validation settings:', err);
      // Use fallback settings
      const fallbackSettings = {
        max_samples: 20,
        min_confidence: 0.0
      };
      setValidationSettings(fallbackSettings);
      setConfig(fallbackSettings);
    }
  };

  const loadValidationHistory = async () => {
    try {
      // Set up real-time listener for validation results
      const validationQuery = query(
        collection(db, 'validation_results'),
        orderBy('started_at', 'desc')
      );

      const unsubscribe = onSnapshot(validationQuery, (snapshot) => {
        const results = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        setValidationHistory(results);
        console.log(`📊 Loaded ${results.length} validation records from Firestore`);
      }, (error) => {
        console.error('❌ Error loading validation history:', error);
        // Set empty array on error
        setValidationHistory([]);
      });

      return unsubscribe;
    } catch (err) {
      console.error('Failed to set up validation history listener:', err);
      setValidationHistory([]);
    }
  };

  const handleValidationTypeChange = (value: ValidationType) => {
    setValidationType(value);
    if (value === 'custom') {
      setConfig({
        max_samples: 10,
        min_confidence: 0.0
      });
    } else {
      setConfig({});
    }
  };

  const handleConfigChange = (key: keyof ValidationConfig, value: string | number) => {
    setConfig(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleStartValidation = async () => {
    if (!selectedModel) {
      toast({
        title: "Error",
        description: "Please select a model first",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(null);
    setValidationResults(null);
    setIsValidating(true);
    setProgress(0);

    try {
      // Use the new modular validation API
      const validationRequest = {
        model_id: selectedModel,
        validation_type: validationType,
        model_type: 'asr',
        max_samples: config.max_samples || 20,
        min_confidence: config.min_confidence || 0.0,
        save_predictions: true,
        save_error_analysis: true
      };

      let results;

      try {
        // Call the new validation API endpoint
        const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000';
        const response = await fetch(`${backendUrl}/api/validation/validate/sync`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(validationRequest)
        });

        if (response.ok) {
          results = await response.json();
          setCurrentValidationId(results.validation_id);

          // Save real validation results to Firestore
          try {
            const firestoreData = {
              ...results,
              started_at: serverTimestamp(),
              completed_at: results.status === 'completed' ? serverTimestamp() : null
            };
            await addDoc(collection(db, 'validation_results'), firestoreData);
            console.log('✅ Real validation results saved to Firestore');
          } catch (firestoreError) {
            console.error('❌ Failed to save validation results to Firestore:', firestoreError);
          }
        } else {
          // Try to get the error details
          let errorMessage = 'Backend API error';
          try {
            const errorData = await response.json();
            errorMessage = errorData.detail || errorData.message || `HTTP ${response.status}`;
          } catch (e) {
            errorMessage = `HTTP ${response.status}: ${response.statusText}`;
          }
          console.warn('Backend validation API error:', errorMessage);
          throw new Error(errorMessage);
        }
      } catch (apiError) {
        console.error('Validation failed:', apiError);

        // Check if it's a "no models available" error
        const errorMessage = apiError.message || apiError.toString();
        if (errorMessage.includes('not found') || errorMessage.includes('No trained models') || (errorMessage.includes('Model') && errorMessage.includes('not found'))) {
          toast({
            title: "No Trained Models Available",
            description: "You need to train an ASR model before you can validate it. Go to the Training page to create your first model.",
            variant: "destructive",
          });

          setError("No trained models available. Please train a model first.");
          setValidationResults(null);
          setProgress(0);
          setIsValidating(false);
          setIsLoading(false);
          return;
        }

        // Check if it's a "not implemented" error (real model inference not ready)
        if (errorMessage.includes('not implemented') || errorMessage.includes('NotImplementedError')) {
          toast({
            title: "Validation Not Ready",
            description: "Real model validation is not implemented yet. The system is being prepared for actual model inference.",
            variant: "destructive",
          });

          setError("Real model validation not implemented yet.");
          setValidationResults(null);
          setProgress(0);
          setIsValidating(false);
          setIsLoading(false);
          return;
        }

        // For other errors, show generic error message
        toast({
          title: "Validation Failed",
          description: "Something went wrong during validation. Please check your connection and try again.",
          variant: "destructive",
        });

        setError("Validation failed. Please try again.");
        setValidationResults(null);
        setProgress(0);
        setIsValidating(false);
        setIsLoading(false);
        return;
      }

      // If we reach here, validation was successful
      // Convert new format to legacy format for UI compatibility
      const legacyResults = {
        metrics: {
          wer: results.metrics.wer || 0,
          cer: results.metrics.cer || 0,
          ser: results.metrics.ser || 0,
          accuracy: results.metrics.accuracy || 0,
          confidence: results.metrics.confidence || 0
        },
        summary: results.summary,
        error_analysis: results.error_analysis
      };

      setProgress(100);
      setValidationResults(legacyResults);
      setSuccess('Validation completed successfully');

      // Save validation result to history
      await addDoc(collection(db, 'validation_results'), {
        ...results,
        model_id: selectedModel,
        validation_type: validationType,
        config: validationRequest,
        timestamp: serverTimestamp()
      });

      toast({
        title: "Validation Complete",
        description: "Model validation has been completed successfully.",
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Validation failed');
      toast({
        title: "Validation Failed",
        description: err instanceof Error ? err.message : 'Validation failed',
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
      setIsValidating(false);
    }
  };

  const handleStopValidation = () => {
    setIsStopConfirmOpen(true);
  };

  const handleStopConfirm = async () => {
    setIsValidating(false);
    setError('Validation stopped by user');
    toast({
      title: "Validation Stopped",
      description: "Model validation has been stopped.",
    });
    setIsStopConfirmOpen(false);
  };

  const handleSaveSettings = async () => {
    try {
      // Prepare settings to save
      const settingsToSave = {
        validation_type: validationType,
        max_samples: config.max_samples ?? 20,
        min_confidence: config.min_confidence ?? 0.0,
        model: selectedModel,
        updated_at: serverTimestamp(),
        updated_by: 'user' // You can replace this with actual user info
      };

      // Save to Firestore
      await setDoc(doc(db, 'validation_settings', 'asr_validation_config'), settingsToSave);

      // Update local state
      setValidationSettings(settingsToSave);
      setConfig(settingsToSave);

      setShowSettings(false);
      toast({
        title: "Settings Saved",
        description: "Validation settings have been saved to Firestore successfully.",
      });

      console.log('✅ Validation settings saved to Firestore:', settingsToSave);
    } catch (err) {
      console.error('❌ Failed to save validation settings:', err);
      toast({
        title: "Error",
        description: "Failed to save validation settings to Firestore",
        variant: "destructive",
      });
    }
  };

  const handleDeleteValidation = async (validationId: string) => {
    try {
      await deleteDoc(doc(db, 'validation_results', validationId));
      toast({
        title: "Validation Deleted",
        description: "Validation result has been deleted successfully.",
      });
      console.log('✅ Validation result deleted from Firestore:', validationId);
    } catch (err) {
      console.error('❌ Failed to delete validation result:', err);
      toast({
        title: "Error",
        description: "Failed to delete validation result",
        variant: "destructive",
      });
    }
  };

  const handleViewDetails = (result: any) => {
    setSelectedResult(result);
    setShowResultDetails(true);
  };

  const formatMetric = (value: number | undefined, isPercentage = true) => {
    if (value === undefined || value === null) return 'N/A';
    return isPercentage ? `${(value * 100).toFixed(2)}%` : value.toFixed(3);
  };

  // Enhanced feature functions
  const handleBatchValidation = async () => {
    if (selectedModelsForBatch.length === 0) {
      toast({
        title: "Error",
        description: "Please select models for batch validation",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      for (const modelId of selectedModelsForBatch) {
        const validationRequest = {
          model_id: modelId,
          validation_type: validationType,
          model_type: 'asr',
          max_samples: config.max_samples || 20,
          min_confidence: config.min_confidence || 0.0,
          save_predictions: true,
          save_error_analysis: true
        };

        try {
          const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000';
          const response = await fetch(`${backendUrl}/api/validation/validate/sync`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(validationRequest)
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || 'Validation failed');
          }
        } catch (apiError) {
          console.error(`Validation failed for ${modelId}:`, apiError);
          toast({
            title: "Validation Failed",
            description: `Failed to validate model ${modelId}: ${apiError.message}`,
            variant: "destructive",
          });
          continue; // Skip this model and continue with others
        }
      }

      toast({
        title: "Batch Validation Complete",
        description: `Successfully validated ${selectedModelsForBatch.length} models`,
      });
      setShowBatchValidation(false);
    } catch (err) {
      toast({
        title: "Batch Validation Failed",
        description: "Some validations may have failed",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleExportReport = async () => {
    setIsExporting(true);
    try {
      const filteredHistory = validationHistory.filter(result => {
        const matchesSearch = searchQuery === '' ||
          result.model_id?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          result.config?.validation_type?.toLowerCase().includes(searchQuery.toLowerCase());
        const matchesStatus = filterStatus === 'all' || result.status === filterStatus;
        const matchesType = filterType === 'all' || result.config?.validation_type === filterType;
        return matchesSearch && matchesStatus && matchesType;
      });

      const csvContent = [
        ['Date', 'Model', 'Type', 'Status', 'Accuracy', 'WER', 'CER', 'Samples'].join(','),
        ...filteredHistory.map(result => [
          result.started_at ? new Date(result.started_at.seconds * 1000).toLocaleDateString() : 'N/A',
          result.model_id || 'N/A',
          result.config?.validation_type || 'unknown',
          result.status || 'unknown',
          formatMetric(result.metrics?.accuracy),
          formatMetric(result.metrics?.wer),
          formatMetric(result.metrics?.cer),
          result.summary?.successful_samples || 'N/A'
        ].join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `validation-report-${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      URL.revokeObjectURL(url);

      toast({
        title: "Report Exported",
        description: "Validation report has been downloaded",
      });
    } catch (err) {
      toast({
        title: "Export Failed",
        description: "Failed to export validation report",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  const calculateValidationStats = () => {
    if (validationHistory.length === 0) return null;

    const completed = validationHistory.filter(r => r.status === 'completed');
    const avgAccuracy = completed.reduce((sum, r) => sum + (r.metrics?.accuracy || 0), 0) / completed.length;
    const avgWER = completed.reduce((sum, r) => sum + (r.metrics?.wer || 0), 0) / completed.length;
    const totalValidations = validationHistory.length;
    const successRate = (completed.length / totalValidations) * 100;

    return {
      totalValidations,
      completedValidations: completed.length,
      successRate,
      avgAccuracy,
      avgWER,
      recentValidations: validationHistory.slice(0, 5)
    };
  };

  const filteredHistory = validationHistory.filter(result => {
    const matchesSearch = searchQuery === '' ||
      result.model_id?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      result.config?.validation_type?.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = filterStatus === 'all' || result.status === filterStatus;
    const matchesType = filterType === 'all' || result.config?.validation_type === filterType;
    return matchesSearch && matchesStatus && matchesType;
  });

  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-3">
            <h3 className="text-lg font-medium">ASR Model Validation</h3>
            <div className="flex items-center gap-2">
              {backendStatus === 'checking' && (
                <div className="flex items-center gap-1 text-xs text-gray-500">
                  <Loader2 className="h-3 w-3 animate-spin" />
                  Checking backend...
                </div>
              )}
              {backendStatus === 'healthy' && (
                <div className="flex items-center gap-1 text-xs text-green-600">
                  <CheckCircle2 className="h-3 w-3" />
                  Backend online
                </div>
              )}
              {backendStatus === 'error' && (
                <div className="flex items-center gap-1 text-xs text-orange-600">
                  <AlertCircle className="h-3 w-3" />
                  Backend issues
                </div>
              )}
              {backendStatus === 'unavailable' && (
                <div className="flex items-center gap-1 text-xs text-red-600">
                  <XCircle className="h-3 w-3" />
                  Backend unavailable
                </div>
              )}
            </div>
          </div>
          <p className="text-sm text-muted-foreground">
            Validate and test the Automatic Speech Recognition model
          </p>
        </div>
        <div className="flex gap-2 flex-wrap">
          <Button
            variant="outline"
            size="icon"
            onClick={() => router.push('/dashboard/ai/asr')}
            title="Back to ASR Dashboard"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>



          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowBatchValidation(true)}
            title="Batch Validation"
            className="bg-orange-50 border-orange-200 hover:bg-orange-100"
          >
            <Users className="h-4 w-4 text-orange-600 mr-1" />
            Batch
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowScheduler(true)}
            title="Schedule Validation"
            className="bg-indigo-50 border-indigo-200 hover:bg-indigo-100"
          >
            <Calendar className="h-4 w-4 text-indigo-600 mr-1" />
            Schedule
          </Button>



          <Button
            variant="outline"
            size="sm"
            onClick={handleExportReport}
            disabled={isExporting}
            title="Export Report"
            className="bg-emerald-50 border-emerald-200 hover:bg-emerald-100"
          >
            {isExporting ? (
              <Loader2 className="h-4 w-4 text-emerald-600 mr-1 animate-spin" />
            ) : (
              <Download className="h-4 w-4 text-emerald-600 mr-1" />
            )}
            Export
          </Button>

          <Button
            variant="outline"
            size="icon"
            onClick={() => setShowHistory(true)}
            title="Validation History"
            className="bg-blue-50 border-blue-200 hover:bg-blue-100"
          >
            <History className="h-4 w-4 text-blue-600" />
          </Button>

          <Dialog open={showSettings} onOpenChange={setShowSettings}>
            <DialogTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                title="Validation Settings"
                className="bg-green-50 border-green-200 hover:bg-green-100"
              >
                <Settings className="h-4 w-4 text-green-600" />
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Validation Settings</DialogTitle>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label>Validation Type</Label>
                  <Select
                    value={validationType}
                    onValueChange={handleValidationTypeChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select validation type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="quick">Quick Validation (20 Samples)</SelectItem>
                      <SelectItem value="full">Full Validation (All Samples)</SelectItem>
                      <SelectItem value="custom">Custom Validation</SelectItem>
                      <SelectItem value="benchmark">Benchmark Testing</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {validationType === 'custom' && (
                  <>
                    <div className="space-y-2">
                      <Label>Max Samples</Label>
                      <div className="flex items-center gap-2">
                        <Input
                          type="number"
                          value={config.max_samples}
                          onChange={(e) => handleConfigChange('max_samples', parseInt(e.target.value))}
                          min={1}
                          max={100}
                        />
                        <span className="text-sm text-gray-500">samples</span>
                      </div>
                      <p className="text-sm text-gray-500">
                        Number of samples to use for validation (1-100)
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label>Min Confidence</Label>
                      <div className="flex items-center gap-2">
                        <Input
                          type="number"
                          value={config.min_confidence}
                          onChange={(e) => handleConfigChange('min_confidence', parseFloat(e.target.value))}
                          min={0}
                          max={1}
                          step={0.1}
                        />
                        <span className="text-sm text-gray-500">(0-1)</span>
                      </div>
                      <p className="text-sm text-gray-500">
                        Minimum confidence threshold for validation
                      </p>
                    </div>
                  </>
                )}

                <div className="space-y-2">
                  <Label>Model</Label>
                  <Select
                    value={selectedModel}
                    onValueChange={setSelectedModel}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select model" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableModels.length > 0 ? (
                        availableModels.map((model) => (
                          <SelectItem key={model} value={model}>
                            {model}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="" disabled>
                          No trained models available
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowSettings(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSaveSettings}
                >
                  Save Settings
                </Button>
              </div>
            </DialogContent>
          </Dialog>

          <Button
            variant="destructive" 
            onClick={() => setIsStopConfirmOpen(true)}
            disabled={!isValidating}
          >
            Stop Validation
          </Button>

          <Button
            onClick={handleStartValidation}
            disabled={isValidating || isLoading || !selectedModel}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : isValidating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Validation Initiated
              </>
            ) : (
              "Start Validation"
            )}
          </Button>
        </div>
      </div>

      {/* Quick Stats Row */}
      {validationHistory.length > 0 && (
        <div className="grid gap-4 md:grid-cols-4 mt-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Validations</p>
                  <p className="text-2xl font-bold">{validationHistory.length}</p>
                </div>
                <FileText className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Success Rate</p>
                  <p className="text-2xl font-bold text-green-600">
                    {Math.round((validationHistory.filter(r => r.status === 'completed').length / validationHistory.length) * 100)}%
                  </p>
                </div>
                <CheckCircle2 className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Avg Accuracy</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {formatMetric(validationHistory.filter(r => r.metrics?.accuracy).reduce((sum, r) => sum + r.metrics.accuracy, 0) / validationHistory.filter(r => r.metrics?.accuracy).length)}
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Recent Activity</p>
                  <p className="text-2xl font-bold text-orange-600">
                    {validationHistory.filter(r => {
                      const date = r.started_at ? new Date(r.started_at.seconds * 1000) : new Date();
                      const daysDiff = (new Date().getTime() - date.getTime()) / (1000 * 3600 * 24);
                      return daysDiff <= 7;
                    }).length}
                  </p>
                  <p className="text-xs text-muted-foreground">Last 7 days</p>
                </div>
                <Clock className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mt-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-yellow-500" />
              Validation Status
            </CardTitle>
            <CardDescription>Current validation progress</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {availableModels.length === 0 ? (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-3">
                      <div>
                        <p className="font-medium">No trained models available</p>
                        <p className="text-sm">Train your first ASR model to start validation.</p>
                      </div>
                      <Button
                        onClick={() => router.push('/dashboard/ai/asr')}
                        size="sm"
                        className="w-full"
                      >
                        <ExternalLink className="mr-2 h-4 w-4" />
                        Go to Training Page
                      </Button>
                    </div>
                  </AlertDescription>
                </Alert>
              ) : (
                <>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Overall Progress</span>
                      <span className="text-sm text-muted-foreground">{progress}%</span>
                    </div>
                    <Progress value={progress} />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Samples Processed</span>
                      <span className="text-sm text-muted-foreground">
                        {Math.floor((progress / 100) * (validationType === 'full' ? 100 : (config.max_samples ?? 10)))}/
                        {validationType === 'full' ? 'All' : config.max_samples}
                      </span>
                    </div>
                    <Progress value={progress} />
                  </div>
                </>
              )}
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
        </Card>

        {validationResults && (
          <Card>
            <CardHeader>
              <CardTitle>Validation Results</CardTitle>
              <CardDescription>Model performance metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label>Accuracy</Label>
                  <div className="text-2xl font-bold text-green-600">
                    {formatMetric(validationResults.metrics.accuracy)}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Confidence</Label>
                  <div className="text-2xl font-bold text-blue-600">
                    {formatMetric(validationResults.metrics.confidence)}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Word Error Rate (WER)</Label>
                  <div className="text-2xl font-bold text-red-600">
                    {formatMetric(validationResults.metrics.wer)}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Character Error Rate (CER)</Label>
                  <div className="text-2xl font-bold text-orange-600">
                    {formatMetric(validationResults.metrics.cer)}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Sentence Error Rate (SER)</Label>
                  <div className="text-2xl font-bold text-purple-600">
                    {formatMetric(validationResults.metrics.ser)}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Samples Tested</Label>
                  <div className="text-2xl font-bold text-gray-600">
                    {validationResults.summary?.successful_samples || 'N/A'}
                  </div>
                </div>
              </div>

              {validationResults.summary && (
                <div className="mt-6 pt-4 border-t">
                  <h4 className="font-medium mb-2">Summary</h4>
                  <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Total Samples:</span>
                      <div className="font-medium">{validationResults.summary.total_samples}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Successful:</span>
                      <div className="font-medium text-green-600">{validationResults.summary.successful_samples}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Failed:</span>
                      <div className="font-medium text-red-600">{validationResults.summary.failed_samples}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Avg Confidence:</span>
                      <div className="font-medium">{formatMetric(validationResults.summary.average_confidence)}</div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Metrics Dashboard Section */}
      {validationHistory.length > 0 && (
        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-purple-600" />
                Validation Metrics Dashboard
              </CardTitle>
              <CardDescription>
                Comprehensive analytics and performance insights
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6">
                {/* Summary Cards */}
                <div className="grid gap-4 md:grid-cols-3">
                  <Card className="border-green-200 bg-green-50">
                    <CardContent className="p-6">
                      <div className="text-center">
                        <h3 className="text-lg font-semibold text-green-800">Average Accuracy</h3>
                        <p className="text-3xl font-bold text-green-600 mt-2">
                          {formatMetric(validationHistory.filter(r => r.metrics?.accuracy).reduce((sum, r) => sum + r.metrics.accuracy, 0) / validationHistory.filter(r => r.metrics?.accuracy).length)}
                        </p>
                        <p className="text-sm text-green-600 mt-1">
                          Across {validationHistory.filter(r => r.metrics?.accuracy).length} validations
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                  <Card className="border-red-200 bg-red-50">
                    <CardContent className="p-6">
                      <div className="text-center">
                        <h3 className="text-lg font-semibold text-red-800">Average WER</h3>
                        <p className="text-3xl font-bold text-red-600 mt-2">
                          {formatMetric(validationHistory.filter(r => r.metrics?.wer).reduce((sum, r) => sum + r.metrics.wer, 0) / validationHistory.filter(r => r.metrics?.wer).length)}
                        </p>
                        <p className="text-sm text-red-600 mt-1">
                          Lower is better
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                  <Card className="border-blue-200 bg-blue-50">
                    <CardContent className="p-6">
                      <div className="text-center">
                        <h3 className="text-lg font-semibold text-blue-800">Success Rate</h3>
                        <p className="text-3xl font-bold text-blue-600 mt-2">
                          {Math.round((validationHistory.filter(r => r.status === 'completed').length / validationHistory.length) * 100)}%
                        </p>
                        <p className="text-sm text-blue-600 mt-1">
                          {validationHistory.filter(r => r.status === 'completed').length} of {validationHistory.length} completed
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Recent Validations Timeline */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Clock className="h-5 w-5" />
                      Recent Validation Timeline
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {validationHistory.slice(0, 8).map((result, index) => (
                        <div key={result.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                          <div className="flex items-center gap-4">
                            <div className="flex-shrink-0">
                              {result.status === 'completed' ? (
                                <CheckCircle2 className="h-5 w-5 text-green-500" />
                              ) : result.status === 'failed' ? (
                                <XCircle className="h-5 w-5 text-red-500" />
                              ) : (
                                <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />
                              )}
                            </div>
                            <div>
                              <div className="flex items-center gap-2">
                                <span className="font-medium">{result.model_id}</span>
                                <Badge variant="outline" className="text-xs">
                                  {result.config?.validation_type || 'unknown'}
                                </Badge>
                              </div>
                              <p className="text-sm text-muted-foreground">
                                {result.started_at ? new Date(result.started_at.seconds * 1000).toLocaleString() : 'N/A'}
                              </p>
                            </div>
                          </div>
                          <div className="flex gap-6 text-sm">
                            <div className="text-center">
                              <p className="text-muted-foreground">Accuracy</p>
                              <p className="font-bold text-green-600">{formatMetric(result.metrics?.accuracy)}</p>
                            </div>
                            <div className="text-center">
                              <p className="text-muted-foreground">WER</p>
                              <p className="font-bold text-red-600">{formatMetric(result.metrics?.wer)}</p>
                            </div>
                            <div className="text-center">
                              <p className="text-muted-foreground">Samples</p>
                              <p className="font-bold">{result.summary?.successful_samples || 'N/A'}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Model Performance Comparison Section */}
      {validationHistory.length > 0 && Array.from(new Set(validationHistory.map(r => r.model_id))).length > 1 && (
        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart2 className="h-5 w-5 text-teal-600" />
                Model Performance Comparison
              </CardTitle>
              <CardDescription>
                Compare performance metrics across different models
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {Array.from(new Set(validationHistory.map(r => r.model_id))).map((modelId) => {
                  const modelResults = validationHistory.filter(r => r.model_id === modelId && r.status === 'completed');
                  if (modelResults.length === 0) return null;

                  const avgAccuracy = modelResults.reduce((sum, r) => sum + (r.metrics?.accuracy || 0), 0) / modelResults.length;
                  const avgWER = modelResults.reduce((sum, r) => sum + (r.metrics?.wer || 0), 0) / modelResults.length;
                  const avgCER = modelResults.reduce((sum, r) => sum + (r.metrics?.cer || 0), 0) / modelResults.length;
                  const latestValidation = modelResults[0];

                  return (
                    <Card key={modelId} className="border-2 hover:border-blue-300 transition-colors">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg flex items-center justify-between">
                          {modelId}
                          <Badge variant="secondary">{modelResults.length} validations</Badge>
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          {/* Performance Metrics */}
                          <div className="grid grid-cols-2 gap-4">
                            <div className="text-center p-3 bg-green-50 rounded-lg">
                              <p className="text-sm font-medium text-green-800">Accuracy</p>
                              <p className="text-xl font-bold text-green-600">{formatMetric(avgAccuracy)}</p>
                            </div>
                            <div className="text-center p-3 bg-red-50 rounded-lg">
                              <p className="text-sm font-medium text-red-800">WER</p>
                              <p className="text-xl font-bold text-red-600">{formatMetric(avgWER)}</p>
                            </div>
                          </div>

                          {/* Additional Metrics */}
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">CER:</span>
                              <span className="font-medium">{formatMetric(avgCER)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Last Validation:</span>
                              <span className="font-medium">
                                {latestValidation.started_at ? new Date(latestValidation.started_at.seconds * 1000).toLocaleDateString() : 'N/A'}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Success Rate:</span>
                              <span className="font-medium">
                                {Math.round((modelResults.length / validationHistory.filter(r => r.model_id === modelId).length) * 100)}%
                              </span>
                            </div>
                          </div>

                          {/* Performance Indicator */}
                          <div className="pt-2 border-t">
                            <div className="flex items-center gap-2">
                              {avgAccuracy > 0.9 ? (
                                <>
                                  <CheckCircle2 className="h-4 w-4 text-green-500" />
                                  <span className="text-sm text-green-600 font-medium">Excellent Performance</span>
                                </>
                              ) : avgAccuracy > 0.8 ? (
                                <>
                                  <CheckCircle2 className="h-4 w-4 text-blue-500" />
                                  <span className="text-sm text-blue-600 font-medium">Good Performance</span>
                                </>
                              ) : (
                                <>
                                  <AlertCircle className="h-4 w-4 text-orange-500" />
                                  <span className="text-sm text-orange-600 font-medium">Needs Improvement</span>
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <Dialog open={isStopConfirmOpen} onOpenChange={setIsStopConfirmOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Stop Validation</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>Are you sure you want to stop the validation process? This action cannot be undone.</p>
          </div>
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={() => setIsStopConfirmOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleStopConfirm}
            >
              Stop Validation
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Validation History Dialog */}
      <Dialog open={showHistory} onOpenChange={setShowHistory}>
        <DialogContent className="max-w-7xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <History className="h-5 w-5" />
              Validation History
              <Badge variant="secondary" className="ml-2">
                {filteredHistory.length} results
              </Badge>
            </DialogTitle>
          </DialogHeader>

          {/* Search and Filters */}
          <div className="flex gap-4 py-4 border-b">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by model ID or validation type..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
                <SelectItem value="running">Running</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="quick">Quick</SelectItem>
                <SelectItem value="full">Full</SelectItem>
                <SelectItem value="custom">Custom</SelectItem>
                <SelectItem value="benchmark">Benchmark</SelectItem>
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setSearchQuery('');
                setFilterStatus('all');
                setFilterType('all');
              }}
            >
              <RefreshCw className="h-4 w-4 mr-1" />
              Reset
            </Button>
          </div>

          <div className="py-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Model</TableHead>
                  <TableHead>Accuracy</TableHead>
                  <TableHead>WER</TableHead>
                  <TableHead>CER</TableHead>
                  <TableHead>Samples</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredHistory.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center">
                      {validationHistory.length === 0 ? 'No validation history found' : 'No results match your filters'}
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredHistory.map((result) => (
                    <TableRow key={result.id}>
                      <TableCell>
                        {result.started_at ? new Date(result.started_at.seconds * 1000).toLocaleDateString() : 'N/A'}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{result.config?.validation_type || 'unknown'}</Badge>
                      </TableCell>
                      <TableCell>{result.model_id || 'N/A'}</TableCell>
                      <TableCell>{formatMetric(result.metrics?.accuracy)}</TableCell>
                      <TableCell>{formatMetric(result.metrics?.wer)}</TableCell>
                      <TableCell>{formatMetric(result.metrics?.cer)}</TableCell>
                      <TableCell>{result.summary?.successful_samples || 'N/A'}</TableCell>
                      <TableCell>
                        <Badge variant={result.status === 'completed' ? 'default' : 'destructive'}>
                          {result.status || 'unknown'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewDetails(result)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteValidation(result.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </DialogContent>
      </Dialog>

      {/* Result Details Dialog */}
      <Dialog open={showResultDetails} onOpenChange={setShowResultDetails}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Validation Result Details</DialogTitle>
          </DialogHeader>
          {selectedResult && (
            <div className="py-4 space-y-6">
              {/* Metrics */}
              <div>
                <h4 className="font-medium mb-3">Performance Metrics</h4>
                <div className="grid grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="p-3 border rounded">
                    <div className="text-sm text-muted-foreground">Accuracy</div>
                    <div className="text-lg font-bold text-green-600">
                      {formatMetric(selectedResult.metrics?.accuracy)}
                    </div>
                  </div>
                  <div className="p-3 border rounded">
                    <div className="text-sm text-muted-foreground">Confidence</div>
                    <div className="text-lg font-bold text-blue-600">
                      {formatMetric(selectedResult.metrics?.confidence)}
                    </div>
                  </div>
                  <div className="p-3 border rounded">
                    <div className="text-sm text-muted-foreground">WER</div>
                    <div className="text-lg font-bold text-red-600">
                      {formatMetric(selectedResult.metrics?.wer)}
                    </div>
                  </div>
                  <div className="p-3 border rounded">
                    <div className="text-sm text-muted-foreground">CER</div>
                    <div className="text-lg font-bold text-orange-600">
                      {formatMetric(selectedResult.metrics?.cer)}
                    </div>
                  </div>
                  <div className="p-3 border rounded">
                    <div className="text-sm text-muted-foreground">SER</div>
                    <div className="text-lg font-bold text-purple-600">
                      {formatMetric(selectedResult.metrics?.ser)}
                    </div>
                  </div>
                </div>
              </div>

              {/* Configuration */}
              <div>
                <h4 className="font-medium mb-3">Configuration</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Validation Type:</span>
                    <div className="font-medium">{selectedResult.config?.validation_type || 'N/A'}</div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Max Samples:</span>
                    <div className="font-medium">{selectedResult.config?.max_samples || 'N/A'}</div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Min Confidence:</span>
                    <div className="font-medium">{selectedResult.config?.min_confidence || 'N/A'}</div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Model ID:</span>
                    <div className="font-medium">{selectedResult.model_id || 'N/A'}</div>
                  </div>
                </div>
              </div>

              {/* Error Analysis Preview */}
              {selectedResult.error_analysis && selectedResult.error_analysis.length > 0 && (
                <div>
                  <h4 className="font-medium mb-3">Error Analysis (First 5 samples)</h4>
                  <div className="space-y-2">
                    {selectedResult.error_analysis.slice(0, 5).map((error: any, index: number) => (
                      <div key={index} className="p-3 border rounded text-sm">
                        <div className="font-medium">Sample: {error.sample_id}</div>
                        <div className="text-muted-foreground">Reference: {error.reference}</div>
                        <div className="text-muted-foreground">Prediction: {error.prediction}</div>
                        <div className="text-xs text-muted-foreground mt-1">
                          Confidence: {formatMetric(error.prediction_confidence)}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Batch Validation Dialog */}
      <Dialog open={showBatchValidation} onOpenChange={setShowBatchValidation}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Batch Validation
            </DialogTitle>
          </DialogHeader>
          <div className="py-4 space-y-4">
            <div>
              <Label>Select Models for Batch Validation</Label>
              <div className="mt-2 space-y-2">
                {availableModels.length > 0 ? (
                  availableModels.map((model) => (
                    <div key={model} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={`batch-${model}`}
                        checked={selectedModelsForBatch.includes(model)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedModelsForBatch([...selectedModelsForBatch, model]);
                          } else {
                            setSelectedModelsForBatch(selectedModelsForBatch.filter(m => m !== model));
                          }
                        }}
                        className="rounded"
                      />
                      <label htmlFor={`batch-${model}`} className="text-sm font-medium">
                        {model}
                      </label>
                    </div>
                  ))
                ) : (
                  <div className="text-sm text-muted-foreground p-4 border rounded-lg bg-gray-50">
                    No trained models available for batch validation. Please train models first.
                  </div>
                )}
              </div>
            </div>
            <div>
              <Label>Validation Configuration</Label>
              <Select value={validationType} onValueChange={setValidationType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="quick">Quick Validation</SelectItem>
                  <SelectItem value="full">Full Validation</SelectItem>
                  <SelectItem value="custom">Custom Validation</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowBatchValidation(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleBatchValidation}
              disabled={selectedModelsForBatch.length === 0 || isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Validating...
                </>
              ) : (
                `Validate ${selectedModelsForBatch.length} Models`
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>



      {/* Scheduler Dialog */}
      <Dialog open={showScheduler} onOpenChange={setShowScheduler}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Schedule Validation
            </DialogTitle>
          </DialogHeader>
          <div className="py-4 space-y-4">
            {/* Enable/Disable Toggle */}
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="space-y-1">
                <Label className="text-base font-medium">Scheduled Validation</Label>
                <p className="text-sm text-muted-foreground">
                  {scheduleEnabled ? 'Automatic validations are enabled' : 'Automatic validations are disabled'}
                </p>
              </div>
              <Switch
                checked={scheduleEnabled}
                onCheckedChange={setScheduleEnabled}
                className="data-[state=checked]:bg-green-600"
              />
            </div>

            {/* Schedule Configuration (only show when enabled) */}
            {scheduleEnabled && (
              <>
                <div>
                  <Label>Model</Label>
                  <Select value={selectedModel} onValueChange={setSelectedModel}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {availableModels.length > 0 ? (
                        availableModels.map((model) => (
                          <SelectItem key={model} value={model}>{model}</SelectItem>
                        ))
                      ) : (
                        <SelectItem value="" disabled>
                          No trained models available
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>Schedule Type</Label>
                  <Select value={scheduleType} onValueChange={setScheduleType}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>Validation Type</Label>
                  <Select value={validationType} onValueChange={setValidationType}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="quick">Quick Validation</SelectItem>
                      <SelectItem value="full">Full Validation</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Scheduled validations will run automatically {scheduleType} based on your configuration.
                  </AlertDescription>
                </Alert>
              </>
            )}

            {/* Disabled State Message */}
            {!scheduleEnabled && (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Enable scheduled validation to automatically run model validations at regular intervals.
                </AlertDescription>
              </Alert>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowScheduler(false)}>
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (scheduleEnabled) {
                  toast({
                    title: "Schedule Updated",
                    description: `Validation scheduled to run ${scheduleType} for ${selectedModel}`,
                  });
                } else {
                  toast({
                    title: "Schedule Disabled",
                    description: "Automatic validation has been disabled",
                  });
                }
                setShowScheduler(false);
              }}
              className={scheduleEnabled ? "bg-green-600 hover:bg-green-700" : "bg-gray-600 hover:bg-gray-700"}
            >
              {scheduleEnabled ? "Save Schedule" : "Disable Schedule"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>


    </div>
  );
}