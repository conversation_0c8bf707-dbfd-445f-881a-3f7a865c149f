"use client"

import React, { createContext, useContext, useState, useEffect } from 'react'
import { Language, detectLanguage, setLanguage as setLang, getTranslation, FocusedTranslations } from '@/lib/focused-translations'

interface FocusedLanguageContextType {
  language: Language
  setLanguage: (lang: Language) => void
  t: (key: keyof FocusedTranslations) => string
  isRTL: boolean
}

const FocusedLanguageContext = createContext<FocusedLanguageContextType | undefined>(undefined)

export function FocusedLanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguageState] = useState<Language>('en')
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    const detectedLang = detectLanguage()
    setLanguageState(detectedLang)
    setLang(detectedLang)
    setMounted(true)
  }, [])

  const setLanguage = (lang: Language) => {
    setLanguageState(lang)
    setLang(lang)
  }

  const t = (key: keyof FocusedTranslations): string => {
    return getTranslation(key, language)
  }

  const isRTL = language === 'ar'

  // Don't render until mounted to avoid hydration mismatch
  if (!mounted) {
    return null
  }

  return (
    <FocusedLanguageContext.Provider value={{ language, setLanguage, t, isRTL }}>
      {children}
    </FocusedLanguageContext.Provider>
  )
}

export function useFocusedLanguage() {
  const context = useContext(FocusedLanguageContext)
  if (context === undefined) {
    throw new Error('useFocusedLanguage must be used within a FocusedLanguageProvider')
  }
  return context
}
