"""
Application settings and configuration
"""

import os
from pathlib import Path
from typing import List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    """Application settings"""
    
    # API Configuration
    API_HOST: str = Field(default="127.0.0.1", env="API_HOST")
    API_PORT: int = Field(default=8000, env="API_PORT")
    DEBUG: bool = Field(default=True, env="DEBUG")
    
    # Firebase Configuration
    FIREBASE_PROJECT_ID: str = Field(..., env="FIREBASE_PROJECT_ID")
    FIREBASE_CLIENT_EMAIL: str = Field(..., env="FIREBASE_CLIENT_EMAIL")
    FIREBASE_PRIVATE_KEY: str = Field(..., env="FIREBASE_PRIVATE_KEY")
    FIREBASE_STORAGE_BUCKET: str = Field(..., env="FIREBASE_STORAGE_BUCKET")
    FIREBASE_PRIVATE_KEY_ID: str = Field(..., env="FIREBASE_PRIVATE_KEY_ID")
    FIREBASE_CLIENT_ID: str = Field(..., env="FIREBASE_CLIENT_ID")
    FIREBASE_CLIENT_CERT_URL: str = Field(..., env="FIREBASE_CLIENT_CERT_URL")
    
    # Google Cloud Storage Configuration
    GCS_PROJECT_ID: str = Field(..., env="GCS_PROJECT_ID")
    GCS_PRIVATE_KEY_ID: str = Field(..., env="GCS_PRIVATE_KEY_ID")
    GCS_PRIVATE_KEY: str = Field(..., env="GCS_PRIVATE_KEY")
    GCS_CLIENT_EMAIL: str = Field(..., env="GCS_CLIENT_EMAIL")
    GCS_CLIENT_ID: str = Field(..., env="GCS_CLIENT_ID")
    GCS_CLIENT_CERT_URL: str = Field(..., env="GCS_CLIENT_CERT_URL")
    GCS_BUCKET_NAME: str = Field(..., env="GCS_BUCKET_NAME")
    
    # Model Storage Paths
    CHECKPOINT_DIR: str = Field(default="./ai_models/checkpoints", env="CHECKPOINT_DIR")
    FINAL_MODEL_DIR: str = Field(default="./ai_models/trained", env="FINAL_MODEL_DIR")
    TEMP_DIR: str = Field(default="./temp", env="TEMP_DIR")
    
    # Audio Processing Settings
    MAX_AUDIO_FILE_SIZE: int = Field(default=52428800, env="MAX_AUDIO_FILE_SIZE")  # 50MB
    MAX_AUDIO_DURATION: int = Field(default=300, env="MAX_AUDIO_DURATION")  # 5 minutes
    SUPPORTED_AUDIO_FORMATS: str = Field(default="wav,mp3,m4a,ogg,webm,flac", env="SUPPORTED_AUDIO_FORMATS")
    TARGET_SAMPLE_RATE: int = Field(default=16000, env="TARGET_SAMPLE_RATE")
    TARGET_CHANNELS: int = Field(default=1, env="TARGET_CHANNELS")
    
    # Training Configuration
    DEFAULT_MODEL_NAME: str = Field(default="openai/whisper-small", env="DEFAULT_MODEL_NAME")
    DEFAULT_EPOCHS: int = Field(default=5, env="DEFAULT_EPOCHS")
    DEFAULT_LEARNING_RATE: float = Field(default=0.001, env="DEFAULT_LEARNING_RATE")
    DEFAULT_BATCH_SIZE: int = Field(default=8, env="DEFAULT_BATCH_SIZE")
    DEFAULT_VALIDATION_SPLIT: float = Field(default=0.2, env="DEFAULT_VALIDATION_SPLIT")

    # Model Upload Configuration
    UPLOAD_TO_GCS: bool = Field(default=True, env="UPLOAD_TO_GCS")

    # Training Timeout Configuration
    DEFAULT_TRAINING_TIMEOUT: int = Field(default=7200, env="DEFAULT_TRAINING_TIMEOUT")  # 2 hours in seconds
    MAX_TRAINING_TIMEOUT: int = Field(default=14400, env="MAX_TRAINING_TIMEOUT")  # 4 hours max
    
    @property
    def supported_audio_formats_list(self) -> List[str]:
        """Get supported audio formats as a list"""
        return [fmt.strip() for fmt in self.SUPPORTED_AUDIO_FORMATS.split(",")]
    
    @property
    def firebase_credentials(self) -> dict:
        """Get Firebase credentials as a dictionary"""
        return {
            "type": "service_account",
            "project_id": self.FIREBASE_PROJECT_ID,
            "private_key_id": self.FIREBASE_PRIVATE_KEY_ID,
            "private_key": self.FIREBASE_PRIVATE_KEY.replace("\\n", "\n"),
            "client_email": self.FIREBASE_CLIENT_EMAIL,
            "client_id": self.FIREBASE_CLIENT_ID,
            "auth_uri": "https://accounts.google.com/o/oauth2/auth",
            "token_uri": "https://oauth2.googleapis.com/token",
            "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
            "client_x509_cert_url": self.FIREBASE_CLIENT_CERT_URL
        }
    
    @property
    def gcs_credentials(self) -> dict:
        """Get GCS credentials as a dictionary"""
        return {
            "type": "service_account",
            "project_id": self.GCS_PROJECT_ID,
            "private_key_id": self.GCS_PRIVATE_KEY_ID,
            "private_key": self.GCS_PRIVATE_KEY.replace("\\n", "\n"),
            "client_email": self.GCS_CLIENT_EMAIL,
            "client_id": self.GCS_CLIENT_ID,
            "auth_uri": "https://accounts.google.com/o/oauth2/auth",
            "token_uri": "https://oauth2.googleapis.com/token",
            "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
            "client_x509_cert_url": self.GCS_CLIENT_CERT_URL
        }
    
    class Config:
        env_file = [".env", "../.env", "../.env.local"]  # Look for backend/.env first, then root files
        case_sensitive = True
        extra = "ignore"  # Ignore extra environment variables

# Create global settings instance
settings = Settings()
