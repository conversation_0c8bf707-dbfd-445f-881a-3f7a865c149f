"use client"

import { LoginForm } from "@/components/login-form"
import { useFocusedLanguage, FocusedLanguageProvider } from "@/components/focused-language-provider"
import { Button } from "@/components/ui/button"
import { Globe } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

function HomeContent() {
  const { t, language, setLanguage, isRTL } = useFocusedLanguage()

  return (
    <main className={`flex min-h-screen flex-col items-center justify-center p-4 bg-gradient-to-b from-slate-50 to-slate-100 dark:from-slate-950 dark:to-slate-900 ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className="w-full max-w-md space-y-8">
        {/* Language Selector */}
        <div className="flex justify-end">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="gap-2">
                <Globe className="h-4 w-4" />
                {language === 'ar' ? 'العربية' : 'English'}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setLanguage('en')}>
                English
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setLanguage('ar')}>
                العربية
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="text-center">
          <h1 className="text-4xl font-bold tracking-tight text-slate-900 dark:text-slate-100">
            {t('masalitLanguage')}
          </h1>
          <h2 className="text-2xl font-semibold text-slate-700 dark:text-slate-300 mt-2">
            {t('languagePlatform')}
          </h2>
          <p className="mt-4 text-slate-600 dark:text-slate-400">
            {t('contributeToPreserving')}
          </p>
        </div>

        <LoginForm />
      </div>
    </main>
  )
}

export default function Home() {
  return (
    <FocusedLanguageProvider>
      <HomeContent />
    </FocusedLanguageProvider>
  )
}
