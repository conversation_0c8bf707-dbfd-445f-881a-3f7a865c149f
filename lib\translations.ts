export type Language = 'en' | 'ar'

export interface Translations {
  // Common
  dashboard: string
  upload: string
  history: string
  profile: string
  settings: string
  logout: string
  loading: string
  error: string
  success: string
  cancel: string
  save: string
  delete: string
  edit: string
  view: string
  
  // Dashboard
  welcome: string
  welcomeBack: string
  quickActions: string
  uploadAudio: string
  uploadAudioDesc: string
  viewHistory: string
  viewHistoryDesc: string
  viewProfile: string
  viewProfileDesc: string
  aiTraining: string
  aiTrainingDesc: string
  
  // Stats
  totalMinutes: string
  totalRecordings: string
  approvalRate: string
  statusDistribution: string
  approved: string
  pending: string
  rejected: string
  lastUpload: string
  never: string
  avgDuration: string
  
  // Charts
  statusDistributionChart: string
  statusDistributionDesc: string
  monthlyContributions: string
  monthlyContributionsDesc: string
  
  // Lists
  topContributors: string
  topContributorsDesc: string
  recentUploads: string
  recentUploadsDesc: string
  
  // Admin specific
  adminDashboard: string
  userManagement: string
  systemStats: string
  totalUsers: string
  totalAudio: string
  systemHealth: string
  
  // Language
  language: string
  english: string
  arabic: string
  selectLanguage: string

  // Login/Auth
  signIn: string
  signUp: string
  welcomeToMasalit: string
  platformDescription: string
  usernameOrEmail: string
  password: string
  confirmPassword: string
  forgotPassword: string
  dontHaveAccount: string
  alreadyHaveAccount: string
  createAccount: string
  resetPassword: string
  resetPasswordDesc: string
  sendResetLink: string
  backToLogin: string
  masalitLanguage: string
  languagePlatform: string
  contributeToPreserving: string
  fullName: string
  username: string
  email: string
  joinMasalitPlatform: string
  accountCreatedSuccess: string
  goToLogin: string
  passwordsDoNotMatch: string
  usernameInvalidChars: string
  usernameAlreadyTaken: string
  emailAlreadyRegistered: string
}

export const translations: Record<Language, Translations> = {
  en: {
    // Common
    dashboard: 'Dashboard',
    upload: 'Upload',
    history: 'History',
    profile: 'Profile',
    settings: 'Settings',
    logout: 'Logout',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    view: 'View',
    
    // Dashboard
    welcome: 'Welcome',
    welcomeBack: 'Welcome back',
    quickActions: 'Quick Actions',
    uploadAudio: 'Upload Audio',
    uploadAudioDesc: 'Upload audio files and transcripts',
    viewHistory: 'View History',
    viewHistoryDesc: 'View your upload history',
    viewProfile: 'View Profile',
    viewProfileDesc: 'View and edit your profile',
    aiTraining: 'AI Training',
    aiTrainingDesc: 'Manage AI models and training',
    
    // Stats
    totalMinutes: 'Total Minutes',
    totalRecordings: 'Total Recordings',
    approvalRate: 'Approval Rate',
    statusDistribution: 'Status Distribution',
    approved: 'Approved',
    pending: 'Pending',
    rejected: 'Rejected',
    lastUpload: 'Last upload',
    never: 'Never',
    avgDuration: 'Avg',
    
    // Charts
    statusDistributionChart: 'Status Distribution',
    statusDistributionDesc: 'Breakdown of your recordings by status',
    monthlyContributions: 'Monthly Contributions',
    monthlyContributionsDesc: 'Your contributions over the last 6 months',
    
    // Lists
    topContributors: 'Top Contributors',
    topContributorsDesc: 'Users with the most contribution minutes',
    recentUploads: 'Recent Uploads',
    recentUploadsDesc: 'Latest contributions to the platform',
    
    // Admin specific
    adminDashboard: 'Admin Dashboard',
    userManagement: 'User Management',
    systemStats: 'System Statistics',
    totalUsers: 'Total Users',
    totalAudio: 'Total Audio Files',
    systemHealth: 'System Health',
    
    // Language
    language: 'Language',
    english: 'English',
    arabic: 'العربية',
    selectLanguage: 'Select Language',

    // Login/Auth
    signIn: 'Sign In',
    signUp: 'Sign Up',
    welcomeToMasalit: 'Welcome back to the Masalit platform',
    platformDescription: 'Contribute to preserving and documenting the Masalit language',
    usernameOrEmail: 'Username or Email',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    forgotPassword: 'Forgot password?',
    dontHaveAccount: "Don't have an account?",
    alreadyHaveAccount: 'Already have an account?',
    createAccount: 'Create Account',
    resetPassword: 'Reset Password',
    resetPasswordDesc: "Enter your email address and we'll send you a link to reset your password.",
    sendResetLink: 'Send Reset Link',
    backToLogin: 'Back to Login',
    masalitLanguage: 'Masalit Language',
    languagePlatform: 'Language Platform',
    contributeToPreserving: 'Contribute to preserving and documenting the Masalit language',
    fullName: 'Full Name',
    username: 'Username',
    email: 'Email',
    joinMasalitPlatform: 'Join the Masalit platform to start contributing',
    accountCreatedSuccess: 'Account created successfully! Please check your email to verify your account before logging in.',
    goToLogin: 'Go to Login',
    passwordsDoNotMatch: 'Passwords do not match',
    usernameInvalidChars: 'Username can only contain letters, numbers, and underscores',
    usernameAlreadyTaken: 'Username is already taken',
    emailAlreadyRegistered: 'Email is already registered',
  },
  ar: {
    // Common
    dashboard: 'لوحة التحكم',
    upload: 'رفع',
    history: 'التاريخ',
    profile: 'الملف الشخصي',
    settings: 'الإعدادات',
    logout: 'تسجيل الخروج',
    loading: 'جاري التحميل...',
    error: 'خطأ',
    success: 'نجح',
    cancel: 'إلغاء',
    save: 'حفظ',
    delete: 'حذف',
    edit: 'تعديل',
    view: 'عرض',
    
    // Dashboard
    welcome: 'مرحباً',
    welcomeBack: 'مرحباً بعودتك',
    quickActions: 'الإجراءات السريعة',
    uploadAudio: 'رفع الصوت',
    uploadAudioDesc: 'رفع ملفات الصوت والنصوص',
    viewHistory: 'عرض التاريخ',
    viewHistoryDesc: 'عرض تاريخ الرفع الخاص بك',
    viewProfile: 'عرض الملف الشخصي',
    viewProfileDesc: 'عرض وتعديل ملفك الشخصي',
    aiTraining: 'تدريب الذكاء الاصطناعي',
    aiTrainingDesc: 'إدارة نماذج الذكاء الاصطناعي والتدريب',
    
    // Stats
    totalMinutes: 'إجمالي الدقائق',
    totalRecordings: 'إجمالي التسجيلات',
    approvalRate: 'معدل الموافقة',
    statusDistribution: 'توزيع الحالة',
    approved: 'موافق عليه',
    pending: 'في الانتظار',
    rejected: 'مرفوض',
    lastUpload: 'آخر رفع',
    never: 'أبداً',
    avgDuration: 'متوسط',
    
    // Charts
    statusDistributionChart: 'توزيع الحالة',
    statusDistributionDesc: 'تفصيل تسجيلاتك حسب الحالة',
    monthlyContributions: 'المساهمات الشهرية',
    monthlyContributionsDesc: 'مساهماتك خلال الـ 6 أشهر الماضية',
    
    // Lists
    topContributors: 'أفضل المساهمين',
    topContributorsDesc: 'المستخدمون الذين لديهم أكثر دقائق المساهمة',
    recentUploads: 'الرفع الحديث',
    recentUploadsDesc: 'أحدث المساهمات في المنصة',
    
    // Admin specific
    adminDashboard: 'لوحة تحكم المدير',
    userManagement: 'إدارة المستخدمين',
    systemStats: 'إحصائيات النظام',
    totalUsers: 'إجمالي المستخدمين',
    totalAudio: 'إجمالي ملفات الصوت',
    systemHealth: 'صحة النظام',
    
    // Language
    language: 'اللغة',
    english: 'English',
    arabic: 'العربية',
    selectLanguage: 'اختر اللغة',

    // Login/Auth
    signIn: 'تسجيل الدخول',
    signUp: 'إنشاء حساب',
    welcomeToMasalit: 'مرحباً بعودتك إلى منصة المساليت',
    platformDescription: 'ساهم في الحفاظ على لغة المساليت وتوثيقها',
    usernameOrEmail: 'اسم المستخدم أو البريد الإلكتروني',
    password: 'كلمة المرور',
    confirmPassword: 'تأكيد كلمة المرور',
    forgotPassword: 'نسيت كلمة المرور؟',
    dontHaveAccount: 'ليس لديك حساب؟',
    alreadyHaveAccount: 'لديك حساب بالفعل؟',
    createAccount: 'إنشاء حساب',
    resetPassword: 'إعادة تعيين كلمة المرور',
    resetPasswordDesc: 'أدخل عنوان بريدك الإلكتروني وسنرسل لك رابطاً لإعادة تعيين كلمة المرور.',
    sendResetLink: 'إرسال رابط الإعادة',
    backToLogin: 'العودة لتسجيل الدخول',
    masalitLanguage: 'لغة المساليت',
    languagePlatform: 'منصة اللغة',
    contributeToPreserving: 'ساهم في الحفاظ على لغة المساليت وتوثيقها',
    fullName: 'الاسم الكامل',
    username: 'اسم المستخدم',
    email: 'البريد الإلكتروني',
    joinMasalitPlatform: 'انضم إلى منصة المساليت لبدء المساهمة',
    accountCreatedSuccess: 'تم إنشاء الحساب بنجاح! يرجى التحقق من بريدك الإلكتروني للتحقق من حسابك قبل تسجيل الدخول.',
    goToLogin: 'الذهاب لتسجيل الدخول',
    passwordsDoNotMatch: 'كلمات المرور غير متطابقة',
    usernameInvalidChars: 'يمكن أن يحتوي اسم المستخدم على أحرف وأرقام وشرطات سفلية فقط',
    usernameAlreadyTaken: 'اسم المستخدم مأخوذ بالفعل',
    emailAlreadyRegistered: 'البريد الإلكتروني مسجل بالفعل',
  }
}

// Language detection and management
export const detectLanguage = (): Language => {
  // Check localStorage first
  const saved = localStorage.getItem('language') as Language
  if (saved && ['en', 'ar'].includes(saved)) {
    return saved
  }
  
  // Check browser language
  const browserLang = navigator.language.toLowerCase()
  if (browserLang.startsWith('ar')) {
    return 'ar'
  }
  
  // Default to English
  return 'en'
}

export const setLanguage = (lang: Language) => {
  localStorage.setItem('language', lang)
  // Update document direction for RTL
  document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr'
  document.documentElement.lang = lang
}

export const getTranslation = (key: keyof Translations, lang: Language): string => {
  return translations[lang][key] || translations.en[key]
}
