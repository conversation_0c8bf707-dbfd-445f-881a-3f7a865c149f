import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Progress, getDynamicTextColor } from "@/components/ui/progress"
import { useASR } from "@/lib/asr"
import { Badge } from "@/components/ui/badge"
import { formatDistanceToNow } from "date-fns"
import { Loader2, CheckCircle, AlertCircle } from "lucide-react"
import { useEffect } from "react"
import { getTrainingStatus } from "@/lib/asr"
import { doc, onSnapshot } from "firebase/firestore"
import { db } from "@/lib/firebase"

export function TrainingProgress() {
  const { status, isLoading, setStatus } = useASR()

  useEffect(() => {
    // Set up real-time listener for ASR status
    const statusRef = doc(db, 'training_status', 'asr')
    const statusUnsubscribe = onSnapshot(statusRef, (doc) => {
      if (doc.exists()) {
        const data = doc.data()
        console.log('Training status update:', data) // Debug log
        
        // Calculate progress based on status
        let progress = data.progress || 0
        if (data.status === 'completed') {
          progress = 100 // Set to 100% when completed
        } else {
          // Use the progress from backend (already in 0-100 range)
          progress = data.progress || 0
        }
        
        // Update status state
        setStatus({
          status: data.status || 'not_started',
          current_epoch: data.current_epoch || 0,
          total_epochs: data.total_epochs || 0,
          progress: progress,
          current_accuracy: data.current_accuracy || 0,
          current_confidence: data.current_confidence || 0,
          current_wer: data.current_wer || 0,
          current_cer: data.current_cer || 0,
          current_ser: data.current_ser || 0,
          samples_processed: data.samples_processed || 0,
          total_samples: data.total_samples || 0,
          training_progress: data.training_progress || 0,
          error: data.error,
          start_time: data.start_time,
          end_time: data.end_time
        })
      }
    }, (error) => {
      console.error('Error in training status listener:', error)
    })

    return () => {
      statusUnsubscribe()
    }
  }, [setStatus])

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Training Progress</CardTitle>
          <CardDescription>Current training status and metrics</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-4">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!status) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Training Progress</CardTitle>
          <CardDescription>Current training status and metrics</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground py-4">
            No training data available
          </div>
        </CardContent>
      </Card>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "training":
        return "bg-blue-500"
      case "completed":
        return "bg-green-500"
      case "failed":
        return "bg-red-500"
      case "error":
        return "bg-red-500"
      default:
        return "bg-gray-500"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "training":
        return <Loader2 className="h-4 w-4 animate-spin" />
      case "completed":
        return <CheckCircle className="h-4 w-4" />
      case "failed":
      case "error":
        return <AlertCircle className="h-4 w-4" />
      default:
        return null
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Training Progress</CardTitle>
        <CardDescription>Current training status and metrics</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className={getStatusColor(status.status)}>
                {getStatusIcon(status.status)}
                <span className="ml-2 capitalize">{status.status}</span>
              </Badge>
              {status.start_time && (
                <span className="text-sm text-muted-foreground">
                  Started {formatDistanceToNow(new Date(status.start_time), { addSuffix: true })}
                </span>
              )}
            </div>
            {status.model_version && (
              <Badge variant="secondary">
                v{status.model_version}
              </Badge>
            )}
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Overall Progress</span>
              <span className={getDynamicTextColor(Math.round(status.progress), 'inverse')}>
                {Math.round(status.progress)}%
              </span>
            </div>
            <Progress
              value={status.progress}
              showDynamicColors={true}
              colorScheme="inverse"
            />
          </div>

          {/* Show samples processed during data preparation and training */}
          {(status.status === "preparing_data" || status.status === "training") && status.total_samples > 0 && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Samples Processed</span>
                <span className="text-sm text-muted-foreground">{status.samples_processed}/{status.total_samples}</span>
              </div>
              <Progress
                value={status.total_samples > 0 ? Math.min((status.samples_processed / status.total_samples) * 100, 100) : 0}
                showDynamicColors={true}
                colorScheme="inverse"
              />
            </div>
          )}

          {/* Show epoch progress only during training */}
          {status.status === "training" && status.total_epochs > 0 && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Current Epoch</span>
                <span className="text-sm text-muted-foreground">{status.current_epoch}/{status.total_epochs}</span>
              </div>
              <Progress
                value={status.total_epochs > 0 ? Math.min((status.current_epoch / status.total_epochs) * 100, 100) : 0}
                showDynamicColors={true}
                colorScheme="inverse"
              />
            </div>
          )}

          {status.error && (
            <div className="text-sm text-red-500">
              Error: {status.error}
            </div>
          )}

          {status.current_accuracy > 0 && (
            <div className="space-y-2">
              <div className="text-sm font-medium">Training Accuracy</div>
              <Progress
                value={Math.round(status.current_accuracy * 100)}
                showDynamicColors={true}
                colorScheme="inverse"
                className="h-2"
              />
              <div className="text-sm">
                <span className={getDynamicTextColor(Math.round(status.current_accuracy * 100), 'inverse')}>
                  {Math.round(status.current_accuracy * 100)}%
                </span>
              </div>
            </div>
          )}

          {status.current_confidence > 0 && (
            <div className="space-y-2">
              <div className="text-sm font-medium">Training Confidence</div>
              <Progress
                value={Math.round(status.current_confidence * 100)}
                showDynamicColors={true}
                colorScheme="inverse"
                className="h-2"
              />
              <div className="text-sm">
                <span className={getDynamicTextColor(Math.round(status.current_confidence * 100), 'inverse')}>
                  {Math.round(status.current_confidence * 100)}%
                </span>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}