#!/usr/bin/env python3
"""
Update Frontend for New Firebase Structure

Updates all frontend components to use the new hierarchical Firebase structure
instead of the old flat collections.
"""

import os
import re
from datetime import datetime

def update_audio_upload_lib():
    """Update lib/audio-upload.ts to use new structure"""
    file_path = "lib/audio-upload.ts"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Add comment about new structure
        new_header = '''// Updated for New Hierarchical Firebase Structure
// Audio data is now stored as:
// audio/{audio_id}/ (main document)
// audio/{audio_id}/transcriptions/primary/ (transcription)
// audio/{audio_id}/metadata/details/ (metadata)
// audio/{audio_id}/review/status/ (review status)
// audio/{audio_id}/training/status/ (unified training status)

'''
        
        # Add the header if not already present
        if "Updated for New Hierarchical Firebase Structure" not in content:
            content = new_header + content
        
        # Update the uploadAudio function to use new API endpoints
        old_upload = '''    // Save audio metadata to Firestore
    const audioDoc = {
      id: audioId,
      audio_url: downloadURL,
      title: metadata.title,
      duration: metadata.duration,
      format: metadata.format,
      gender: metadata.gender,
      created_at: new Date(),
      updated_at: new Date(),
      user_id: userId,
      action: 'pending' as const,
      trained_asr: false,
      tts_trained: false,
      is_flagged: false,
      source: metadata.source || 'direct_upload',
    }

    await setDoc(doc(db, 'audio', audioId), audioDoc)'''

        new_upload = '''    // Use new hierarchical API endpoint
    const audioData = {
      title: metadata.title,
      duration: metadata.duration,
      format: metadata.format,
      user_id: userId,
      source: metadata.source || 'direct_upload',
      gender: metadata.gender,
      language: metadata.language || 'masalit',
      recording_context: metadata.recordingContext
    }

    // Create hierarchical audio record via API
    const response = await fetch('/api/audio/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...audioData,
        audio_url: downloadURL
      }),
    })

    if (!response.ok) {
      throw new Error('Failed to create audio record')
    }

    const result = await response.json()
    const audioId = result.audio_id'''

        content = content.replace(old_upload, new_upload)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ Updated {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error updating {file_path}: {e}")
        return False

def update_transcription_lib():
    """Update lib/transcription.ts for new structure"""
    file_path = "lib/transcription.ts"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Update functions to use new API endpoints
        old_save_transcription = '''export async function saveTranscription(transcriptionData: TranscriptionData): Promise<string> {
  try {
    const transcriptionId = `transcription_${Date.now()}`
    
    await setDoc(doc(db, 'transcription', transcriptionId), {
      ...transcriptionData,
      id: transcriptionId,
      created_at: new Date(),
      updated_at: new Date(),
    })
    
    return transcriptionId
  } catch (error) {
    console.error('Error saving transcription:', error)
    throw error
  }
}'''

        new_save_transcription = '''export async function saveTranscription(audioId: string, transcriptionData: Omit<TranscriptionData, 'audio_id'>): Promise<string> {
  try {
    // Use new hierarchical API endpoint
    const response = await fetch(`/api/audio/${audioId}/transcription`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(transcriptionData),
    })

    if (!response.ok) {
      throw new Error('Failed to save transcription')
    }

    const result = await response.json()
    return result.transcription_id || 'primary'
  } catch (error) {
    console.error('Error saving transcription:', error)
    throw error
  }
}'''

        content = content.replace(old_save_transcription, new_save_transcription)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ Updated {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error updating {file_path}: {e}")
        return False

def create_new_audio_api_lib():
    """Create new audio API library for hierarchical structure"""
    file_path = "lib/audio-api.ts"
    
    content = '''// New Audio API Library for Hierarchical Firebase Structure
// This library provides functions to interact with the new hierarchical audio structure

export interface HierarchicalAudioData {
  // Main document
  id: string
  title: string
  audio_url: string
  duration: number
  format: string
  created_at: string
  user_id: string
  source: string

  // Subcollections (loaded on demand)
  transcriptions?: {
    primary?: {
      content: string
      language: string
      transcription_source: string
      created_at: string
    }
  }
  metadata?: {
    details?: {
      gender: string
      language: string
      recording_context?: string
    }
  }
  review?: {
    status?: {
      action: 'pending' | 'approved' | 'rejected'
      reviewed_by?: string
      reviewed_at?: string
      feedback?: string
      is_flagged: boolean
    }
  }
  training?: {
    status?: {
      trained_asr: boolean
      tts_trained: boolean
      training_sessions: string[]
      last_trained_at?: string
    }
  }
}

export interface HierarchicalUserData {
  // Main document
  email: string
  name: string
  username: string
  role: string
  created_at: string

  // Subcollections (loaded on demand)
  profile?: {
    details?: {
      avatar_url?: string
      bio?: string
      language_preferences: string[]
      email_verified: boolean
    }
  }
  statistics?: {
    summary?: {
      contribution_count: number
      audio_uploads: number
      transcriptions_created: number
      last_activity: string
    }
  }
  preferences?: {
    settings?: {
      theme: 'light' | 'dark'
      language: string
      notifications: object
    }
  }
}

// Audio API Functions
export async function getAudioWithSubcollections(
  audioId: string, 
  include: string[] = ['transcriptions', 'metadata', 'review', 'training']
): Promise<HierarchicalAudioData | null> {
  try {
    const includeParam = include.join(',')
    const response = await fetch(`/api/audio/${audioId}?include=${includeParam}`)
    
    if (!response.ok) {
      if (response.status === 404) return null
      throw new Error('Failed to fetch audio data')
    }
    
    const result = await response.json()
    return result.data
  } catch (error) {
    console.error('Error fetching audio data:', error)
    throw error
  }
}

export async function listUserAudio(
  userId: string,
  options: {
    trained_asr?: boolean
    tts_trained?: boolean
    action?: string
    limit?: number
  } = {}
): Promise<HierarchicalAudioData[]> {
  try {
    const params = new URLSearchParams({
      user_id: userId,
      limit: (options.limit || 20).toString()
    })
    
    if (options.trained_asr !== undefined) {
      params.append('trained_asr', options.trained_asr.toString())
    }
    if (options.tts_trained !== undefined) {
      params.append('tts_trained', options.tts_trained.toString())
    }
    if (options.action) {
      params.append('action', options.action)
    }
    
    const response = await fetch(`/api/audio/?${params}`)
    
    if (!response.ok) {
      throw new Error('Failed to fetch user audio')
    }
    
    const result = await response.json()
    return result.audio_records
  } catch (error) {
    console.error('Error fetching user audio:', error)
    throw error
  }
}

export async function updateTrainingStatus(
  audioId: string,
  updates: {
    trained_asr?: boolean
    tts_trained?: boolean
    training_session_id?: string
  }
): Promise<void> {
  try {
    const response = await fetch(`/api/audio/${audioId}/training/status`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    })
    
    if (!response.ok) {
      throw new Error('Failed to update training status')
    }
  } catch (error) {
    console.error('Error updating training status:', error)
    throw error
  }
}

// User API Functions
export async function getUserWithSubcollections(
  userId: string,
  include: string[] = ['profile', 'statistics', 'preferences']
): Promise<HierarchicalUserData | null> {
  try {
    const includeParam = include.join(',')
    const response = await fetch(`/api/users/${userId}?include=${includeParam}`)
    
    if (!response.ok) {
      if (response.status === 404) return null
      throw new Error('Failed to fetch user data')
    }
    
    const result = await response.json()
    return result.data
  } catch (error) {
    console.error('Error fetching user data:', error)
    throw error
  }
}

export async function updateUserStatistics(
  userId: string,
  updates: {
    contribution_count?: number
    audio_uploads?: number
    transcriptions_created?: number
  }
): Promise<void> {
  try {
    const response = await fetch(`/api/users/${userId}/statistics`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    })
    
    if (!response.ok) {
      throw new Error('Failed to update user statistics')
    }
  } catch (error) {
    console.error('Error updating user statistics:', error)
    throw error
  }
}

export async function incrementUserStat(
  userId: string,
  statName: string,
  incrementBy: number = 1
): Promise<void> {
  try {
    const response = await fetch(`/api/users/${userId}/statistics/increment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        stat_name: statName,
        increment_by: incrementBy
      }),
    })
    
    if (!response.ok) {
      throw new Error('Failed to increment user statistic')
    }
  } catch (error) {
    console.error('Error incrementing user statistic:', error)
    throw error
  }
}
'''
    
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ Created {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating {file_path}: {e}")
        return False

def main():
    """Main function to update frontend for new structure"""
    print("🔄 Updating Frontend for New Hierarchical Firebase Structure")
    print("=" * 70)
    
    success_count = 0
    total_updates = 3
    
    # Update existing libraries
    if update_audio_upload_lib():
        success_count += 1
    
    if update_transcription_lib():
        success_count += 1
    
    # Create new API library
    if create_new_audio_api_lib():
        success_count += 1
    
    print(f"\n📊 Frontend Update Results:")
    print(f"✅ Successfully updated: {success_count}/{total_updates} files")
    
    if success_count == total_updates:
        print("\n🎉 Frontend libraries updated successfully!")
        print("📚 Updated/Created files:")
        print("   - lib/audio-upload.ts (updated for new structure)")
        print("   - lib/transcription.ts (updated for new structure)")
        print("   - lib/audio-api.ts (new hierarchical API library)")
        print("\n📝 Next Steps:")
        print("   1. Update frontend components to use new lib/audio-api.ts")
        print("   2. Replace direct Firebase calls with API calls")
        print("   3. Update upload/bulk-upload/history pages")
        print("   4. Update review and admin pages")
        print("   5. Update profile and user management pages")
    else:
        print(f"\n⚠️ Some files failed to update. Check the errors above.")

if __name__ == "__main__":
    main()
