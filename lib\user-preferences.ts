import React from 'react'
import { db } from './firebase'
import { doc, getDoc, setDoc, updateDoc, onSnapshot } from 'firebase/firestore'
import { create } from 'zustand'

// Types
export interface UIPreferences {
  theme: 'light' | 'dark' | 'system'
  language: 'en' | 'ar'
  dashboard_layout: 'compact' | 'comfortable' | 'spacious'
  sidebar_collapsed: boolean
  animations_enabled: boolean
  high_contrast: boolean
  font_size: 'small' | 'medium' | 'large'
  color_scheme: 'default' | 'colorblind' | 'high_contrast'
}

export interface ASRPreferences {
  favorites: string[]
  default_view_mode: 'table' | 'cards'
  default_sort_by: 'date' | 'accuracy' | 'wer'
  default_sort_order: 'asc' | 'desc'
  auto_refresh_interval: number
  show_advanced_metrics: boolean
  preferred_model_size: 'tiny' | 'small' | 'medium' | 'large'
  default_training_settings: {
    epochs: number
    learning_rate: number
    batch_size: number
    validation_split: number
  }
  notification_preferences: {
    training_complete: boolean
    training_failed: boolean
    validation_complete: boolean
  }
}

export interface TTSPreferences {
  favorites: string[]
  default_voice: string
  default_speed: number
  default_pitch: number
  preferred_output_format: 'wav' | 'mp3' | 'ogg'
  auto_play_samples: boolean
}

export interface RecordingPreferences {
  default_quality: 'low' | 'medium' | 'high'
  auto_save: boolean
  show_waveform: boolean
  noise_reduction: boolean
  auto_trim_silence: boolean
  preferred_duration: number
  microphone_device_id: string
  gain_level: number
}

export interface DashboardPreferences {
  favorite_widgets: string[]
  widget_layout: object
  default_date_range: '7d' | '30d' | '90d' | 'all'
  show_system_metrics: boolean
  refresh_interval: number
  compact_mode: boolean
}

export interface NotificationPreferences {
  email_notifications: boolean
  push_notifications: boolean
  sound_enabled: boolean
  training_updates: boolean
  system_alerts: boolean
  weekly_reports: boolean
  achievement_notifications: boolean
}

export interface UserPreferences {
  user_id: string
  created_at: string
  updated_at: string
  ui_preferences: UIPreferences
  asr_preferences: ASRPreferences
  tts_preferences: TTSPreferences
  recording_preferences: RecordingPreferences
  dashboard_preferences: DashboardPreferences
  notification_preferences: NotificationPreferences
  recent_activity: {
    recent_models: string[]
    recent_recordings: string[]
    recent_searches: string[]
    bookmarked_pages: string[]
    quick_actions: string[]
  }
  privacy_preferences: {
    analytics_enabled: boolean
    error_reporting: boolean
    usage_statistics: boolean
    data_sharing: boolean
  }
}

// Default preferences
export const defaultPreferences: Omit<UserPreferences, 'user_id' | 'created_at' | 'updated_at'> = {
  ui_preferences: {
    theme: 'system',
    language: 'en',
    dashboard_layout: 'comfortable',
    sidebar_collapsed: false,
    animations_enabled: true,
    high_contrast: false,
    font_size: 'medium',
    color_scheme: 'default'
  },
  asr_preferences: {
    favorites: [],
    default_view_mode: 'table',
    default_sort_by: 'date',
    default_sort_order: 'desc',
    auto_refresh_interval: 30,
    show_advanced_metrics: false,
    preferred_model_size: 'small',
    default_training_settings: {
      epochs: 5,
      learning_rate: 0.001,
      batch_size: 8,
      validation_split: 0.2
    },
    notification_preferences: {
      training_complete: true,
      training_failed: true,
      validation_complete: true
    }
  },
  tts_preferences: {
    favorites: [],
    default_voice: 'default',
    default_speed: 1.0,
    default_pitch: 1.0,
    preferred_output_format: 'wav',
    auto_play_samples: true
  },
  recording_preferences: {
    default_quality: 'medium',
    show_waveform: true,
    noise_reduction: false,
    auto_trim_silence: true,
    preferred_duration: 30,
    microphone_device_id: 'default',
    gain_level: 0.8
  },
  dashboard_preferences: {
    favorite_widgets: ['training_status', 'recent_activity', 'system_health'],
    widget_layout: {},
    default_date_range: '30d',
    show_system_metrics: true,
    refresh_interval: 60,
    compact_mode: false
  },
  notification_preferences: {
    email_notifications: true,
    push_notifications: true,
    sound_enabled: true,
    training_updates: true,
    system_alerts: true,
    weekly_reports: false,
    achievement_notifications: true
  },
  recent_activity: {
    recent_models: [],
    recent_recordings: [],
    recent_searches: [],
    bookmarked_pages: [],
    quick_actions: ['start_training', 'new_recording', 'view_history']
  },
  privacy_preferences: {
    analytics_enabled: true,
    error_reporting: true,
    usage_statistics: true,
    data_sharing: false
  }
}

// Zustand store for preferences
interface PreferencesStore {
  preferences: UserPreferences | null
  loading: boolean
  error: string | null
  setPreferences: (preferences: UserPreferences) => void
  updatePreferences: (updates: Partial<UserPreferences>) => Promise<void>
  addToFavorites: (type: 'asr' | 'tts', id: string) => Promise<void>
  removeFromFavorites: (type: 'asr' | 'tts', id: string) => Promise<void>
  addToRecentActivity: (type: keyof UserPreferences['recent_activity'], id: string) => Promise<void>
}

export const usePreferencesStore = create<PreferencesStore>((set, get) => ({
  preferences: null,
  loading: false,
  error: null,
  
  setPreferences: (preferences) => set({ preferences }),
  
  updatePreferences: async (updates) => {
    const { preferences } = get()
    if (!preferences) {
      console.error('No preferences available for updatePreferences')
      return
    }

    try {
      // Update preferences in the user document
      await UserPreferencesService.updatePreferences(preferences.user_id, updates)

      // Update local state
      const updatedPreferences = {
        ...preferences,
        ...updates,
        updated_at: new Date().toISOString()
      }
      set({ preferences: updatedPreferences })
    } catch (error) {
      console.error('Error updating preferences:', error)
      set({ error: 'Failed to update preferences' })
    }
  },
  
  addToFavorites: async (type, id) => {
    const { preferences, updatePreferences } = get()
    if (!preferences) {
      console.error('No preferences available for addToFavorites')
      return
    }

    const currentFavorites = type === 'asr'
      ? preferences.asr_preferences.favorites
      : preferences.tts_preferences.favorites

    if (!currentFavorites.includes(id)) {
      const updates = type === 'asr'
        ? { asr_preferences: { ...preferences.asr_preferences, favorites: [...currentFavorites, id] } }
        : { tts_preferences: { ...preferences.tts_preferences, favorites: [...currentFavorites, id] } }

      await updatePreferences(updates)
    }
  },

  removeFromFavorites: async (type, id) => {
    const { preferences, updatePreferences } = get()
    if (!preferences) {
      console.error('No preferences available for removeFromFavorites')
      return
    }

    const currentFavorites = type === 'asr'
      ? preferences.asr_preferences.favorites
      : preferences.tts_preferences.favorites

    const newFavorites = currentFavorites.filter(fav => fav !== id)
    const updates = type === 'asr'
      ? { asr_preferences: { ...preferences.asr_preferences, favorites: newFavorites } }
      : { tts_preferences: { ...preferences.tts_preferences, favorites: newFavorites } }

    await updatePreferences(updates)
  },
  
  addToRecentActivity: async (type, id) => {
    const { preferences, updatePreferences } = get()
    if (!preferences) return
    
    const currentItems = preferences.recent_activity[type]
    const newItems = [id, ...currentItems.filter(item => item !== id)].slice(0, 10) // Keep last 10
    
    await updatePreferences({
      recent_activity: {
        ...preferences.recent_activity,
        [type]: newItems
      }
    })
  }
}))

// Service functions
export class UserPreferencesService {
  static async getUserPreferences(userId: string): Promise<UserPreferences> {
    try {
      const userDocRef = doc(db, 'users', userId)
      const userDocSnap = await getDoc(userDocRef)

      if (userDocSnap.exists()) {
        const userData = userDocSnap.data()

        // Check if preferences exist in the user document
        if (userData.preferences) {
          return {
            user_id: userId,
            ...userData.preferences
          } as UserPreferences
        } else {
          // Create default preferences and add to user document
          const newPreferences = {
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            ...defaultPreferences
          }

          await updateDoc(userDocRef, {
            preferences: newPreferences,
            updated_at: new Date()
          })

          return {
            user_id: userId,
            ...newPreferences
          } as UserPreferences
        }
      } else {
        throw new Error('User document not found')
      }
    } catch (error) {
      console.error('Error getting user preferences:', error)
      throw error
    }
  }
  
  static subscribeToPreferences(userId: string, callback: (preferences: UserPreferences) => void) {
    const userDocRef = doc(db, 'users', userId)
    return onSnapshot(userDocRef, async (doc) => {
      if (doc.exists()) {
        const userData = doc.data()

        if (userData.preferences) {
          // Preferences exist, return them
          callback({
            user_id: userId,
            ...userData.preferences
          } as UserPreferences)
        } else {
          // Preferences don't exist, create them
          try {
            const newPreferences = {
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              ...defaultPreferences
            }

            await updateDoc(userDocRef, {
              preferences: newPreferences,
              updated_at: new Date()
            })

            callback({
              user_id: userId,
              ...newPreferences
            } as UserPreferences)
          } catch (error) {
            console.error('Error creating default preferences:', error)
          }
        }
      } else {
        console.error('User document not found for preferences subscription')
      }
    })
  }
  
  static async updatePreferences(userId: string, updates: Partial<UserPreferences>): Promise<void> {
    try {
      const userDocRef = doc(db, 'users', userId)

      // Remove user_id from updates since it's not part of the preferences object
      const { user_id, ...preferencesUpdates } = updates

      await updateDoc(userDocRef, {
        [`preferences.updated_at`]: new Date().toISOString(),
        ...Object.keys(preferencesUpdates).reduce((acc, key) => {
          acc[`preferences.${key}`] = preferencesUpdates[key as keyof typeof preferencesUpdates]
          return acc
        }, {} as any),
        updated_at: new Date()
      })
    } catch (error) {
      console.error('Error updating preferences:', error)
      throw error
    }
  }
}

// Hook to use preferences - will be imported from auth provider
export function useUserPreferences() {
  // This will be implemented in the component that uses it
  // by importing useAuth from the auth provider
  return {
    preferences: null,
    loading: false,
    error: null,
    isLoggedIn: false,
    userId: undefined
  }
}
