import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from './auth'
import { activityTracker } from './activity-tracker'

interface ActivityMiddlewareOptions {
  trackRoute?: boolean
  activityType?: string
  description?: string
  extractMetadata?: (req: NextRequest, res: NextResponse) => Record<string, any>
}

export function withActivityTracking(
  handler: (req: NextRequest, ...args: any[]) => Promise<NextResponse>,
  options: ActivityMiddlewareOptions = {}
) {
  return async function (req: NextRequest, ...args: any[]): Promise<NextResponse> {
    const startTime = Date.now()
    let response: NextResponse

    try {
      // Execute the original handler
      response = await handler(req, ...args)

      // Track successful activity if configured
      if (options.trackRoute && response.status < 400) {
        await trackActivity(req, response, options, startTime)
      }

      return response
    } catch (error) {
      // Track failed activity
      if (options.trackRoute) {
        await trackActivity(req, NextResponse.json({ error: 'Internal Server Error' }, { status: 500 }), options, startTime, error)
      }
      throw error
    }
  }
}

async function trackActivity(
  req: NextRequest,
  res: NextResponse,
  options: ActivityMiddlewareOptions,
  startTime: number,
  error?: any
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) return

    const duration = Date.now() - startTime
    const method = req.method
    const url = req.url
    const status = res.status

    let activityType = options.activityType
    let description = options.description

    // Auto-generate activity type and description if not provided
    if (!activityType || !description) {
      const routeInfo = analyzeRoute(method, url, status, error)
      activityType = activityType || routeInfo.type
      description = description || routeInfo.description
    }

    // Extract metadata
    let metadata: Record<string, any> = {
      method,
      url,
      status,
      duration,
      timestamp: new Date().toISOString()
    }

    if (options.extractMetadata) {
      metadata = { ...metadata, ...options.extractMetadata(req, res) }
    }

    if (error) {
      metadata.error = error.message
      metadata.stack = error.stack
    }

    // Get client information
    metadata.ip_address = req.headers.get('x-forwarded-for') || 
                         req.headers.get('x-real-ip') || 
                         'unknown'
    metadata.user_agent = req.headers.get('user-agent') || 'unknown'

    // Track the activity
    await activityTracker.trackActivity(
      session.user.id,
      activityType as any,
      description,
      metadata
    )
  } catch (trackingError) {
    console.error('Failed to track activity:', trackingError)
    // Don't throw to avoid breaking the main request
  }
}

function analyzeRoute(method: string, url: string, status: number, error?: any): {
  type: string
  description: string
} {
  const urlPath = new URL(url).pathname

  // Audio-related routes
  if (urlPath.includes('/api/audio')) {
    if (method === 'POST' && urlPath.includes('/upload')) {
      return {
        type: 'audio_upload',
        description: status < 400 ? 'Audio uploaded successfully' : 'Audio upload failed'
      }
    }
    if (method === 'PATCH' && urlPath.includes('/review')) {
      return {
        type: 'audio_review',
        description: status < 400 ? 'Audio reviewed' : 'Audio review failed'
      }
    }
    if (method === 'POST' && urlPath.includes('/bulk-upload')) {
      return {
        type: 'bulk_upload',
        description: status < 400 ? 'Bulk upload completed' : 'Bulk upload failed'
      }
    }
  }

  // User-related routes
  if (urlPath.includes('/api/users')) {
    if (method === 'PATCH' && urlPath.includes('/preferences')) {
      return {
        type: 'preferences_update',
        description: status < 400 ? 'Preferences updated' : 'Preferences update failed'
      }
    }
    if (method === 'PATCH') {
      return {
        type: 'profile_update',
        description: status < 400 ? 'Profile updated' : 'Profile update failed'
      }
    }
  }

  // Training-related routes
  if (urlPath.includes('/api/training') || urlPath.includes('/api/asr')) {
    if (method === 'POST' && urlPath.includes('/start')) {
      return {
        type: 'training_start',
        description: status < 400 ? 'Training started' : 'Training start failed'
      }
    }
  }

  // Auth-related routes
  if (urlPath.includes('/api/auth')) {
    if (urlPath.includes('/signin')) {
      return {
        type: 'login',
        description: status < 400 ? 'User signed in' : 'Sign in failed'
      }
    }
    if (urlPath.includes('/signout')) {
      return {
        type: 'logout',
        description: 'User signed out'
      }
    }
    if (urlPath.includes('/reset-password')) {
      return {
        type: 'password_change',
        description: status < 400 ? 'Password reset' : 'Password reset failed'
      }
    }
  }

  // Default fallback
  return {
    type: 'api_request',
    description: `${method} ${urlPath} - ${status < 400 ? 'Success' : 'Failed'}`
  }
}

// Specific middleware for common routes
export const trackAudioUpload = (handler: any) => 
  withActivityTracking(handler, {
    trackRoute: true,
    activityType: 'audio_upload',
    extractMetadata: (req) => {
      // Extract audio metadata from request if available
      return {}
    }
  })

export const trackProfileUpdate = (handler: any) =>
  withActivityTracking(handler, {
    trackRoute: true,
    activityType: 'profile_update',
    extractMetadata: (req) => {
      // Extract updated fields from request body
      return {}
    }
  })

export const trackPreferencesUpdate = (handler: any) =>
  withActivityTracking(handler, {
    trackRoute: true,
    activityType: 'preferences_update',
    extractMetadata: (req) => {
      // Extract preference changes from request body
      return {}
    }
  })

export const trackTrainingAction = (handler: any) =>
  withActivityTracking(handler, {
    trackRoute: true,
    extractMetadata: (req) => {
      // Extract training metadata
      return {}
    }
  })

// Helper function to manually track activities in API routes
export async function trackApiActivity(
  req: NextRequest,
  activityType: string,
  description: string,
  metadata?: Record<string, any>
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) return

    const enhancedMetadata = {
      ...metadata,
      method: req.method,
      url: req.url,
      ip_address: req.headers.get('x-forwarded-for') || 
                 req.headers.get('x-real-ip') || 
                 'unknown',
      user_agent: req.headers.get('user-agent') || 'unknown',
      timestamp: new Date().toISOString()
    }

    await activityTracker.trackActivity(
      session.user.id,
      activityType as any,
      description,
      enhancedMetadata
    )
  } catch (error) {
    console.error('Failed to track API activity:', error)
  }
}
