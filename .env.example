# Masalit AI Platform Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# FRONTEND CONFIGURATION (Next.js)
# =============================================================================

# API Configuration
NEXT_PUBLIC_BACKEND_URL=http://127.0.0.1:8000
NEXT_PUBLIC_APP_URL=http://127.0.0.1:3000
# Note: NEXT_PUBLIC_BACKEND_URL is the main backend URL used throughout the app

# Firebase Configuration (Frontend)
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

# NextAuth Configuration
NEXTAUTH_SECRET=your_nextauth_secret_key_here
NEXTAUTH_URL=http://localhost:3000

# =============================================================================
# BACKEND CONFIGURATION (Python/FastAPI)
# =============================================================================

# API Server Settings
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4
API_TIMEOUT=300
DEBUG=false

# Firebase Configuration (Backend - Service Account)
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_PRIVATE_KEY_ID=your_private_key_id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_private_key_here\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=your_service_account@your_project_id.iam.gserviceaccount.com
FIREBASE_CLIENT_ID=your_client_id
FIREBASE_CLIENT_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/your_service_account%40your_project_id.iam.gserviceaccount.com

# Google Cloud Storage Configuration
GCS_PROJECT_ID=your_gcs_project_id
GCS_PRIVATE_KEY_ID=your_gcs_private_key_id
GCS_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_gcs_private_key_here\n-----END PRIVATE KEY-----\n"
GCS_CLIENT_EMAIL=your_gcs_service_account@your_gcs_project_id.iam.gserviceaccount.com
GCS_CLIENT_ID=your_gcs_client_id
GCS_CLIENT_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/your_gcs_service_account%40your_gcs_project_id.iam.gserviceaccount.com
GCS_BUCKET_NAME=your_gcs_bucket_name

# Model Configuration
MODEL_NAME=openai/whisper-small
CHECKPOINT_DIR=checkpoints
FINAL_MODEL_DIR=final_models

# Training Configuration
DEFAULT_EPOCHS=5
DEFAULT_BATCH_SIZE=8
DEFAULT_LEARNING_RATE=1e-5

# Service Account Key Path (alternative to environment variables)
SERVICE_ACCOUNT_KEY_PATH=model-uploader.json

# =============================================================================
# ASR TRAINING SETTINGS (Firestore Configuration)
# =============================================================================
# These settings will be stored in Firestore under training_settings/asr
# The values below are defaults used when Firestore is not available

# Training Configuration
DEFAULT_EARLY_STOPPING_PATIENCE=3
DEFAULT_EVAL_STEPS=100
DEFAULT_MODEL_NAME=large
DEFAULT_NUMBER_OF_SAMPLES=1
DEFAULT_USE_AUGMENTATION=false
DEFAULT_VALIDATION_SPLIT=0.2

# Model Storage Paths
CHECKPOINT_DIR=./ai_models/checkpoints
FINAL_MODEL_DIR=./ai_models/trained
TEMP_DIR=./temp

# Audio Processing Settings
MAX_AUDIO_FILE_SIZE=********  # 50MB in bytes
MAX_AUDIO_DURATION=300        # 5 minutes in seconds
SUPPORTED_AUDIO_FORMATS=wav,mp3,m4a,ogg,webm,flac
TARGET_SAMPLE_RATE=16000
TARGET_CHANNELS=1

# Model Upload Configuration
UPLOAD_TO_GCS=true  # Set to false to disable GCS upload during training (models will still be saved locally)

# Training Timeout Configuration
DEFAULT_TRAINING_TIMEOUT=7200   # Default training timeout in seconds (2 hours)
MAX_TRAINING_TIMEOUT=14400      # Maximum allowed training timeout in seconds (4 hours)

# =============================================================================
# PRODUCTION OVERRIDES
# =============================================================================
# For production, update these values:

# Production Frontend URLs (uncomment and use these for production)
# NEXT_PUBLIC_BACKEND_URL=https://api.buragatechnologies.com
# NEXT_PUBLIC_APP_URL=https://buragatechnologies.com
# NEXTAUTH_URL=https://buragatechnologies.com
# API_HOST=api.buragatechnologies.com
# API_PORT=443

# Production API Settings
# DEBUG=false
# API_WORKERS=8

# =============================================================================
# DEVELOPMENT NOTES
# =============================================================================

# 1. Firebase Setup:
#    - Create a Firebase project at https://console.firebase.google.com
#    - Enable Firestore Database
#    - Enable Authentication (if using)
#    - Generate a service account key for backend access
#    - Get web app config for frontend access

# 2. Google Cloud Storage Setup:
#    - Create a GCS bucket for model storage
#    - Create a service account with Storage Admin permissions
#    - Download the service account key JSON file
#    - Either place it as model-uploader.json or use environment variables

# 3. Audio Processing Requirements:
#    - Install FFmpeg on your system
#    - Ensure it's available in your PATH

# 4. GPU Support (Optional):
#    - Install CUDA toolkit for GPU acceleration
#    - Install PyTorch with CUDA support
#    - Ensure adequate GPU memory (8GB+ recommended)

# 5. Security Notes:
#    - Never commit this file with real values to version control
#    - Use strong, unique secrets for production
#    - Rotate keys regularly
#    - Use environment-specific configurations
