"use client"

import * as React from "react"
import * as ProgressPrimitive from "@radix-ui/react-progress"

import { cn } from "@/lib/utils"

// Dynamic color calculation based on percentage
const getDynamicProgressColor = (value: number, colorScheme: 'default' | 'inverse' = 'default') => {
  const percentage = Math.max(0, Math.min(100, value || 0))

  if (colorScheme === 'inverse') {
    // For metrics where lower is better (like error rates)
    if (percentage >= 80) return "bg-red-500"
    if (percentage >= 60) return "bg-orange-500"
    if (percentage >= 40) return "bg-yellow-500"
    return "bg-green-500"
  } else {
    // For metrics where higher is better (like accuracy, or resource usage warnings)
    if (percentage >= 80) return "bg-red-500"    // Critical
    if (percentage >= 60) return "bg-orange-500" // High
    if (percentage >= 40) return "bg-yellow-500" // Medium
    return "bg-green-500"                        // Good/Low
  }
}

// Get text color for percentage display
const getDynamicTextColor = (value: number, colorScheme: 'default' | 'inverse' = 'default') => {
  const percentage = Math.max(0, Math.min(100, value || 0))

  if (colorScheme === 'inverse') {
    if (percentage >= 80) return "text-red-500"
    if (percentage >= 60) return "text-orange-500"
    if (percentage >= 40) return "text-yellow-500"
    return "text-green-500"
  } else {
    if (percentage >= 80) return "text-red-500"
    if (percentage >= 60) return "text-orange-500"
    if (percentage >= 40) return "text-yellow-500"
    return "text-green-500"
  }
}

interface ProgressProps extends React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root> {
  value?: number
  colorScheme?: 'default' | 'inverse'
  showDynamicColors?: boolean
}

const Progress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  ProgressProps
>(({ className, value, colorScheme = 'default', showDynamicColors = false, ...props }, ref) => {
  const dynamicColorClass = showDynamicColors ? getDynamicProgressColor(value || 0, colorScheme) : "bg-primary"

  return (
    <ProgressPrimitive.Root
      ref={ref}
      className={cn(
        "relative h-4 w-full overflow-hidden rounded-full bg-secondary",
        className
      )}
      {...props}
    >
      <ProgressPrimitive.Indicator
        className={cn(
          "h-full w-full flex-1 transition-all duration-300 ease-in-out",
          dynamicColorClass
        )}
        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}
      />
    </ProgressPrimitive.Root>
  )
})
Progress.displayName = ProgressPrimitive.Root.displayName

export { Progress, getDynamicProgressColor, getDynamicTextColor }
