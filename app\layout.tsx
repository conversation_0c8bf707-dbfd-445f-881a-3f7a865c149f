import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { AuthProvider } from "@/components/auth-provider"
import { Toaster } from 'react-hot-toast'
import FirebaseStatus from "@/components/firebase-status"
import CSPTest from "@/components/csp-test"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Masalit Language AI Training Platform",
  description: "Platform for collecting and managing Masalit language audio recordings and transcriptions",
  generator: 'v0.dev',
  other: {
    // Additional CSP meta tag for Firebase compatibility
    'Content-Security-Policy': process.env.NODE_ENV === 'development'
      ? "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https: http:; style-src 'self' 'unsafe-inline' https: http:; img-src 'self' data: https: http: blob:; font-src 'self' data: https: http:; media-src 'self' blob: https://firebasestorage.googleapis.com https://storage.googleapis.com; connect-src 'self' http://127.0.0.1:8000 http://localhost:8000 http://localhost:3000 https: wss: ws:; frame-src 'self' https: http:; worker-src 'self' blob:; object-src 'none'; base-uri 'self';"
      : "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://www.google.com https://www.gstatic.com https://www.googletagmanager.com https://apis.google.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: https: blob:; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: https://firebasestorage.googleapis.com https://storage.googleapis.com; connect-src 'self' https://buragatechnologies.com https://www.google.com https://firebase.googleapis.com https://securetoken.googleapis.com https://firestore.googleapis.com https://identitytoolkit.googleapis.com https://www.googleapis.com https://storage.googleapis.com https://firebasestorage.googleapis.com; frame-src 'self' https://www.google.com https://www.recaptcha.net; worker-src 'self' blob:; object-src 'none'; base-uri 'self';"
  }
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Suppress runtime.lastError messages from browser extensions
              if (typeof window !== 'undefined') {
                window.addEventListener('error', function(e) {
                  if (e.message && e.message.includes('runtime.lastError')) {
                    e.preventDefault();
                    return false;
                  }
                });

                // Suppress unhandled promise rejections from extensions
                window.addEventListener('unhandledrejection', function(e) {
                  if (e.reason && e.reason.message && e.reason.message.includes('runtime.lastError')) {
                    e.preventDefault();
                    return false;
                  }
                });
              }
            `,
          }}
        />
      </head>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem disableTransitionOnChange>
          <AuthProvider>{children}</AuthProvider>
        </ThemeProvider>
        <Toaster position="top-right" />
        <FirebaseStatus showInDevelopment={true} />
        <CSPTest showInDevelopment={true} />
      </body>
    </html>
  )
}
