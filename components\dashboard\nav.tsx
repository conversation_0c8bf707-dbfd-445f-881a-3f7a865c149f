"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { buttonVariants } from "@/components/ui/button"
import { Mic, Volume2, Activity } from "lucide-react"

interface NavItem {
  title: string
  href: string
  iconType: "mic" | "volume" | "activity"
}

interface DashboardNavProps {
  items: NavItem[]
}

const iconMap = {
  mic: Mic,
  volume: Volume2,
  activity: Activity,
}

export function DashboardNav({ items }: DashboardNavProps) {
  const path = usePathname()

  return (
    <nav className="grid items-start gap-2">
      {items.map((item) => {
        const Icon = iconMap[item.iconType]
        return (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              buttonVariants({ variant: "ghost" }),
              path === item.href
                ? "bg-muted hover:bg-muted"
                : "hover:bg-transparent hover:underline",
              "justify-start"
            )}
          >
            <Icon className="mr-2 h-4 w-4" />
            {item.title}
          </Link>
        )
      })}
    </nav>
  )
} 