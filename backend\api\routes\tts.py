"""
TTS API endpoints
"""

import logging
import tempfile
import os
from fastapi import APIRouter, HTTPException, BackgroundTasks, Response
from fastapi.responses import FileResponse
from pydantic import BaseModel
from typing import Dict, Any, Optional, List

from backend.core.tts.trainer import tts_trainer
from backend.core.tts.engine import tts_engine
from backend.services.firebase_clean import clean_firebase_service

logger = logging.getLogger(__name__)

router = APIRouter()

class TTSTrainingRequest(BaseModel):
    epochs: int = 100
    learning_rate: float = 0.0002
    number_of_samples: int = 100
    batch_size: int = 16
    validation_split: float = 0.1
    early_stopping_patience: int = 10
    use_augmentation: bool = True
    training_timeout: Optional[int] = None

class TTSTrainingResponse(BaseModel):
    message: str
    status: str
    error: Optional[str] = None

class TTSSynthesisRequest(BaseModel):
    text: str
    speaker_id: Optional[int] = None
    speed: float = 1.0
    pitch: float = 1.0
    output_format: str = "wav"

class TTSSynthesisResponse(BaseModel):
    message: str
    audio_url: Optional[str] = None
    duration: Optional[float] = None
    error: Optional[str] = None

# ==================== TRAINING ENDPOINTS ====================

@router.post("/training/start", response_model=TTSTrainingResponse)
async def start_tts_training(request: TTSTrainingRequest, background_tasks: BackgroundTasks):
    """Start TTS model training"""
    try:
        # Convert request to dict
        training_settings = request.model_dump()
        
        # Save settings to clean Firebase structure
        from datetime import datetime
        clean_firebase_service.initialize()
        clean_firebase_service.update_settings('tts_training', {
            **training_settings,
            'updated_at': datetime.now().isoformat()
        })
        
        # Start training
        result = await tts_trainer.start_training(training_settings)
        
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return TTSTrainingResponse(
            message=result["message"],
            status=result["status"]
        )
        
    except Exception as e:
        logger.error(f"Error starting TTS training: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/training/stop", response_model=TTSTrainingResponse)
async def stop_tts_training():
    """Stop current TTS training"""
    try:
        result = await tts_trainer.stop_training()
        
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return TTSTrainingResponse(
            message=result["message"],
            status="stopped"
        )
        
    except Exception as e:
        logger.error(f"Error stopping TTS training: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/training/status")
async def get_tts_training_status():
    """Get current TTS training status"""
    try:
        status = await tts_trainer.get_training_status()
        return status

    except Exception as e:
        logger.error(f"Error getting TTS training status: {e}")
        return {
            "status": "error",
            "progress": 0,
            "current_epoch": 0,
            "total_epochs": 0,
            "error": str(e)
        }

@router.get("/training/settings")
async def get_tts_training_settings():
    """Get current TTS training settings"""
    try:
        clean_firebase_service.initialize()
        settings = clean_firebase_service.get_settings('tts_training')
        return settings

    except Exception as e:
        logger.error(f"Error getting TTS training settings: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/training/settings")
async def update_tts_training_settings(request: TTSTrainingRequest):
    """Update TTS training settings"""
    try:
        settings = request.model_dump()
        
        # Save to clean Firebase structure
        from datetime import datetime
        clean_firebase_service.initialize()
        clean_firebase_service.update_settings('tts_training', {
            **settings,
            'updated_at': datetime.now().isoformat()
        })
        
        return {"message": "TTS training settings updated successfully", "settings": settings}
        
    except Exception as e:
        logger.error(f"Error updating TTS training settings: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ==================== SYNTHESIS ENDPOINTS ====================

@router.post("/synthesize", response_model=TTSSynthesisResponse)
async def synthesize_speech(request: TTSSynthesisRequest):
    """Synthesize speech from text"""
    try:
        # Validate text
        validation = tts_engine.validate_text(request.text)
        if not validation["valid"]:
            raise HTTPException(status_code=400, detail=validation["error"])
        
        # Load model if not already loaded
        if not tts_engine.is_loaded:
            if not tts_engine.load_model():
                raise HTTPException(status_code=503, detail="TTS model not available")
        
        # Synthesize audio
        audio_data = tts_engine.synthesize(
            text=request.text,
            speaker_id=request.speaker_id,
            speed=request.speed,
            pitch=request.pitch
        )
        
        if audio_data is None:
            raise HTTPException(status_code=500, detail="Speech synthesis failed")
        
        # Save to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=f".{request.output_format}") as temp_file:
            temp_file.write(audio_data)
            temp_path = temp_file.name
        
        # Return file response
        return FileResponse(
            path=temp_path,
            media_type=f"audio/{request.output_format}",
            filename=f"synthesis_{int(datetime.now().timestamp())}.{request.output_format}"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error synthesizing speech: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/synthesize/file")
async def synthesize_to_file(request: TTSSynthesisRequest):
    """Synthesize speech and return file path"""
    try:
        # Validate text
        validation = tts_engine.validate_text(request.text)
        if not validation["valid"]:
            raise HTTPException(status_code=400, detail=validation["error"])
        
        # Load model if not already loaded
        if not tts_engine.is_loaded:
            if not tts_engine.load_model():
                raise HTTPException(status_code=503, detail="TTS model not available")
        
        # Create output file path
        from datetime import datetime
        timestamp = int(datetime.now().timestamp())
        output_filename = f"tts_synthesis_{timestamp}.{request.output_format}"
        output_path = os.path.join(tempfile.gettempdir(), output_filename)
        
        # Synthesize to file
        success = tts_engine.synthesize_to_file(
            text=request.text,
            output_path=output_path,
            speaker_id=request.speaker_id,
            speed=request.speed,
            pitch=request.pitch
        )
        
        if not success:
            raise HTTPException(status_code=500, detail="Speech synthesis failed")
        
        return TTSSynthesisResponse(
            message="Speech synthesized successfully",
            audio_url=f"/tmp/{output_filename}",
            duration=validation.get("estimated_duration")
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error synthesizing speech to file: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/validate/text")
async def validate_text_for_tts(text: str):
    """Validate text for TTS synthesis"""
    try:
        validation = tts_engine.validate_text(text)
        return validation
        
    except Exception as e:
        logger.error(f"Error validating text: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ==================== MODEL MANAGEMENT ENDPOINTS ====================

@router.get("/model/info")
async def get_tts_model_info():
    """Get current TTS model information"""
    try:
        model_info = tts_engine.get_model_info()
        if model_info:
            return model_info
        else:
            return {"message": "No TTS model loaded", "is_loaded": False}
            
    except Exception as e:
        logger.error(f"Error getting TTS model info: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/model/load")
async def load_tts_model(model_path: Optional[str] = None):
    """Load TTS model"""
    try:
        success = tts_engine.load_model(model_path)
        if success:
            return {"message": "TTS model loaded successfully", "is_loaded": True}
        else:
            raise HTTPException(status_code=503, detail="Failed to load TTS model")
            
    except Exception as e:
        logger.error(f"Error loading TTS model: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/model/unload")
async def unload_tts_model():
    """Unload TTS model to free memory"""
    try:
        tts_engine.unload_model()
        return {"message": "TTS model unloaded successfully", "is_loaded": False}
        
    except Exception as e:
        logger.error(f"Error unloading TTS model: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/speakers")
async def get_available_speakers():
    """Get list of available speakers"""
    try:
        speakers = tts_engine.get_available_speakers()
        return {"speakers": speakers, "total": len(speakers)}
        
    except Exception as e:
        logger.error(f"Error getting available speakers: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/data/stats")
async def get_tts_training_data_stats():
    """Get statistics about available TTS training data"""
    try:
        # Get TTS training data using clean service
        clean_firebase_service.initialize()
        training_data = clean_firebase_service.get_training_data(model_type='tts')
        
        # Calculate statistics
        total_samples = len(training_data)
        total_duration = sum(item.get('duration', 0) for item in training_data)
        
        # Gender distribution
        gender_stats = {}
        for item in training_data:
            gender = item.get('gender', 'unknown')
            gender_stats[gender] = gender_stats.get(gender, 0) + 1
        
        # Language/dialect distribution
        language_stats = {}
        for item in training_data:
            language = item.get('language', 'unknown')
            language_stats[language] = language_stats.get(language, 0) + 1
        
        return {
            "total_samples": total_samples,
            "total_duration_seconds": total_duration,
            "total_duration_hours": round(total_duration / 3600, 2),
            "gender_distribution": gender_stats,
            "language_distribution": language_stats,
            "average_duration": round(total_duration / total_samples, 2) if total_samples > 0 else 0
        }
        
    except Exception as e:
        logger.error(f"Error getting TTS training data stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))
