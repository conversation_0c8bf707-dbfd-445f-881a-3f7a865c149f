# Windows 11 Development Setup Guide

## 🪟 Windows 11 Laptop Development Environment

This guide is specifically for developing the Masalit AI platform on your Windows 11 laptop.

## 📋 Prerequisites

### Required Software
- ✅ **Python 3.11.6** (already installed)
- ✅ **Node.js 18+** with npm/pnpm
- ✅ **Git** for version control
- ⚠️ **OpenSSL** (for HTTPS development - optional)

### Install OpenSSL (Optional - for HTTPS development)
Choose one method:

**Method 1: Direct Download**
1. Download from: https://slproweb.com/products/Win32OpenSSL.html
2. Install "Win64 OpenSSL v3.x.x"
3. Add to PATH: `C:\Program Files\OpenSSL-Win64\bin`

**Method 2: Chocolatey**
```powershell
# Install Chocolatey first (if not installed)
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# Install OpenSSL
choco install openssl
```

**Method 3: Scoop**
```powershell
# Install Scoop first (if not installed)
Set-ExecutionPolicy RemoteSigned -Scope CurrentUser
irm get.scoop.sh | iex

# Install OpenSSL
scoop install openssl
```

## 🚀 Quick Start

### Option 1: Simple HTTP Development (Recommended)
```batch
# Double-click or run in Command Prompt
start-development.bat
```

**Notes:**
- The backend uses a separate environment file (`backend/.env`) to avoid conflicts with frontend variables
- After CSP changes, restart the development server for changes to take effect

## 🔥 Firebase Connectivity Issues

If you see Firebase network errors during development:

### **Common Error:**
```
FirebaseError: Firebase: Error (auth/network-request-failed)
```

### **Solutions:**

1. **Check Internet Connection** - Ensure you have a stable internet connection
2. **Firebase Project Access** - Verify the Firebase project `kana-masark-255aa` is accessible
3. **Development Mode** - The app includes better error handling for development
4. **Firebase Status** - Check the Firebase status indicator in the bottom-right corner

### **Development Features:**
- ✅ **reCAPTCHA Bypass** - Automatically bypassed on localhost
- ✅ **Better Error Messages** - Clear network error explanations
- ✅ **Firebase Status Indicator** - Real-time connection status (bottom-right)
- ✅ **CSP Status Indicator** - Content Security Policy test results (bottom-left)
- ✅ **Graceful Fallbacks** - App continues to work with limited functionality
- ✅ **Permissive CSP** - Development-friendly Content Security Policy

## 🔒 Content Security Policy (CSP)

The app includes different CSP configurations for development and production:

### **Development CSP:**
- More permissive to allow Firebase and Google services
- Allows `http:` and `https:` connections
- Includes localhost and development servers

### **Production CSP:**
- Strict security policy
- Only allows specific Firebase and Google domains
- Optimized for `buragatechnologies.com`

### **Status Indicators:**

**Firebase Status (bottom-right):**
- 🟢 Auth: connected/disconnected
- 🟢 Firestore: connected/disconnected

**CSP Status (bottom-left):**
- ✅ Firebase: allowed/blocked
- ✅ Google: allowed/blocked

### **Common Issues Fixed:**
- ✅ `connectivityState` import error resolved
- ✅ CSP violations for Firebase domains fixed
- ✅ Development-friendly error handling added

### Option 2: HTTPS Development (Advanced)
```batch
# First-time setup (generates SSL certificates)
setup-ssl-windows.bat

# Then start with HTTPS
start-development-ssl.bat
```

### Stop All Services
```batch
stop-development.bat
```

## 📁 Windows-Specific Files

### Development Scripts
- `start-development.bat` - Start HTTP development environment
- `start-development-ssl.bat` - Start HTTPS development environment
- `setup-ssl-windows.bat` - Generate SSL certificates for HTTPS
- `stop-development.bat` - Stop all development services

### Configuration
- `.env.local` - Development environment variables
- `logs/` - Development logs directory (auto-created)

## 🔧 Development Workflow

### 1. Daily Development
```batch
# Start development environment
start-development.bat

# Your services will be available at:
# Frontend: http://localhost:3000
# Backend:  http://127.0.0.1:8000
# API Docs: http://127.0.0.1:8000/docs
```

### 2. Code Changes
- Edit files in VS Code or your preferred editor
- Frontend auto-reloads on changes (Next.js hot reload)
- Backend auto-reloads on changes (uvicorn --reload)

### 3. Testing Features
- Access frontend at `http://localhost:3000`
- Test API endpoints at `http://127.0.0.1:8000/docs`
- Check logs in the `logs/` directory

### 4. Stop Development
```batch
# Stop all services
stop-development.bat

# Or simply close the service windows
```

## 🌐 Environment Configuration

### Development URLs (.env.local)
```env
NEXT_PUBLIC_BACKEND_URL=http://127.0.0.1:8000
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://127.0.0.1:8000
```

### HTTPS Development (Optional)
If you set up SSL certificates:
```env
NEXT_PUBLIC_BACKEND_URL=https://buragatechnologies.com:8000
NEXT_PUBLIC_APP_URL=https://buragatechnologies.com
```

## 🔍 Troubleshooting

### Port Already in Use
```batch
# Check what's using the ports
netstat -ano | findstr :3000
netstat -ano | findstr :8000

# Kill processes if needed
taskkill /PID <process_id> /F
```

### Python/Node Not Found
```batch
# Check if Python is in PATH
python --version

# Check if Node.js is in PATH
node --version
npm --version

# Add to PATH if needed via System Properties > Environment Variables
```

### SSL Certificate Issues
```batch
# Regenerate certificates
setup-ssl-windows.bat

# Or use HTTP development instead
start-development.bat
```

### Permission Issues
```batch
# Run Command Prompt as Administrator if needed
# Right-click Command Prompt > "Run as administrator"
```

## 📊 Performance Tips

### Windows 11 Optimization
1. **Exclude project folder from Windows Defender**
   - Windows Security > Virus & threat protection
   - Add exclusion for your project folder

2. **Use Windows Terminal** (better than Command Prompt)
   - Install from Microsoft Store
   - Better performance and features

3. **Use pnpm instead of npm** (faster)
   ```batch
   npm install -g pnpm
   ```

### Development Tools
- **VS Code Extensions:**
  - Python
  - TypeScript and JavaScript
  - Tailwind CSS IntelliSense
  - ES7+ React/Redux/React-Native snippets

## 🔄 Git Workflow

### Development Cycle
```batch
# 1. Start development
start-development.bat

# 2. Make changes and test

# 3. Commit changes
git add .
git commit -m "Your changes"

# 4. Push to GitHub
git push origin main

# 5. Deploy to production server (Ubuntu)
# (Run deploy-production.sh on the server)
```

## 📱 Mobile Testing

### Test on Mobile Devices
```batch
# Find your Windows IP address
ipconfig

# Access from mobile device on same network:
# http://YOUR_WINDOWS_IP:3000
# Example: http://*************:3000
```

## 🎯 Next Steps

1. **Start developing**: Run `start-development.bat`
2. **Make changes**: Edit code in your preferred editor
3. **Test locally**: Use the development URLs
4. **Commit & push**: Use Git to save your changes
5. **Deploy**: Run deployment script on Ubuntu server

Your Windows 11 development environment is now optimized for the Masalit AI platform! 🚀
