"use client"

import { useState, useEffect } from "react"
import { useToast } from "@/hooks/use-toast"
import { checkServerStatus } from "@/lib/asr"
import { TrainingStatus, TrainingSettings, ModelInfo } from "@/types/asr"
import { ArrowLeft, AlertCircle } from "lucide-react"
import Link from "next/link"

// Import modular components
import { ASRHeroSection } from "./components/ASRHeroSection"
import { TrainingControlPanel } from "./components/TrainingControlPanel"
import { DashboardCards } from "./components/DashboardCards"
import { QuickActionsBar } from "./components/QuickActionsBar"
import { TrainingDialogs } from "./components/TrainingDialogs"

export default function ASRTrainingPage() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(true)
  const [isServerAvailable, setIsServerAvailable] = useState(true)
  const [status, setStatus] = useState<TrainingStatus>({
    status: "not_started",
    current_epoch: 0,
    total_epochs: 0,
    progress: 0
  })
  const [settings, setSettings] = useState<TrainingSettings>({
    epochs: 5,
    learning_rate: 0.001,
    model_name: "small",
    validation_split: 0.2,
    early_stopping_patience: 3,
    use_augmentation: false,
    number_of_samples: 1,
    eval_steps: 100,
    training_timeout: 7200,
    use_existing_model: true
  })
  const [modelInfo, setModelInfo] = useState<ModelInfo | null>(null)

  // Dialog states
  const [isAdvancedSettingsOpen, setIsAdvancedSettingsOpen] = useState(false)
  const [isResetConfirmOpen, setIsResetConfirmOpen] = useState(false)
  const [isModelManagementOpen, setIsModelManagementOpen] = useState(false)
  const [isModelCompareOpen, setIsModelCompareOpen] = useState(false)
  const [isScheduleOpen, setIsScheduleOpen] = useState(false)

  // Handler functions for buttons
  const handleAdvancedSettings = () => {
    setIsAdvancedSettingsOpen(true)
  }

  const handleResetTraining = () => {
    setIsResetConfirmOpen(true)
  }

  const handleModelManagement = () => {
    setIsModelManagementOpen(true)
  }

  const handleModelCompare = () => {
    setIsModelCompareOpen(true)
  }

  const handleScheduleClick = () => {
    setIsScheduleOpen(true)
  }

  // Fetch model information
  const fetchModelInfo = async () => {
    try {
      const response = await fetch('/api/asr/model/info', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        const data = await response.json()
        setModelInfo(data)
      } else {
        console.warn("Backend not available for model info")
        setModelInfo(null)
      }
    } catch (error) {
      console.warn("Error fetching model info (backend may not be running):", error)
      setModelInfo(null)
    }
  }

  // Check server availability and fetch model info on mount
  useEffect(() => {
    const checkServer = async () => {
      try {
        const available = await checkServerStatus()
        setIsServerAvailable(available)

        if (available) {
          await fetchModelInfo()
        }
      } catch (error) {
        console.error("Error checking server status:", error)
        setIsServerAvailable(false)
      } finally {
        setIsLoading(false)
      }
    }

    checkServer()
  }, [])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        <div className="container mx-auto p-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center space-y-4">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-muted-foreground">Loading ASR Training Hub...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!isServerAvailable) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        <div className="container mx-auto p-6">
          <div className="flex items-center justify-between mb-6">
            <Link href="/dashboard/ai" className="flex items-center text-muted-foreground hover:text-foreground transition-colors">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to AI Dashboard
            </Link>
          </div>
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center space-y-4 max-w-md">
              <div className="rounded-full bg-red-100 p-4 w-16 h-16 mx-auto flex items-center justify-center">
                <AlertCircle className="h-8 w-8 text-red-600" />
              </div>
              <h2 className="text-xl font-semibold">Server Unavailable</h2>
              <p className="text-muted-foreground">
                The ASR training server is currently unavailable. Please check your backend connection and try again.
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      <div className="container mx-auto p-6 space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <Link href="/dashboard/ai" className="flex items-center text-muted-foreground hover:text-foreground transition-colors">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to AI Dashboard
          </Link>
        </div>

        {/* Hero Section */}
        <ASRHeroSection
          isServerAvailable={isServerAvailable}
          status={status}
          modelInfo={modelInfo}
        />

        {/* Training Control Panel */}
        <TrainingControlPanel
          status={status}
          settings={settings}
          onSettingsChange={setSettings}
          onStatusChange={setStatus}
          isServerAvailable={isServerAvailable}
          modelInfo={modelInfo}
          onModelInfoUpdate={fetchModelInfo}
        />

        {/* Dashboard Cards */}
        <DashboardCards
          status={status}
          isServerAvailable={isServerAvailable}
          onManageClick={handleModelManagement}
          onCompareClick={handleModelCompare}
          onScheduleClick={handleScheduleClick}
          modelInfo={modelInfo}
        />

        {/* Quick Actions Bar */}
        <QuickActionsBar
          onAdvancedSettingsClick={handleAdvancedSettings}
          onResetTrainingClick={handleResetTraining}
        />

        {/* Training Dialogs */}
        <TrainingDialogs
          status={status}
          settings={settings}
          onSettingsChange={setSettings}
          onStatusChange={setStatus}
          isAdvancedSettingsOpen={isAdvancedSettingsOpen}
          setIsAdvancedSettingsOpen={setIsAdvancedSettingsOpen}
          isResetConfirmOpen={isResetConfirmOpen}
          setIsResetConfirmOpen={setIsResetConfirmOpen}
          isModelManagementOpen={isModelManagementOpen}
          setIsModelManagementOpen={setIsModelManagementOpen}
          isModelCompareOpen={isModelCompareOpen}
          setIsModelCompareOpen={setIsModelCompareOpen}
          isScheduleOpen={isScheduleOpen}
          setIsScheduleOpen={setIsScheduleOpen}
        />
      </div>
    </div>
  )
}
