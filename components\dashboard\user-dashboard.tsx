"use client"

import { useEffect, useState } from "react"
import { useAuth } from "@/components/auth-provider"
import { useFocusedLanguage } from "@/components/focused-language-provider"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { Upload, History, User, Clock, Trophy, Mic, TrendingUp, CheckCircle, XCircle, AlertCircle, Zap, FileAudio, BarChart3 } from "lucide-react"
import { collection, query, where, getDocs, orderBy, limit, getDoc, doc as firestoreDoc } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { Loader2 } from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Bar, Doughnut } from "react-chartjs-2"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  ArcElement,
} from "chart.js"

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
)

interface UserStats {
  totalMinutes: number
  totalRecordings: number
  approvedRecordings: number
  pendingRecordings: number
  rejectedRecordings: number
  averageDuration: number
  lastUploadDate: Date | null
  approvalRate: number
}

interface RecentUpload {
  id: string
  title: string
  user_id: string
  username: string
  name: string
  created_at: Date
  duration: number
  action: 'pending' | 'approved' | 'rejected'
}

// Helper function to safely convert timestamps to Date objects
const safeToDate = (timestamp: any): Date => {
  if (!timestamp) return new Date()
  if (timestamp instanceof Date) return timestamp
  if (typeof timestamp === 'string') return new Date(timestamp)
  if (timestamp.toDate && typeof timestamp.toDate === 'function') return timestamp.toDate()
  if (timestamp.seconds) return new Date(timestamp.seconds * 1000)
  return new Date()
}

export function UserDashboard() {
  const { user } = useAuth()
  const { t, isRTL } = useFocusedLanguage()
  const [loading, setLoading] = useState(true)
  const [userStats, setUserStats] = useState<UserStats>({
    totalMinutes: 0,
    totalRecordings: 0,
    approvedRecordings: 0,
    pendingRecordings: 0,
    rejectedRecordings: 0,
    averageDuration: 0,
    lastUploadDate: null,
    approvalRate: 0
  })
  const [recentUploads, setRecentUploads] = useState<RecentUpload[]>([])
  const [chartData, setChartData] = useState({
    statusDistribution: {
      labels: [t('approved'), t('pending'), t('rejected')],
      datasets: [{
        data: [0, 0, 0],
        backgroundColor: ['#22c55e', '#eab308', '#ef4444'],
      }]
    },
    monthlyContributions: {
      labels: [],
      datasets: [{
        label: t('monthlyContributions'),
        data: [],
        backgroundColor: '#3b82f6',
      }]
    }
  })

  useEffect(() => {
    const fetchUserData = async () => {
      if (!user) return

      try {
        // Fetch user's recordings
        const userRecordingsQuery = query(
          collection(db, "audio"),
          where("user_id", "==", user.id)
        )
        const userRecordingsSnapshot = await getDocs(userRecordingsQuery)
        const userRecordings = []

        for (const audioDoc of userRecordingsSnapshot.docs) {
          const audioData = {
            id: audioDoc.id,
            ...audioDoc.data()
          }

          // Fetch review status from subcollection
          try {
            const reviewStatusDoc = await getDoc(firestoreDoc(db, 'audio', audioDoc.id, 'review', 'status'))
            if (reviewStatusDoc.exists()) {
              audioData.review = {
                status: reviewStatusDoc.data()
              }
            }
          } catch (error) {
            console.warn(`Could not fetch review status for audio ${audioDoc.id}:`, error)
          }

          userRecordings.push(audioData)
        }

        // Calculate user stats with enhanced review status
        const totalMinutes = Math.round(userRecordings.reduce((acc, r) => acc + (r.duration || 0), 0) / 60)
        const approvedCount = userRecordings.filter(r => {
          const action = r.review?.status?.action || r.action
          return action === 'approved'
        }).length
        const pendingCount = userRecordings.filter(r => {
          const action = r.review?.status?.action || r.action
          return !action || action === 'pending'
        }).length
        const rejectedCount = userRecordings.filter(r => {
          const action = r.review?.status?.action || r.action
          return action === 'rejected'
        }).length
        const totalCount = userRecordings.length
        const averageDuration = totalCount > 0 
          ? Math.round(userRecordings.reduce((acc, r) => acc + (r.duration || 0), 0) / totalCount)
          : 0
        const sortedRecordings = userRecordings.sort((a, b) => safeToDate(b.created_at).getTime() - safeToDate(a.created_at).getTime())
        const lastUpload = sortedRecordings.length > 0 ? safeToDate(sortedRecordings[0].created_at) : null
        const approvalRate = totalCount > 0 ? Math.round((approvedCount / totalCount) * 100) : 0

        setUserStats({
          totalMinutes,
          totalRecordings: totalCount,
          approvedRecordings: approvedCount,
          pendingRecordings: pendingCount,
          rejectedRecordings: rejectedCount,
          averageDuration,
          lastUploadDate: lastUpload,
          approvalRate
        })

        // Fetch recent uploads (user's own) with review status
        const recentUploadsQuery = query(
          collection(db, "audio"),
          where("user_id", "==", user.id),
          orderBy("created_at", "desc"),
          limit(5)
        )
        const recentUploadsSnapshot = await getDocs(recentUploadsQuery)
        const recentUploadsData = []

        for (const uploadDoc of recentUploadsSnapshot.docs) {
          const data = uploadDoc.data()

          // Fetch review status from subcollection
          let reviewStatus = 'pending'
          try {
            const reviewStatusDoc = await getDoc(firestoreDoc(db, 'audio', uploadDoc.id, 'review', 'status'))
            if (reviewStatusDoc.exists()) {
              reviewStatus = reviewStatusDoc.data().action || 'pending'
            }
          } catch (error) {
            console.warn(`Could not fetch review status for recent upload ${uploadDoc.id}:`, error)
          }

          // Get the best available title with enhanced fallbacks
          let audioTitle = data.title || data.filename || data.name

          // If still no title, try to get transcription content for a preview
          if (!audioTitle || audioTitle === 'Untitled') {
            try {
              const transcriptionDoc = await getDoc(firestoreDoc(db, 'audio', uploadDoc.id, 'transcriptions', 'primary'))
              if (transcriptionDoc.exists()) {
                const transcriptionData = transcriptionDoc.data()
                if (transcriptionData.content) {
                  // Use first 30 characters of transcription as title
                  const preview = transcriptionData.content.substring(0, 30).trim()
                  if (preview) {
                    const ellipsis = transcriptionData.content.length > 30 ? '...' : ''
                    audioTitle = `"${preview}${ellipsis}"`
                  }
                }
              }
            } catch (error) {
              console.warn(`Could not fetch transcription for audio ${uploadDoc.id}:`, error)
            }
          }

          // Final fallback
          if (!audioTitle || audioTitle === 'Untitled') {
            const timestamp = safeToDate(data.created_at)
            audioTitle = `Recording ${timestamp.toLocaleDateString()}`
          }

          recentUploadsData.push({
            id: uploadDoc.id,
            title: audioTitle,
            user_id: data.user_id,
            username: user.username || "You",
            name: user.name || user.username || "You",
            created_at: safeToDate(data.created_at),
            duration: data.duration || 0,
            action: reviewStatus
          })
        }
        setRecentUploads(recentUploadsData)

        // Update chart data
        setChartData(prev => ({
          ...prev,
          statusDistribution: {
            labels: [t('approved'), t('pending'), t('rejected')],
            datasets: [{
              ...prev.statusDistribution.datasets[0],
              data: [approvedCount, pendingCount, rejectedCount]
            }]
          }
        }))

        // Calculate monthly contributions
        const monthlyData = Array(6).fill(0)
        const monthLabels = Array(6).fill('')
        const now = new Date()
        
        for (let i = 5; i >= 0; i--) {
          const month = new Date(now.getFullYear(), now.getMonth() - i, 1)
          monthLabels[5-i] = month.toLocaleString(isRTL ? 'ar' : 'en', { month: 'short' })
          
          const monthStart = new Date(month.getFullYear(), month.getMonth(), 1)
          const monthEnd = new Date(month.getFullYear(), month.getMonth() + 1, 0)
          
          const monthContributions = userRecordings.filter(r => {
            const date = safeToDate(r.created_at)
            return date >= monthStart && date <= monthEnd
          }).length
          
          monthlyData[5-i] = monthContributions
        }

        setChartData(prev => ({
          ...prev,
          monthlyContributions: {
            labels: monthLabels,
            datasets: [{
              label: t('monthlyContributions'),
              data: monthlyData,
              backgroundColor: '#3b82f6',
            }]
          }
        }))

      } catch (error) {
        console.error("Error fetching user data:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchUserData()
  }, [user, t, isRTL])

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Welcome Section */}
      <div className="text-center mb-8">
        <h1 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
          {t('welcomeBack')}, {user?.name || user?.username}!
        </h1>
        <p className="text-muted-foreground text-lg">
          {t('quickActions')}
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Link href="/dashboard/upload">
          <Card className="hover:shadow-lg transition-all duration-300 hover:scale-105 cursor-pointer border-0 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-950/20 dark:to-indigo-950/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-blue-700 dark:text-blue-300">{t('uploadAudio')}</CardTitle>
              <div className="rounded-full bg-blue-100 dark:bg-blue-900/30 p-2">
                <Upload className="h-5 w-5 text-blue-600" />
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-blue-600 dark:text-blue-400">Enhanced with auto-save, drag-drop & mobile optimization</CardDescription>
            </CardContent>
          </Card>
        </Link>


        
        <Link href="/dashboard/history">
          <Card className="hover:shadow-lg transition-all duration-300 hover:scale-105 cursor-pointer border-0 bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-950/20 dark:to-emerald-950/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-green-700 dark:text-green-300">{t('viewHistory')}</CardTitle>
              <div className="rounded-full bg-green-100 dark:bg-green-900/30 p-2">
                <History className="h-5 w-5 text-green-600" />
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-green-600 dark:text-green-400">Enhanced with advanced search, favorites & auto-save edits</CardDescription>
            </CardContent>
          </Card>
        </Link>
        
        <Link href="/dashboard/profile">
          <Card className="hover:shadow-lg transition-all duration-300 hover:scale-105 cursor-pointer border-0 bg-gradient-to-br from-purple-50 to-violet-100 dark:from-purple-950/20 dark:to-violet-950/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-purple-700 dark:text-purple-300">{t('viewProfile')}</CardTitle>
              <div className="rounded-full bg-purple-100 dark:bg-purple-900/30 p-2">
                <User className="h-5 w-5 text-purple-600" />
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-purple-600 dark:text-purple-400">{t('viewProfileDesc')}</CardDescription>
            </CardContent>
          </Card>
        </Link>
      </div>

      {/* User Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-0 bg-gradient-to-br from-orange-50 to-amber-100 dark:from-orange-950/20 dark:to-amber-950/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-orange-700 dark:text-orange-300">{t('totalMinutes')}</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-800 dark:text-orange-200">{userStats.totalMinutes}</div>
            <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">
              {t('avgDuration')} {userStats.averageDuration}s
            </p>
          </CardContent>
        </Card>
        
        <Card className="border-0 bg-gradient-to-br from-blue-50 to-cyan-100 dark:from-blue-950/20 dark:to-cyan-950/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-700 dark:text-blue-300">{t('totalRecordings')}</CardTitle>
            <Mic className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-800 dark:text-blue-200">{userStats.totalRecordings}</div>
            <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
              {t('lastUpload')}: {userStats.lastUploadDate ? new Date(userStats.lastUploadDate).toLocaleDateString(isRTL ? 'ar' : 'en') : t('never')}
            </p>
          </CardContent>
        </Card>
        
        <Card className="border-0 bg-gradient-to-br from-green-50 to-teal-100 dark:from-green-950/20 dark:to-teal-950/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-700 dark:text-green-300">{t('approvalRate')}</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-800 dark:text-green-200">{userStats.approvalRate}%</div>
            <Progress value={userStats.approvalRate} className="mt-2" />
          </CardContent>
        </Card>
        
        <Card className="border-0 bg-gradient-to-br from-purple-50 to-pink-100 dark:from-purple-950/20 dark:to-pink-950/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-700 dark:text-purple-300">{t('statusDistribution')}</CardTitle>
            <AlertCircle className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">{t('approved')}</span>
                </div>
                <span className="text-sm font-medium">{userStats.approvedRecordings}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-yellow-500" />
                  <span className="text-sm">{t('pending')}</span>
                </div>
                <span className="text-sm font-medium">{userStats.pendingRecordings}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <XCircle className="h-4 w-4 text-red-500" />
                  <span className="text-sm">{t('rejected')}</span>
                </div>
                <span className="text-sm font-medium">{userStats.rejectedRecordings}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-xl">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              {t('statusDistributionChart')}
            </CardTitle>
            <CardDescription>{t('statusDistributionDesc')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] flex items-center justify-center">
              <Doughnut
                data={chartData.statusDistribution}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'bottom',
                    },
                  },
                }}
              />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-xl">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              {t('monthlyContributions')}
            </CardTitle>
            <CardDescription>{t('monthlyContributionsDesc')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <Bar
                data={chartData.monthlyContributions}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      display: false,
                    },
                  },
                  scales: {
                    y: {
                      beginAtZero: true,
                      ticks: {
                        stepSize: 1,
                      },
                    },
                  },
                }}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Uploads */}
      <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileAudio className="h-5 w-5" />
            {t('recentUploads')}
          </CardTitle>
          <CardDescription>{t('recentUploadsDesc')}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentUploads.length > 0 ? (
              recentUploads.map((upload) => (
                <div key={upload.id} className="flex items-center justify-between p-4 rounded-lg bg-gray-50 dark:bg-gray-800/50">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">{upload.title}</p>
                    <p className="text-xs text-muted-foreground">
                      {upload.created_at.toLocaleDateString(isRTL ? 'ar' : 'en')}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge
                      variant={upload.action === 'approved' ? 'default' : upload.action === 'pending' ? 'secondary' : 'destructive'}
                      className="text-xs"
                    >
                      {upload.action === 'approved' ? t('approved') : upload.action === 'pending' ? t('pending') : t('rejected')}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {upload.duration}s
                    </span>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <FileAudio className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>{t('uploadAudioDesc')}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
