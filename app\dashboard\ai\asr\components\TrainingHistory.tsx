import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { useASR } from "@/lib/asr"

export function TrainingHistory() {
  const { stats } = useASR()
  const lastValidation = stats?.last_validation

  if (!lastValidation) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Training History</CardTitle>
          <CardDescription>Recent training and validation results</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground py-4">
            No validation results available
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Training History</CardTitle>
        <CardDescription>Recent training and validation results</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="text-sm text-muted-foreground">
            Last validated: {new Date(lastValidation.created_at).toLocaleString()}
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="text-sm font-medium">Accuracy</div>
              <div className="text-2xl font-bold">
                {Math.round(lastValidation.accuracy * 100)}%
              </div>
              <Progress value={lastValidation.accuracy * 100} />
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium">Confidence</div>
              <div className="text-2xl font-bold">
                {Math.round(lastValidation.confidence * 100)}%
              </div>
              <Progress value={lastValidation.confidence * 100} />
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium">Word Error Rate (WER)</div>
              <div className="text-2xl font-bold">
                {Math.round(lastValidation.wer * 100)}%
              </div>
              <Progress value={lastValidation.wer * 100} className="bg-red-100" />
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium">Character Error Rate (CER)</div>
              <div className="text-2xl font-bold">
                {Math.round(lastValidation.cer * 100)}%
              </div>
              <Progress value={lastValidation.cer * 100} className="bg-red-100" />
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium">Sentence Error Rate (SER)</div>
              <div className="text-2xl font-bold">
                {Math.round(lastValidation.ser * 100)}%
              </div>
              <Progress value={lastValidation.ser * 100} className="bg-red-100" />
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium">Samples Tested</div>
              <div className="text-2xl font-bold">
                {lastValidation.samples_tested}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 