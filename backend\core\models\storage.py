"""
Model Storage Management

Handles local and cloud storage operations for AI models
"""

import os
import json
import shutil
import tarfile
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

from backend.config.settings import settings
from backend.services.gcs import gcs_service
from .types import ModelMetadata, ModelType

logger = logging.getLogger(__name__)


class ModelStorage:
    """Unified model storage management"""
    
    def __init__(self):
        self.local_base_path = Path(settings.FINAL_MODEL_DIR)
        self.temp_path = Path(settings.TEMP_DIR)
        self.ensure_directories()
    
    def ensure_directories(self):
        """Ensure required directories exist"""
        self.local_base_path.mkdir(parents=True, exist_ok=True)
        self.temp_path.mkdir(parents=True, exist_ok=True)
        
        # Create subdirectories for each model type
        for model_type in ModelType:
            (self.local_base_path / model_type.value).mkdir(exist_ok=True)
    
    def get_model_path(self, model_type: ModelType, model_id: str) -> Path:
        """Get local path for a model"""
        return self.local_base_path / model_type.value / model_id
    
    def get_metadata_path(self, model_type: ModelType, model_id: str) -> Path:
        """Get metadata file path for a model"""
        return self.get_model_path(model_type, model_id) / "metadata.json"
    
    async def save_model(
        self, 
        model_type: ModelType,
        model_id: str,
        model_files: Dict[str, Any],
        metadata: ModelMetadata
    ) -> str:
        """Save model files and metadata locally"""
        try:
            model_path = self.get_model_path(model_type, model_id)
            model_path.mkdir(parents=True, exist_ok=True)
            
            # Save model files
            for filename, content in model_files.items():
                file_path = model_path / filename
                
                if hasattr(content, 'save_pretrained'):
                    # Hugging Face model/tokenizer
                    content.save_pretrained(str(file_path.parent))
                elif isinstance(content, (str, bytes)):
                    # Raw content
                    mode = 'w' if isinstance(content, str) else 'wb'
                    with open(file_path, mode) as f:
                        f.write(content)
                else:
                    # JSON serializable content
                    with open(file_path, 'w') as f:
                        json.dump(content, f, indent=2, default=str)
            
            # Save metadata
            metadata.local_path = str(model_path)
            metadata.updated_at = datetime.now()
            await self.save_metadata(model_type, model_id, metadata)
            
            # Calculate size
            size_bytes = self._calculate_directory_size(model_path)
            metadata.size_bytes = size_bytes
            await self.save_metadata(model_type, model_id, metadata)
            
            logger.info(f"Model {model_id} saved locally at {model_path}")
            return str(model_path)
            
        except Exception as e:
            logger.error(f"Error saving model {model_id}: {e}")
            raise
    
    async def load_model(self, model_type: ModelType, model_id: str) -> Optional[Dict[str, Any]]:
        """Load model files from local storage"""
        try:
            model_path = self.get_model_path(model_type, model_id)
            
            if not model_path.exists():
                logger.warning(f"Model {model_id} not found at {model_path}")
                return None
            
            # Load all files in the model directory
            model_files = {}
            for file_path in model_path.rglob('*'):
                if file_path.is_file() and file_path.name != 'metadata.json':
                    relative_path = file_path.relative_to(model_path)
                    
                    try:
                        # Try to load as JSON first
                        with open(file_path, 'r') as f:
                            model_files[str(relative_path)] = json.load(f)
                    except (json.JSONDecodeError, UnicodeDecodeError):
                        # Load as binary if not JSON
                        with open(file_path, 'rb') as f:
                            model_files[str(relative_path)] = f.read()
            
            logger.info(f"Model {model_id} loaded from {model_path}")
            return model_files
            
        except Exception as e:
            logger.error(f"Error loading model {model_id}: {e}")
            raise
    
    async def save_metadata(self, model_type: ModelType, model_id: str, metadata: ModelMetadata):
        """Save model metadata"""
        try:
            metadata_path = self.get_metadata_path(model_type, model_id)
            metadata_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(metadata_path, 'w') as f:
                json.dump(metadata.dict(), f, indent=2, default=str)
            
            logger.debug(f"Metadata saved for model {model_id}")
            
        except Exception as e:
            logger.error(f"Error saving metadata for model {model_id}: {e}")
            raise
    
    async def load_metadata(self, model_type: ModelType, model_id: str) -> Optional[ModelMetadata]:
        """Load model metadata"""
        try:
            metadata_path = self.get_metadata_path(model_type, model_id)
            
            if not metadata_path.exists():
                return None
            
            with open(metadata_path, 'r') as f:
                data = json.load(f)
            
            return ModelMetadata(**data)
            
        except Exception as e:
            logger.error(f"Error loading metadata for model {model_id}: {e}")
            return None
    
    async def delete_model(self, model_type: ModelType, model_id: str) -> bool:
        """Delete model and its metadata"""
        try:
            model_path = self.get_model_path(model_type, model_id)
            
            if model_path.exists():
                shutil.rmtree(model_path)
                logger.info(f"Model {model_id} deleted from {model_path}")
                return True
            else:
                logger.warning(f"Model {model_id} not found for deletion")
                return False
                
        except Exception as e:
            logger.error(f"Error deleting model {model_id}: {e}")
            raise
    
    async def list_models(self, model_type: Optional[ModelType] = None) -> List[ModelMetadata]:
        """List all models or models of a specific type"""
        try:
            models = []
            
            if model_type:
                model_types = [model_type]
            else:
                model_types = list(ModelType)
            
            for mt in model_types:
                type_path = self.local_base_path / mt.value
                if not type_path.exists():
                    continue
                
                for model_dir in type_path.iterdir():
                    if model_dir.is_dir():
                        metadata = await self.load_metadata(mt, model_dir.name)
                        if metadata:
                            models.append(metadata)
            
            # Sort by creation date (newest first)
            models.sort(key=lambda x: x.created_at, reverse=True)
            return models
            
        except Exception as e:
            logger.error(f"Error listing models: {e}")
            raise
    
    async def upload_to_gcs(self, model_type: ModelType, model_id: str) -> Optional[str]:
        """Upload model to Google Cloud Storage"""
        try:
            if not settings.UPLOAD_TO_GCS:
                logger.info("GCS upload disabled")
                return None
            
            model_path = self.get_model_path(model_type, model_id)
            if not model_path.exists():
                raise FileNotFoundError(f"Model {model_id} not found")
            
            # Create archive
            archive_path = self.temp_path / f"{model_id}.tar.gz"
            with tarfile.open(archive_path, "w:gz") as tar:
                tar.add(model_path, arcname=model_id)
            
            # Upload to GCS
            gcs_path = f"models/{model_type.value}/{model_id}.tar.gz"
            
            def upload_sync():
                return gcs_service.upload_model(str(archive_path), model_id, "latest")
            
            loop = asyncio.get_event_loop()
            gcs_url = await loop.run_in_executor(None, upload_sync)
            
            # Clean up archive
            archive_path.unlink()
            
            # Update metadata with GCS path
            metadata = await self.load_metadata(model_type, model_id)
            if metadata:
                metadata.gcs_path = gcs_url
                await self.save_metadata(model_type, model_id, metadata)
            
            logger.info(f"Model {model_id} uploaded to GCS: {gcs_url}")
            return gcs_url
            
        except Exception as e:
            logger.error(f"Error uploading model {model_id} to GCS: {e}")
            raise
    
    def _calculate_directory_size(self, path: Path) -> int:
        """Calculate total size of directory in bytes"""
        total_size = 0
        for file_path in path.rglob('*'):
            if file_path.is_file():
                total_size += file_path.stat().st_size
        return total_size
