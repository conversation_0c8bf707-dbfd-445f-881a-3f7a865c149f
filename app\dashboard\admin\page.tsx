"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/components/auth-provider"
import { useRouter } from "next/navigation"
import { AdminGuard } from "@/components/navigation-guard"
import { useToast } from "@/hooks/use-toast"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Dialog, DialogContent, <PERSON><PERSON>Des<PERSON>, Di<PERSON>Header, <PERSON><PERSON><PERSON><PERSON>le, DialogTrigger } from "@/components/ui/dialog"
import { Loader2, Search, Filter, Users, Activity, BarChart3, Settings, Eye, Edit, Trash2, Download, RefreshCw } from "lucide-react"
import { collection, getDocs, doc, updateDoc, query, where } from "firebase/firestore"
import { db } from "@/lib/firebase"

type User = {
  id: string
  email: string
  name: string
  username: string
  role: "admin" | "user"
  contribution_count?: number
  isDisabled?: boolean
  email_verified?: boolean
  created_at?: string
  updated_at?: string
  profile?: {
    details?: {
      avatar_url?: string
      bio?: string
      language_preferences?: string[]
      email_verified?: boolean
      phone_verified?: boolean
    }
  }
  statistics?: {
    summary?: {
      contribution_count?: number
      audio_uploads?: number
      transcriptions_created?: number
      reviews_completed?: number
      training_sessions?: number
      total_audio_duration?: number
      last_activity?: string
    }
  }
  preferences?: {
    settings?: {
      theme?: string
      language?: string
      notifications?: any
      privacy?: any
    }
  }
}

type UserFilters = {
  role?: 'admin' | 'user' | '' | undefined
  status?: 'active' | 'disabled' | 'unverified' | '' | undefined
  search?: string
  sortBy?: 'name' | 'email' | 'created_at' | 'last_activity'
  sortOrder?: 'asc' | 'desc'
}

type UserStats = {
  totalUsers: number
  activeUsers: number
  adminUsers: number
  disabledUsers: number
  unverifiedUsers: number
  newUsersThisMonth: number
}

export default function AdminPage() {
  const { user } = useAuth()
  const { toast } = useToast()
  const router = useRouter()
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [userStats, setUserStats] = useState<UserStats>({
    totalUsers: 0,
    activeUsers: 0,
    adminUsers: 0,
    disabledUsers: 0,
    unverifiedUsers: 0,
    newUsersThisMonth: 0
  })
  const [filters, setFilters] = useState<UserFilters>({
    role: undefined,
    status: undefined,
    search: '',
    sortBy: 'created_at',
    sortOrder: 'desc'
  })
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [showUserDetails, setShowUserDetails] = useState(false)
  const [bulkActionLoading, setBulkActionLoading] = useState(false)


  useEffect(() => {
    if (!user) {
      router.push('/auth/signin?callbackUrl=/dashboard/admin')
      return
    }

    if (user?.role !== "admin") {
      router.push('/dashboard?error=admin-required')
      return
    }

    fetchUsers()
  }, [user, filters, router])

  const fetchUsers = async () => {
    try {
      setLoading(true)

      // Use the new enhanced API
      const params = new URLSearchParams()
      if (filters.role) params.append('role', filters.role)
      if (filters.status) params.append('status', filters.status)
      if (filters.search) params.append('search', filters.search)
      if (filters.sortBy) params.append('sortBy', filters.sortBy)
      if (filters.sortOrder) params.append('sortOrder', filters.sortOrder)
      params.append('pageSize', '100') // Get more users for admin view

      // Use the main API
      const response = await fetch(`/api/users?${params.toString()}`)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch users`)
      }

      const data = await response.json()
      setUsers(data.users || [])

      // Calculate statistics
      calculateUserStats(data.users || [])
    } catch (err) {
      console.error("Error fetching users:", err)
      setError(err instanceof Error ? err.message : "Failed to fetch users")
      // Set empty users array on error
      setUsers([])
    } finally {
      setLoading(false)
    }
  }

  const calculateUserStats = (usersData: User[]) => {
    const now = new Date()
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1)

    const stats: UserStats = {
      totalUsers: usersData.length,
      activeUsers: usersData.filter(u => !u.isDisabled && u.email_verified).length,
      adminUsers: usersData.filter(u => u.role === 'admin').length,
      disabledUsers: usersData.filter(u => u.isDisabled).length,
      unverifiedUsers: usersData.filter(u => !u.email_verified && !u.isDisabled).length,
      newUsersThisMonth: usersData.filter(u => {
        const createdAt = new Date(u.created_at || 0)
        return createdAt >= thisMonth
      }).length
    }

    setUserStats(stats)
  }

  const handleRoleChange = async (userId: string, newRole: "admin" | "user") => {
    try {
      const response = await fetch(`/api/users/${userId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ role: newRole })
      })

      if (!response.ok) {
        throw new Error('Failed to update user role')
      }

      // Update local state
      setUsers(users.map(user =>
        user.id === userId ? { ...user, role: newRole } : user
      ))

      toast({
        title: "Role updated",
        description: `User role has been updated to ${newRole}`,
      })
    } catch (err) {
      console.error("Error updating role:", err)
      toast({
        title: "Error",
        description: "Failed to update user role",
        variant: "destructive",
      })
    }
  }

  const handleToggleUserStatus = async (userId: string, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/users/${userId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isDisabled: !currentStatus })
      })

      if (!response.ok) {
        throw new Error('Failed to update user status')
      }

      // Update local state
      setUsers(users.map(user =>
        user.id === userId ? { ...user, isDisabled: !currentStatus } : user
      ))

      toast({
        title: "User status updated",
        description: `User has been ${!currentStatus ? 'disabled' : 'enabled'}`,
      })
    } catch (err) {
      console.error("Error updating user status:", err)
      toast({
        title: "Error",
        description: "Failed to update user status",
        variant: "destructive",
      })
    }
  }

  const handleBulkAction = async (action: 'enable' | 'disable' | 'make_admin' | 'make_user') => {
    if (selectedUsers.length === 0) {
      toast({
        title: "No users selected",
        description: "Please select users to perform bulk action",
        variant: "destructive",
      })
      return
    }

    setBulkActionLoading(true)
    try {
      const updates: any = {}
      switch (action) {
        case 'enable':
          updates.isDisabled = false
          break
        case 'disable':
          updates.isDisabled = true
          break
        case 'make_admin':
          updates.role = 'admin'
          break
        case 'make_user':
          updates.role = 'user'
          break
      }

      const response = await fetch('/api/users', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userIds: selectedUsers, updates })
      })

      if (!response.ok) {
        throw new Error('Failed to perform bulk action')
      }

      const result = await response.json()

      // Refresh users list
      await fetchUsers()
      setSelectedUsers([])

      toast({
        title: "Bulk action completed",
        description: `${result.message}`,
      })
    } catch (err) {
      console.error("Error performing bulk action:", err)
      toast({
        title: "Error",
        description: "Failed to perform bulk action",
        variant: "destructive",
      })
    } finally {
      setBulkActionLoading(false)
    }
  }

  const handleSelectUser = (userId: string, checked: boolean) => {
    if (checked) {
      setSelectedUsers([...selectedUsers, userId])
    } else {
      setSelectedUsers(selectedUsers.filter(id => id !== userId))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedUsers(filteredUsers.map(user => user.id))
    } else {
      setSelectedUsers([])
    }
  }

  const handleViewUserDetails = async (userId: string) => {
    try {
      const response = await fetch(`/api/users/${userId}`)
      if (!response.ok) {
        throw new Error('Failed to fetch user details')
      }
      const userData = await response.json()
      setSelectedUser(userData)
      setShowUserDetails(true)
    } catch (err) {
      console.error("Error fetching user details:", err)
      toast({
        title: "Error",
        description: "Failed to fetch user details",
        variant: "destructive",
      })
    }
  }



  const filteredUsers = users // Filtering is now done server-side

  if (!user || user.role !== "admin") {
    return (
      <div className="container mx-auto p-4">
        <Alert variant="destructive">
          <AlertDescription>You do not have permission to access this page</AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <AdminGuard>
      <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Admin Management</h1>
        <p className="text-muted-foreground mt-2">Manage users, monitor activity, and view system statistics</p>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userStats.totalUsers}</div>
            <p className="text-xs text-muted-foreground">
              +{userStats.newUsersThisMonth} this month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userStats.activeUsers}</div>
            <p className="text-xs text-muted-foreground">
              {userStats.disabledUsers} disabled, {userStats.unverifiedUsers} unverified
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Administrators</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userStats.adminUsers}</div>
            <p className="text-xs text-muted-foreground">
              {((userStats.adminUsers / userStats.totalUsers) * 100).toFixed(1)}% of total
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="users" className="space-y-4">
        <TabsList>
          <TabsTrigger value="users">User Management</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>User Management</CardTitle>
                  <CardDescription>View and manage user roles and account status</CardDescription>
                </div>
                <Button onClick={fetchUsers} variant="outline" size="sm">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Filters and Search */}
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <Input
                      placeholder="Search by email, name, or username"
                      value={filters.search}
                      onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                      className="max-w-sm"
                    />
                  </div>
                  <Select value={filters.role || 'all'} onValueChange={(value) => setFilters({ ...filters, role: value === 'all' ? undefined : value as any })}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Filter by role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Roles</SelectItem>
                      <SelectItem value="admin">Admin</SelectItem>
                      <SelectItem value="user">User</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={filters.status || 'all'} onValueChange={(value) => setFilters({ ...filters, status: value === 'all' ? undefined : value as any })}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="disabled">Disabled</SelectItem>
                      <SelectItem value="unverified">Unverified</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Bulk Actions */}
                {selectedUsers.length > 0 && (
                  <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                    <span className="text-sm font-medium">
                      {selectedUsers.length} user(s) selected
                    </span>
                    <div className="flex gap-2 ml-auto">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleBulkAction('enable')}
                        disabled={bulkActionLoading}
                      >
                        Enable
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleBulkAction('disable')}
                        disabled={bulkActionLoading}
                      >
                        Disable
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleBulkAction('make_admin')}
                        disabled={bulkActionLoading}
                      >
                        Make Admin
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleBulkAction('make_user')}
                        disabled={bulkActionLoading}
                      >
                        Make User
                      </Button>
                    </div>
                  </div>
                )}

                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <div className="space-y-4">
                  {loading ? (
                    <div className="flex justify-center items-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin" />
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {/* Select All Checkbox */}
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="select-all"
                          checked={selectedUsers.length === filteredUsers.length && filteredUsers.length > 0}
                          onCheckedChange={handleSelectAll}
                        />
                        <Label htmlFor="select-all" className="text-sm font-medium">
                          Select all ({filteredUsers.length} users)
                        </Label>
                      </div>

                      {/* User List */}
                      {filteredUsers.map((user) => (
                        <div
                          key={user.id}
                          className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                        >
                          <div className="flex items-center space-x-4">
                            <Checkbox
                              checked={selectedUsers.includes(user.id)}
                              onCheckedChange={(checked) => handleSelectUser(user.id, checked as boolean)}
                            />
                            <Avatar className="h-10 w-10">
                              <AvatarImage src={user.profile?.details?.avatar_url || `https://avatar.vercel.sh/${user.username}`} />
                              <AvatarFallback>{user.username?.slice(0, 2).toUpperCase()}</AvatarFallback>
                            </Avatar>
                            <div className="space-y-1">
                              <div className="flex items-center gap-2">
                                <p className="font-medium">{user.name}</p>
                                <Badge variant={user.role === "admin" ? "default" : "secondary"}>
                                  {user.role}
                                </Badge>
                                {user.isDisabled && (
                                  <Badge variant="destructive">Disabled</Badge>
                                )}
                                {!user.email_verified && (
                                  <Badge variant="outline">Unverified</Badge>
                                )}
                              </div>
                              <p className="text-sm text-muted-foreground">{user.email}</p>
                              <p className="text-sm text-muted-foreground">@{user.username}</p>
                              {user.statistics?.summary && (
                                <p className="text-xs text-muted-foreground">
                                  {user.statistics.summary.audio_uploads || 0} uploads •
                                  {Math.round((user.statistics.summary.total_audio_duration || 0) / 60)} minutes
                                </p>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleViewUserDetails(user.id)}
                            >
                              <Eye className="h-4 w-4 mr-1" />
                              View
                            </Button>
                            <Button
                              variant={user.role === "admin" ? "secondary" : "default"}
                              size="sm"
                              onClick={() => handleRoleChange(user.id, user.role === "admin" ? "user" : "admin")}
                            >
                              Make {user.role === "admin" ? "User" : "Admin"}
                            </Button>
                            <Button
                              variant={user.isDisabled ? "default" : "destructive"}
                              size="sm"
                              onClick={() => handleToggleUserStatus(user.id, user.isDisabled || false)}
                            >
                              {user.isDisabled ? "Enable" : "Disable"}
                            </Button>
                          </div>
                        </div>
                      ))}

                      {filteredUsers.length === 0 && !loading && (
                        <div className="text-center py-8">
                          <p className="text-muted-foreground">No users found matching your criteria.</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>User Growth</CardTitle>
                <CardDescription>User registration trends</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">This Month</span>
                    <span className="font-bold">{userStats.newUsersThisMonth}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Total Users</span>
                    <span className="font-bold">{userStats.totalUsers}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Active Rate</span>
                    <span className="font-bold">
                      {userStats.totalUsers > 0 ? ((userStats.activeUsers / userStats.totalUsers) * 100).toFixed(1) : 0}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>User Status Distribution</CardTitle>
                <CardDescription>Breakdown of user account status</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-green-600">Active</span>
                    <span className="font-bold">{userStats.activeUsers}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-red-600">Disabled</span>
                    <span className="font-bold">{userStats.disabledUsers}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-yellow-600">Unverified</span>
                    <span className="font-bold">{userStats.unverifiedUsers}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-blue-600">Administrators</span>
                    <span className="font-bold">{userStats.adminUsers}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* User Details Dialog */}
      <Dialog open={showUserDetails} onOpenChange={setShowUserDetails}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>User Details</DialogTitle>
            <DialogDescription>
              Detailed information about {selectedUser?.name}
            </DialogDescription>
          </DialogHeader>

          {selectedUser && (
            <div className="space-y-6">
              {/* User Profile Section */}
              <div className="flex items-start space-x-4">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={selectedUser.profile?.details?.avatar_url || `https://avatar.vercel.sh/${selectedUser.username}`} />
                  <AvatarFallback>{selectedUser.username?.slice(0, 2).toUpperCase()}</AvatarFallback>
                </Avatar>
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold">{selectedUser.name}</h3>
                  <p className="text-muted-foreground">{selectedUser.email}</p>
                  <p className="text-muted-foreground">@{selectedUser.username}</p>
                  <div className="flex gap-2">
                    <Badge variant={selectedUser.role === "admin" ? "default" : "secondary"}>
                      {selectedUser.role}
                    </Badge>
                    {selectedUser.isDisabled && (
                      <Badge variant="destructive">Disabled</Badge>
                    )}
                    {!selectedUser.email_verified && (
                      <Badge variant="outline">Unverified Email</Badge>
                    )}
                  </div>
                </div>
              </div>

              {/* Statistics */}
              {selectedUser.statistics?.summary && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Contribution Statistics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold">{selectedUser.statistics.summary.audio_uploads || 0}</div>
                        <div className="text-sm text-muted-foreground">Audio Uploads</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold">{Math.round((selectedUser.statistics.summary.total_audio_duration || 0) / 60)}</div>
                        <div className="text-sm text-muted-foreground">Minutes</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold">{selectedUser.statistics.summary.reviews_completed || 0}</div>
                        <div className="text-sm text-muted-foreground">Reviews</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold">{selectedUser.statistics.summary.training_sessions || 0}</div>
                        <div className="text-sm text-muted-foreground">Training Sessions</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Recent Activity */}
              {selectedUser.recentActivity && selectedUser.recentActivity.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Recent Activity</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {selectedUser.recentActivity.slice(0, 5).map((activity: any, index: number) => (
                        <div key={index} className="flex items-center justify-between py-2 border-b last:border-b-0">
                          <div>
                            <p className="text-sm font-medium">{activity.title}</p>
                            <p className="text-xs text-muted-foreground">
                              {new Date(activity.created_at).toLocaleDateString()}
                            </p>
                          </div>
                          <Badge variant={
                            activity.status === 'approved' ? 'default' :
                            activity.status === 'rejected' ? 'destructive' : 'secondary'
                          }>
                            {activity.status}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Account Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Account Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium">Created</Label>
                      <p className="text-sm text-muted-foreground">
                        {selectedUser.created_at ? new Date(selectedUser.created_at).toLocaleDateString() : 'Unknown'}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Last Updated</Label>
                      <p className="text-sm text-muted-foreground">
                        {selectedUser.updated_at ? new Date(selectedUser.updated_at).toLocaleDateString() : 'Unknown'}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Email Verified</Label>
                      <p className="text-sm text-muted-foreground">
                        {selectedUser.email_verified ? 'Yes' : 'No'}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Language Preferences</Label>
                      <p className="text-sm text-muted-foreground">
                        {selectedUser.profile?.details?.language_preferences?.join(', ') || 'Not set'}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </DialogContent>
      </Dialog>
      </div>
    </AdminGuard>
  )
}