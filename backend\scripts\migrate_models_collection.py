#!/usr/bin/env python3
"""
Migration script to populate models collection from existing training_status and training_history data
"""

import sys
import os

# Add the backend directory to Python path
backend_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(backend_dir)
sys.path.insert(0, parent_dir)
sys.path.insert(0, backend_dir)

from services.firebase import firebase_service
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_models_collection():
    """Migrate existing training data to models collection"""
    try:
        firebase_service.initialize()
        
        migrated_count = 0
        
        # Get all training status documents
        status_docs = firebase_service.db.collection('training_status').get()
        
        for doc in status_docs:
            try:
                model_type = doc.id  # asr, tts, etc.
                status_data = doc.to_dict()
                
                # Only migrate completed training sessions that have a model version
                if (status_data.get('status') == 'completed' and 
                    status_data.get('model_version') and 
                    status_data.get('model_version') != ''):
                    
                    model_version = status_data.get('model_version')
                    model_id = f"{model_type}_{model_version}"
                    
                    # Check if model record already exists
                    existing_model = firebase_service.db.collection('models').document(model_id).get()
                    
                    if existing_model.exists:
                        logger.info(f"Model record already exists for {model_id}, skipping")
                        continue
                    
                    # Create model record from status data
                    model_data = {
                        "model_id": model_id,
                        "model_type": model_type,
                        "model_version": model_version,
                        "base_model": f"openai/whisper-small",  # Default assumption for ASR
                        "status": "active",  # Assume the latest completed model is active
                        "confidence": status_data.get('current_confidence', 0.0),
                        "accuracy": status_data.get('current_accuracy', 0.0),
                        "wer": status_data.get('current_wer', 0.0),
                        "cer": status_data.get('current_cer', 0.0),
                        "ser": status_data.get('current_ser', 0.0),
                        "samples_trained": status_data.get('samples_trained', 0),
                        "total_training_sessions": 1,
                        "first_trained": status_data.get('end_time', datetime.now().isoformat()),
                        "last_trained": status_data.get('end_time', datetime.now().isoformat()),
                        "model_path": status_data.get('model_path', ''),
                        "gcs_url": status_data.get('gcs_url', ''),
                        "upload_success": status_data.get('upload_success', False),
                        "training_epochs": status_data.get('total_epochs', 0),
                        "created_at": status_data.get('end_time', datetime.now().isoformat()),
                        "last_validated": None,
                        "validation_samples": 0
                    }
                    
                    # Save model record
                    firebase_service.save_model_record(model_data)
                    migrated_count += 1
                    logger.info(f"Migrated {model_type} model: {model_id}")
                    
            except Exception as e:
                logger.error(f"Error migrating {doc.id}: {e}")
                continue
        
        # Also check training_history for additional models
        try:
            history_docs = firebase_service.db.collection('training_history').get()
            
            for doc in history_docs:
                try:
                    history_data = doc.to_dict()
                    model_type = history_data.get('model_type')
                    model_version = history_data.get('model_version')
                    
                    if not model_type or not model_version:
                        continue
                    
                    model_id = f"{model_type}_{model_version}"
                    
                    # Check if model record already exists
                    existing_model = firebase_service.db.collection('models').document(model_id).get()
                    
                    if existing_model.exists:
                        continue
                    
                    # Create model record from history data
                    model_data = {
                        "model_id": model_id,
                        "model_type": model_type,
                        "model_version": model_version,
                        "base_model": history_data.get('base_model', f"openai/whisper-small"),
                        "status": "active" if history_data.get('status') == 'completed' else "inactive",
                        "confidence": history_data.get('confidence', 0.0),
                        "accuracy": history_data.get('accuracy', 0.0),
                        "wer": history_data.get('wer', 0.0),
                        "cer": history_data.get('cer', 0.0),
                        "ser": history_data.get('ser', 0.0),
                        "samples_trained": history_data.get('samples_trained', 0),
                        "total_training_sessions": 1,
                        "first_trained": history_data.get('end_time', datetime.now().isoformat()),
                        "last_trained": history_data.get('end_time', datetime.now().isoformat()),
                        "model_path": history_data.get('model_path', ''),
                        "gcs_url": history_data.get('gcs_url', ''),
                        "upload_success": history_data.get('upload_success', False),
                        "training_epochs": history_data.get('epochs', 0),
                        "created_at": history_data.get('end_time', datetime.now().isoformat()),
                        "last_validated": None,
                        "validation_samples": 0
                    }
                    
                    # Save model record
                    firebase_service.save_model_record(model_data)
                    migrated_count += 1
                    logger.info(f"Migrated {model_type} model from history: {model_id}")
                    
                except Exception as e:
                    logger.error(f"Error migrating history {doc.id}: {e}")
                    continue
                    
        except Exception as e:
            logger.warning(f"Error processing training_history: {e}")
        
        logger.info(f"Migration completed. Migrated {migrated_count} model records.")
        return migrated_count
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        raise

if __name__ == "__main__":
    migrate_models_collection()
