{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/prisma-adapter": "^2.9.0", "@google-cloud/storage": "^7.16.0", "@hookform/resolvers": "^3.9.1", "@prisma/client": "^6.6.0", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@types/react-google-recaptcha": "^2.1.9", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.20", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "firebase": "^10.8.0", "get-audio-duration": "^4.0.1", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "music-metadata": "^11.2.1", "next": "15.2.4", "next-auth": "^4.24.11", "next-themes": "^0.4.4", "react": "^19", "react-chartjs-2": "^5.3.0", "react-day-picker": "8.10.1", "react-dom": "^19", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.54.1", "react-hot-toast": "^2.5.2", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "tesseract.js": "^5.0.5", "trace": "^3.2.0", "uuid": "^11.1.0", "vaul": "^0.9.6", "xlsx": "^0.18.5", "ytdl-core": "^4.11.5", "zod": "^3.24.1", "zustand": "^5.0.5"}, "devDependencies": {"@types/node": "^22", "@types/node-fetch": "^2.6.12", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^16.5.0", "firebase-admin": "^13.3.0", "node-fetch": "^3.3.2", "postcss": "^8", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^5", "vercel": "^41.7.0"}}