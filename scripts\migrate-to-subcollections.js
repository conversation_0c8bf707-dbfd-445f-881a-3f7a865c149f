#!/usr/bin/env node

/**
 * Migration Script: Move Collections to User Subcollections
 * 
 * This script migrates:
 * - activity_logs → users/{userId}/activity_logs
 * - security_events → users/{userId}/security_events  
 * - user_sessions → users/{userId}/sessions
 */

const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');

// Initialize Firebase Admin
if (!admin.apps.length) {
  // You'll need to set GOOGLE_APPLICATION_CREDENTIALS or provide service account key
  admin.initializeApp({
    credential: admin.credential.applicationDefault(),
    projectId: 'kana-masark-255aa'
  });
}

const db = admin.firestore();

class CollectionMigrator {
  constructor() {
    this.stats = {
      activity_logs: { processed: 0, migrated: 0, errors: 0 },
      security_events: { processed: 0, migrated: 0, errors: 0 },
      user_sessions: { processed: 0, migrated: 0, errors: 0 }
    };
  }

  async migrateActivityLogs() {
    console.log('\n📋 Migrating activity_logs...');
    
    try {
      const snapshot = await db.collection('activity_logs').get();
      console.log(`Found ${snapshot.size} activity log documents`);

      const batch = db.batch();
      let batchCount = 0;
      const BATCH_SIZE = 500;

      for (const doc of snapshot.docs) {
        this.stats.activity_logs.processed++;
        
        try {
          const data = doc.data();
          const userId = data.userId;
          
          if (!userId) {
            console.warn(`⚠️  Activity log ${doc.id} missing userId, skipping`);
            this.stats.activity_logs.errors++;
            continue;
          }

          // Create new document in user subcollection
          const newDocRef = db.collection('users').doc(userId)
                             .collection('activity_logs').doc(doc.id);
          
          batch.set(newDocRef, data);
          batchCount++;
          
          // Execute batch when it reaches limit
          if (batchCount >= BATCH_SIZE) {
            await batch.commit();
            console.log(`✅ Migrated ${batchCount} activity logs`);
            batchCount = 0;
          }
          
          this.stats.activity_logs.migrated++;
        } catch (error) {
          console.error(`❌ Error migrating activity log ${doc.id}:`, error);
          this.stats.activity_logs.errors++;
        }
      }

      // Execute remaining batch
      if (batchCount > 0) {
        await batch.commit();
        console.log(`✅ Migrated final ${batchCount} activity logs`);
      }

    } catch (error) {
      console.error('❌ Error migrating activity_logs:', error);
    }
  }

  async migrateSecurityEvents() {
    console.log('\n🔒 Migrating security_events...');
    
    try {
      const snapshot = await db.collection('security_events').get();
      console.log(`Found ${snapshot.size} security event documents`);

      const batch = db.batch();
      let batchCount = 0;
      const BATCH_SIZE = 500;

      for (const doc of snapshot.docs) {
        this.stats.security_events.processed++;
        
        try {
          const data = doc.data();
          const userId = data.userId;
          
          if (!userId) {
            console.warn(`⚠️  Security event ${doc.id} missing userId, skipping`);
            this.stats.security_events.errors++;
            continue;
          }

          // Create new document in user subcollection
          const newDocRef = db.collection('users').doc(userId)
                             .collection('security_events').doc(doc.id);
          
          batch.set(newDocRef, data);
          batchCount++;
          
          // Execute batch when it reaches limit
          if (batchCount >= BATCH_SIZE) {
            await batch.commit();
            console.log(`✅ Migrated ${batchCount} security events`);
            batchCount = 0;
          }
          
          this.stats.security_events.migrated++;
        } catch (error) {
          console.error(`❌ Error migrating security event ${doc.id}:`, error);
          this.stats.security_events.errors++;
        }
      }

      // Execute remaining batch
      if (batchCount > 0) {
        await batch.commit();
        console.log(`✅ Migrated final ${batchCount} security events`);
      }

    } catch (error) {
      console.error('❌ Error migrating security_events:', error);
    }
  }

  async migrateUserSessions() {
    console.log('\n👤 Migrating user_sessions...');
    
    try {
      const snapshot = await db.collection('user_sessions').get();
      console.log(`Found ${snapshot.size} user session documents`);

      const batch = db.batch();
      let batchCount = 0;
      const BATCH_SIZE = 500;

      for (const doc of snapshot.docs) {
        this.stats.user_sessions.processed++;
        
        try {
          const data = doc.data();
          const userId = data.userId;
          
          if (!userId) {
            console.warn(`⚠️  User session ${doc.id} missing userId, skipping`);
            this.stats.user_sessions.errors++;
            continue;
          }

          // Create new document in user subcollection (renamed to 'sessions')
          const newDocRef = db.collection('users').doc(userId)
                             .collection('sessions').doc(doc.id);
          
          batch.set(newDocRef, data);
          batchCount++;
          
          // Execute batch when it reaches limit
          if (batchCount >= BATCH_SIZE) {
            await batch.commit();
            console.log(`✅ Migrated ${batchCount} user sessions`);
            batchCount = 0;
          }
          
          this.stats.user_sessions.migrated++;
        } catch (error) {
          console.error(`❌ Error migrating user session ${doc.id}:`, error);
          this.stats.user_sessions.errors++;
        }
      }

      // Execute remaining batch
      if (batchCount > 0) {
        await batch.commit();
        console.log(`✅ Migrated final ${batchCount} user sessions`);
      }

    } catch (error) {
      console.error('❌ Error migrating user_sessions:', error);
    }
  }

  async deleteOldCollections() {
    console.log('\n🗑️  Deleting old collections...');
    
    const collections = ['activity_logs', 'security_events', 'user_sessions'];
    
    for (const collectionName of collections) {
      try {
        console.log(`Deleting ${collectionName}...`);
        await this.deleteCollection(collectionName);
        console.log(`✅ Deleted ${collectionName}`);
      } catch (error) {
        console.error(`❌ Error deleting ${collectionName}:`, error);
      }
    }
  }

  async deleteCollection(collectionName) {
    const collectionRef = db.collection(collectionName);
    const batchSize = 500;
    
    let query = collectionRef.limit(batchSize);
    
    return new Promise((resolve, reject) => {
      this.deleteQueryBatch(query, resolve, reject);
    });
  }

  deleteQueryBatch(query, resolve, reject) {
    query.get()
      .then((snapshot) => {
        if (snapshot.size === 0) {
          return 0;
        }

        const batch = db.batch();
        snapshot.docs.forEach((doc) => {
          batch.delete(doc.ref);
        });

        return batch.commit().then(() => {
          return snapshot.size;
        });
      })
      .then((numDeleted) => {
        if (numDeleted === 0) {
          resolve();
          return;
        }

        process.nextTick(() => {
          this.deleteQueryBatch(query, resolve, reject);
        });
      })
      .catch(reject);
  }

  printStats() {
    console.log('\n📊 Migration Statistics:');
    console.log('========================');
    
    Object.entries(this.stats).forEach(([collection, stats]) => {
      console.log(`\n${collection}:`);
      console.log(`  Processed: ${stats.processed}`);
      console.log(`  Migrated:  ${stats.migrated}`);
      console.log(`  Errors:    ${stats.errors}`);
      console.log(`  Success:   ${((stats.migrated / stats.processed) * 100).toFixed(1)}%`);
    });
  }

  async run(options = {}) {
    console.log('🚀 Starting Collection Migration to User Subcollections');
    console.log('======================================================');
    
    const startTime = Date.now();
    
    try {
      // Run migrations
      await this.migrateActivityLogs();
      await this.migrateSecurityEvents();
      await this.migrateUserSessions();
      
      // Delete old collections if requested
      if (options.deleteOld) {
        await this.deleteOldCollections();
      } else {
        console.log('\n⚠️  Old collections preserved. Use --delete-old to remove them.');
      }
      
      const duration = ((Date.now() - startTime) / 1000).toFixed(2);
      
      this.printStats();
      console.log(`\n🎉 Migration completed in ${duration} seconds!`);
      
    } catch (error) {
      console.error('\n❌ Migration failed:', error);
      process.exit(1);
    }
  }
}

// CLI execution
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {
    deleteOld: args.includes('--delete-old')
  };
  
  const migrator = new CollectionMigrator();
  migrator.run(options)
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

module.exports = CollectionMigrator;
