#!/usr/bin/env node

/**
 * Test Script: Verify Subcollection Migration
 * 
 * This script tests the new subcollection structure to ensure
 * everything works correctly after migration.
 */

const admin = require('firebase-admin');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.applicationDefault(),
    projectId: 'kana-masark-255aa'
  });
}

const db = admin.firestore();

class SubcollectionTester {
  constructor() {
    this.testUserId = 'test-user-' + Date.now();
    this.results = {
      activity_logs: { passed: 0, failed: 0 },
      security_events: { passed: 0, failed: 0 },
      sessions: { passed: 0, failed: 0 }
    };
  }

  async testActivityLogs() {
    console.log('\n📋 Testing Activity Logs Subcollection...');
    
    try {
      // Test: Create activity log
      const activityData = {
        type: 'test',
        description: 'Test activity log',
        timestamp: new Date().toISOString(),
        metadata: { test: true }
      };

      const activityRef = await db.collection('users')
        .doc(this.testUserId)
        .collection('activity_logs')
        .add(activityData);

      console.log('✅ Activity log created:', activityRef.id);
      this.results.activity_logs.passed++;

      // Test: Read activity log
      const activityDoc = await activityRef.get();
      if (activityDoc.exists && activityDoc.data().type === 'test') {
        console.log('✅ Activity log read successfully');
        this.results.activity_logs.passed++;
      } else {
        console.log('❌ Activity log read failed');
        this.results.activity_logs.failed++;
      }

      // Test: Query activity logs
      const querySnapshot = await db.collection('users')
        .doc(this.testUserId)
        .collection('activity_logs')
        .where('type', '==', 'test')
        .get();

      if (querySnapshot.size > 0) {
        console.log('✅ Activity logs query successful');
        this.results.activity_logs.passed++;
      } else {
        console.log('❌ Activity logs query failed');
        this.results.activity_logs.failed++;
      }

    } catch (error) {
      console.error('❌ Activity logs test failed:', error);
      this.results.activity_logs.failed++;
    }
  }

  async testSecurityEvents() {
    console.log('\n🔒 Testing Security Events Subcollection...');
    
    try {
      // Test: Create security event
      const eventData = {
        event_type: 'test_event',
        description: 'Test security event',
        severity: 'low',
        timestamp: new Date().toISOString(),
        resolved: false,
        metadata: { test: true }
      };

      const eventRef = await db.collection('users')
        .doc(this.testUserId)
        .collection('security_events')
        .add(eventData);

      console.log('✅ Security event created:', eventRef.id);
      this.results.security_events.passed++;

      // Test: Read security event
      const eventDoc = await eventRef.get();
      if (eventDoc.exists && eventDoc.data().event_type === 'test_event') {
        console.log('✅ Security event read successfully');
        this.results.security_events.passed++;
      } else {
        console.log('❌ Security event read failed');
        this.results.security_events.failed++;
      }

      // Test: Update security event (resolve)
      await eventRef.update({
        resolved: true,
        resolved_by: 'test-system',
        resolved_at: new Date().toISOString()
      });

      const updatedDoc = await eventRef.get();
      if (updatedDoc.data().resolved === true) {
        console.log('✅ Security event update successful');
        this.results.security_events.passed++;
      } else {
        console.log('❌ Security event update failed');
        this.results.security_events.failed++;
      }

    } catch (error) {
      console.error('❌ Security events test failed:', error);
      this.results.security_events.failed++;
    }
  }

  async testSessions() {
    console.log('\n👤 Testing Sessions Subcollection...');
    
    try {
      // Test: Create session
      const sessionData = {
        session_id: 'test-session-' + Date.now(),
        login_time: new Date().toISOString(),
        last_activity: new Date().toISOString(),
        is_active: true,
        ip_address: '127.0.0.1',
        user_agent: 'Test Agent'
      };

      const sessionRef = await db.collection('users')
        .doc(this.testUserId)
        .collection('sessions')
        .add(sessionData);

      console.log('✅ Session created:', sessionRef.id);
      this.results.sessions.passed++;

      // Test: Read session
      const sessionDoc = await sessionRef.get();
      if (sessionDoc.exists && sessionDoc.data().is_active === true) {
        console.log('✅ Session read successfully');
        this.results.sessions.passed++;
      } else {
        console.log('❌ Session read failed');
        this.results.sessions.failed++;
      }

      // Test: Update session (end session)
      await sessionRef.update({
        is_active: false,
        logout_time: new Date().toISOString(),
        logout_reason: 'test'
      });

      const updatedSession = await sessionRef.get();
      if (updatedSession.data().is_active === false) {
        console.log('✅ Session update successful');
        this.results.sessions.passed++;
      } else {
        console.log('❌ Session update failed');
        this.results.sessions.failed++;
      }

    } catch (error) {
      console.error('❌ Sessions test failed:', error);
      this.results.sessions.failed++;
    }
  }

  async testCollectionGroupQueries() {
    console.log('\n🔍 Testing Collection Group Queries...');
    
    try {
      // Test: Query all activity logs across users (admin feature)
      const allActivities = await db.collectionGroup('activity_logs')
        .where('metadata.test', '==', true)
        .limit(10)
        .get();

      console.log(`✅ Collection group query found ${allActivities.size} activity logs`);

      // Test: Query all security events across users
      const allEvents = await db.collectionGroup('security_events')
        .where('metadata.test', '==', true)
        .limit(10)
        .get();

      console.log(`✅ Collection group query found ${allEvents.size} security events`);

    } catch (error) {
      console.error('❌ Collection group queries failed:', error);
    }
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up test data...');
    
    try {
      // Delete test user and all subcollections
      await this.deleteCollection(`users/${this.testUserId}/activity_logs`);
      await this.deleteCollection(`users/${this.testUserId}/security_events`);
      await this.deleteCollection(`users/${this.testUserId}/sessions`);
      
      // Delete test user document
      await db.doc(`users/${this.testUserId}`).delete();
      
      console.log('✅ Test data cleaned up');
    } catch (error) {
      console.error('❌ Cleanup failed:', error);
    }
  }

  async deleteCollection(collectionPath) {
    const collectionRef = db.collection(collectionPath);
    const snapshot = await collectionRef.get();
    
    const batch = db.batch();
    snapshot.docs.forEach(doc => {
      batch.delete(doc.ref);
    });
    
    if (snapshot.size > 0) {
      await batch.commit();
    }
  }

  printResults() {
    console.log('\n📊 Test Results:');
    console.log('================');
    
    let totalPassed = 0;
    let totalFailed = 0;
    
    Object.entries(this.results).forEach(([collection, results]) => {
      const total = results.passed + results.failed;
      const percentage = total > 0 ? ((results.passed / total) * 100).toFixed(1) : '0.0';
      
      console.log(`\n${collection}:`);
      console.log(`  Passed: ${results.passed}`);
      console.log(`  Failed: ${results.failed}`);
      console.log(`  Success Rate: ${percentage}%`);
      
      totalPassed += results.passed;
      totalFailed += results.failed;
    });
    
    const overallTotal = totalPassed + totalFailed;
    const overallPercentage = overallTotal > 0 ? ((totalPassed / overallTotal) * 100).toFixed(1) : '0.0';
    
    console.log(`\nOverall:`);
    console.log(`  Total Passed: ${totalPassed}`);
    console.log(`  Total Failed: ${totalFailed}`);
    console.log(`  Overall Success Rate: ${overallPercentage}%`);
    
    if (totalFailed === 0) {
      console.log('\n🎉 All tests passed! Subcollection migration is working correctly.');
    } else {
      console.log('\n⚠️  Some tests failed. Please check the migration and code updates.');
    }
  }

  async run() {
    console.log('🧪 Starting Subcollection Migration Tests');
    console.log('=========================================');
    console.log(`Test User ID: ${this.testUserId}`);
    
    const startTime = Date.now();
    
    try {
      await this.testActivityLogs();
      await this.testSecurityEvents();
      await this.testSessions();
      await this.testCollectionGroupQueries();
      
      const duration = ((Date.now() - startTime) / 1000).toFixed(2);
      
      this.printResults();
      console.log(`\n⏱️  Tests completed in ${duration} seconds`);
      
    } catch (error) {
      console.error('\n❌ Test suite failed:', error);
    } finally {
      await this.cleanup();
    }
  }
}

// CLI execution
if (require.main === module) {
  const tester = new SubcollectionTester();
  tester.run()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Test suite failed:', error);
      process.exit(1);
    });
}

module.exports = SubcollectionTester;
