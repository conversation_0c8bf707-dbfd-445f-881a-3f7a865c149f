"""
Model Management Types and Enums
"""

from enum import Enum
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pydantic import BaseModel


class ModelType(str, Enum):
    """Supported model types"""
    ASR = "asr"
    TTS = "tts"
    NLP = "nlp"
    CUSTOM = "custom"


class ModelStatus(str, Enum):
    """Model status states"""
    TRAINING = "training"
    TRAINED = "trained"
    VALIDATED = "validated"
    DEPLOYED = "deployed"
    ARCHIVED = "archived"
    FAILED = "failed"


class ValidationStatus(str, Enum):
    """Validation status states"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"


class ModelMetadata(BaseModel):
    """Model metadata structure"""
    model_config = {"protected_namespaces": ()}

    id: str
    name: str
    type: ModelType
    version: str
    status: ModelStatus
    created_at: datetime
    updated_at: datetime
    size_bytes: Optional[int] = None
    tags: List[str] = []
    description: Optional[str] = None
    
    # Training information
    training_config: Optional[Dict[str, Any]] = None
    training_duration: Optional[float] = None
    training_samples: Optional[int] = None
    
    # Performance metrics
    metrics: Optional[Dict[str, float]] = None
    
    # Storage information
    local_path: Optional[str] = None
    gcs_path: Optional[str] = None
    
    # Validation information
    validation_status: ValidationStatus = ValidationStatus.PENDING
    validation_results: Optional[Dict[str, Any]] = None
    last_validated: Optional[datetime] = None


class ModelInfo(BaseModel):
    """Simplified model information for listings"""
    model_config = {"protected_namespaces": ()}

    id: str
    name: str
    type: ModelType
    version: str
    status: ModelStatus
    created_at: datetime
    size_mb: Optional[float] = None
    tags: List[str] = []
    metrics: Optional[Dict[str, float]] = None


class ModelComparison(BaseModel):
    """Model comparison results"""
    model_config = {"protected_namespaces": ()}

    model1: ModelInfo
    model2: ModelInfo
    comparison: Dict[str, Union[float, str]]
    summary: str
    recommendation: Optional[str] = None


class ValidationConfig(BaseModel):
    """Validation configuration"""
    model_config = {"protected_namespaces": ()}

    validation_type: str = "quick"  # quick, full, custom
    max_samples: Optional[int] = None
    min_confidence: Optional[float] = None
    test_dataset: Optional[str] = None
    metrics_to_compute: List[str] = ["accuracy", "confidence"]

    # Model-specific configs
    model_specific_config: Optional[Dict[str, Any]] = None


class ValidationResult(BaseModel):
    """Validation result structure"""
    model_config = {"protected_namespaces": ()}

    model_id: str
    validation_id: str
    status: ValidationStatus
    config: ValidationConfig
    
    # Timing information
    started_at: datetime
    completed_at: Optional[datetime] = None
    duration_seconds: Optional[float] = None
    
    # Results
    metrics: Dict[str, float] = {}
    error_analysis: Optional[List[Dict[str, Any]]] = None
    summary: Optional[Dict[str, Any]] = None
    
    # Error information
    error_message: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None


class CleanupConfig(BaseModel):
    """Model cleanup configuration"""
    model_config = {"protected_namespaces": ()}

    days_to_keep: int = 30
    keep_tagged: bool = True
    keep_deployed: bool = True
    keep_latest_n: int = 5
    model_types: Optional[List[ModelType]] = None
    dry_run: bool = False


class CleanupResult(BaseModel):
    """Model cleanup results"""
    model_config = {"protected_namespaces": ()}

    total_models: int
    deleted_models: int
    skipped_models: int
    freed_space_mb: float
    deleted_model_ids: List[str]
    skipped_reasons: Dict[str, List[str]]
