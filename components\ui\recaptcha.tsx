"use client"

import React, { forwardRef, useImperativeHandle, useRef } from 'react'
import <PERSON>CAP<PERSON><PERSON> from 'react-google-recaptcha'

interface RecaptchaProps {
  onVerify: (token: string | null) => void
  onExpired?: () => void
  onError?: () => void
  size?: 'compact' | 'normal' | 'invisible'
  theme?: 'light' | 'dark'
  className?: string
}

export interface RecaptchaRef {
  reset: () => void
  execute: () => void
}

export const Recaptcha = forwardRef<RecaptchaRef, RecaptchaProps>(
  ({ onVerify, onExpired, onError, size = 'normal', theme = 'light', className }, ref) => {
    const recaptchaRef = useRef<ReCAPTCHA>(null)

    useImperativeHandle(ref, () => ({
      reset: () => {
        recaptchaRef.current?.reset()
      },
      execute: () => {
        recaptchaRef.current?.execute()
      }
    }))

    const siteKey = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY
    const isDevelopment = process.env.NODE_ENV === 'development'
    const isLocalhost = typeof window !== 'undefined' &&
      (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1')

    // Skip reCAPTCHA in development on localhost
    if (isDevelopment && isLocalhost) {
      // Auto-verify in development
      React.useEffect(() => {
        onVerify('dev-bypass-token')
      }, [onVerify])

      return (
        <div className={className}>
          <div className="p-4 border border-dashed border-gray-300 rounded text-center text-sm text-gray-500">
            reCAPTCHA bypassed in development
          </div>
        </div>
      )
    }

    if (!siteKey) {
      console.error('reCAPTCHA site key not found in environment variables')
      return null
    }

    return (
      <div className={className}>
        <ReCAPTCHA
          ref={recaptchaRef}
          sitekey={siteKey}
          onChange={onVerify}
          onExpired={onExpired}
          onError={onError}
          size={size}
          theme={theme}
        />
      </div>
    )
  }
)

Recaptcha.displayName = 'Recaptcha'
