import { ref, uploadBytes, getDownloadURL, deleteObject } from "firebase/storage"
import { doc, setDoc, updateDoc, increment, collection, query, where, getDocs, getDoc, deleteDoc } from "firebase/firestore"
import { storage, db } from "@/lib/firebase"
import { createUserIfNotExists } from "./init-db"

export type AudioMetadata = {
  action: string
  audio_url: string
  created_at: string
  duration: number
  feedback: null
  format: string
  gender: string
  id: string
  is_flagged: boolean
  last_trained: null
  last_trained_at: null
  reviewed_at: null
  reviewed_by: null
  source: string
  title: string
  trained_asr: boolean
  tts_trained: boolean
  updated_at: string
  user_id: string
  transcription: {
    audio_id: string
    content: string
    created_at: string
    dialect: null
    is_flagged: boolean
    language: string
    trained_asr: boolean
    transcription_source: string
    tts_trained: boolean
    type: string
    updated_at: string
    user_id: string
  }
}

export type AudioUpdateData = {
  title?: string
  content?: string
  recording_context?: string
  updated_at: Date
  action?: 'approved' | 'rejected'
  reviewed_by?: string
  reviewed_at?: Date
  feedback?: string
}

interface Filters {
  status?: 'pending' | 'approved' | 'rejected' | 'all';
  source?: 'direct_upload' | 'all';
  userSearch?: string;
}

interface UserData {
  name?: string;
  username?: string;
  email?: string;
  role?: string;
  contribution_count?: number;
  updated_at?: Date;
}

export type AudioRecording = {
  id: string;
  title: string;
  audio_url: string;
  duration: number;
  format: string;
  gender: string;
  created_at: Date;
  updated_at: Date;
  approved: number;
  source: 'direct_upload' | 'ai';
  recording_context?: string;
  user_id: string;
  action: 'pending' | 'approved' | 'rejected';
  reviewed_by?: string;
  reviewed_at?: Date | null;
  feedback?: string;
  name: string;
  username: string;
  email: string;
  trained_asr: boolean;
  training_epoch: number | null;
  is_flagged: boolean;
  transcript?: {
    id: string;
    content: string;
    transcription_source: 'human/manual' | 'ai';
    type: 'txt' | 'pdf' | 'docx' | 'json';
    language: string;
    dialect?: string | null;
    speaker_count: number;
    status: 'pending' | 'approved' | 'rejected';
    trained_asr: boolean;
    training_epoch: number | null;
    is_flagged: boolean;
  };
}

export async function uploadAudio(
  file: File,
  metadata: AudioMetadata,
  userId: string
): Promise<string> {
  try {
    // Ensure user exists in the database
    await createUserIfNotExists(userId);

    // Create a unique file path
    const timestamp = Date.now()
    const fileExtension = file.name.split('.').pop()
    const fileName = `${userId}/${timestamp}_${metadata.title}.${fileExtension}`
    const storageRef = ref(storage, `audio/${fileName}`)

    // Upload the file
    const snapshot = await uploadBytes(storageRef, file)
    const downloadURL = await getDownloadURL(snapshot.ref)

    // Create audio recording document
    const audioId = metadata.id
    const audioData = {
      id: audioId,
      audio_url: downloadURL,
      created_at: new Date(metadata.created_at),
      duration: metadata.duration,
      format: metadata.format,
      source: metadata.source,
      updated_at: new Date(metadata.updated_at),
      user_id: metadata.user_id,
      gender: metadata.gender,
      action: metadata.action,
      reviewed_by: metadata.reviewed_by,
      reviewed_at: metadata.reviewed_at ? new Date(metadata.reviewed_at) : null,
      title: metadata.title,
      feedback: metadata.feedback,
      trained_asr: metadata.trained_asr,
      last_trained: metadata.last_trained,
      last_trained_at: metadata.last_trained_at ? new Date(metadata.last_trained_at) : null,
      tts_trained: metadata.tts_trained,
      is_flagged: metadata.is_flagged
    }

    // Create the audio recording document directly in Firestore
    const audioRef = doc(db, "audio", audioId);
    await setDoc(audioRef, audioData);

    // Create associated transcription document
    const transcriptionData = {
      audio_id: metadata.transcription.audio_id,
      content: metadata.transcription.content,
      created_at: new Date(metadata.transcription.created_at),
      dialect: metadata.transcription.dialect,
      is_flagged: metadata.transcription.is_flagged,
      language: metadata.transcription.language,
      trained_asr: metadata.transcription.trained_asr,
      transcription_source: metadata.transcription.transcription_source,
      tts_trained: metadata.transcription.tts_trained,
      type: metadata.transcription.type,
      updated_at: new Date(metadata.transcription.updated_at),
      user_id: metadata.transcription.user_id
    }

    // Create the transcription document
    const transcriptionRef = doc(db, "transcription", `transcription_${audioId.replace('audio_', '')}`);
    await setDoc(transcriptionRef, transcriptionData);

    // Update user's contribution count
    const userRef = doc(db, "users", userId);
    await updateDoc(userRef, {
      contribution_count: increment(1),
      updated_at: new Date()
    });

    return audioId
  } catch (error) {
    console.error("Error uploading audio:", error)
    throw error
  }
}

export async function getAudioRecordings(userId: string): Promise<AudioRecording[]> {
  try {
    const audioRef = collection(db, "audio");
    const q = query(audioRef, where("user_id", "==", userId));
    const querySnapshot = await getDocs(q);
    
    const recordings: AudioRecording[] = [];
    for (const docSnapshot of querySnapshot.docs) {
      const data = docSnapshot.data();
      
      // Get associated transcription
      const transcriptionRef = collection(db, "transcription");
      const transcriptionQuery = query(transcriptionRef, where("audio_id", "==", docSnapshot.id));
      const transcriptionSnapshot = await getDocs(transcriptionQuery);
      const transcriptionData = transcriptionSnapshot.docs[0]?.data();

      // Get user details
      const userRef = doc(db, "users", userId);
      const userDoc = await getDoc(userRef);
      const userData = userDoc.data() as UserData;
      
      recordings.push({
        id: docSnapshot.id,
        title: data.title || '',
        audio_url: data.audio_url || '',
        duration: data.duration || 0,
        format: data.format || 'audio/wav',
        gender: data.gender || '',
        created_at: data.created_at.toDate(),
        updated_at: data.updated_at.toDate(),
        approved: data.approved || 0,
        source: data.source || 'direct_upload',
        recording_context: transcriptionData?.content || data.recording_context || '',
        user_id: data.user_id || '',
        action: data.action || 'pending',
        reviewed_by: data.reviewed_by,
        reviewed_at: data.reviewed_at?.toDate() || null,
        feedback: data.feedback,
        name: userData?.name || '',
        username: userData?.username || '',
        email: userData?.email || '',
        trained_asr: false,
        training_epoch: null,
        is_flagged: false,
        transcript: transcriptionData ? {
          id: transcriptionData.id,
          content: transcriptionData.content,
          transcription_source: transcriptionData.transcription_source || 'human/manual',
          type: transcriptionData.type || 'txt',
          language: transcriptionData.language || 'masalit',
          dialect: transcriptionData.dialect || null,
          speaker_count: transcriptionData.speaker_count || 1,
          status: transcriptionData.status || 'pending',
          trained_asr: false,
          training_epoch: null,
          is_flagged: false
        } : undefined
      });
    }
    
    return recordings;
  } catch (error) {
    console.error("Error fetching audio recordings:", error);
    throw error;
  }
}

export async function deleteAudioRecording(audioId: string, userId: string) {
  try {
    // Validate inputs
    if (!audioId || !userId) {
      throw new Error("Missing required parameters: audioId and userId are required")
    }

    const audioRef = doc(db, "audio", audioId)
    const audioDoc = await getDoc(audioRef)
    
    if (!audioDoc.exists()) {
      throw new Error(`Audio recording with ID ${audioId} not found`)
    }
    
    const audioData = audioDoc.data()
    if (!audioData) {
      throw new Error(`Audio recording data is missing for ID ${audioId}`)
    }

    // Get user data to check role
    const userRef = doc(db, "users", userId)
    const userDoc = await getDoc(userRef)
    const userData = userDoc.data()

    // Remove permission check - authenticated users can delete any recording
    
    // Delete the file from Storage using the audio_url
    if (audioData.audio_url) {
      try {
        // Extract the path from the audio_url
        const url = new URL(audioData.audio_url)
        const path = decodeURIComponent(url.pathname.split('/o/')[1].split('?')[0])
        const fileRef = ref(storage, path)
        await deleteObject(fileRef)
      } catch (error) {
        console.error("Error deleting file from storage:", error)
        // Continue with document deletion even if file deletion fails
      }
    }
    
    // Delete associated transcription
    const transcriptionRef = collection(db, "transcription")
    const transcriptionQuery = query(transcriptionRef, where("audio_id", "==", audioId))
    const transcriptionSnapshot = await getDocs(transcriptionQuery)
    
    // Delete all associated transcriptions
    const deletePromises = transcriptionSnapshot.docs.map(async (doc) => {
      try {
        await deleteDoc(doc.ref)
        console.log(`Deleted transcription ${doc.id} for audio ${audioId}`)
      } catch (error: any) {
        console.error(`Error deleting transcription ${doc.id}:`, error)
        throw new Error(`Failed to delete transcription: ${error.message}`)
      }
    })
    
    // Wait for all transcription deletions to complete
    await Promise.all(deletePromises)
    
    // Delete the audio document from Firestore
    await deleteDoc(audioRef)
    console.log(`Deleted audio recording ${audioId}`)
    
    // Update user's contribution count only if the user is not an admin
    if (userData?.role !== "admin") {
      const userRef = doc(db, "users", userId)
      await updateDoc(userRef, {
        contribution_count: increment(-1),
        updated_at: new Date()
      })
    }
    
    return {
      success: true,
      message: "Recording and associated transcriptions deleted successfully"
    }
  } catch (error: any) {
    console.error("Error deleting audio recording:", error)
    throw {
      message: error?.message || "Failed to delete recording",
      code: error?.code || "DELETE_FAILED",
      details: error
    }
  }
}

export async function updateAudioRecording(audioId: string, updateData: AudioUpdateData) {
  try {
    const audioRef = doc(db, "audio", audioId)
    const audioDoc = await getDoc(audioRef)
    
    if (!audioDoc.exists()) {
      throw new Error("Audio recording not found")
    }
    
    const audioData = audioDoc.data()
    if (audioData.action !== 'pending') {
      throw new Error("Can only edit pending recordings")
    }
    
    // Update audio document
    await updateDoc(audioRef, {
      title: updateData.title,
      updated_at: new Date()
    })

    // Update transcription document
    const transcriptionRef = collection(db, "transcription")
    const transcriptionQuery = query(transcriptionRef, where("audio_id", "==", audioId))
    const transcriptionSnapshot = await getDocs(transcriptionQuery)
    
    if (!transcriptionSnapshot.empty) {
      const transcriptionDoc = transcriptionSnapshot.docs[0]
      await updateDoc(doc(db, "transcription", transcriptionDoc.id), {
        content: updateData.content,
        title: updateData.title,
        updated_at: new Date()
      })
    }
    
    return true
  } catch (error) {
    console.error("Error updating audio recording:", error)
    throw error
  }
}

export async function getPendingRecordings(): Promise<AudioRecording[]> {
  try {
    const response = await fetch('/api/recordings/pending')
    if (!response.ok) {
      throw new Error('Failed to fetch pending recordings')
    }
    const data = await response.json()
    return data.recordings
  } catch (error) {
    console.error('Error fetching pending recordings:', error)
    throw error
  }
}

export async function updateRecordingStatus(
  recordingId: string,
  updateData: {
    action: 'pending' | 'approved' | 'rejected';
    reviewed_by: string;
    reviewed_at: Date;
    feedback?: string;
  }
) {
  const audioRef = doc(db, "audio", recordingId)
  const audioDoc = await getDoc(audioRef)
  
  if (!audioDoc.exists()) {
    throw new Error("Recording not found")
  }
  
  const audioData = audioDoc.data()
  
  // Allow status changes for all recording states
  await updateDoc(audioRef, {
    action: updateData.action,
    reviewed_by: updateData.reviewed_by,
    reviewed_at: updateData.reviewed_at,
    feedback: updateData.feedback || null,
    updated_at: new Date()
  })
} 