@echo off
title Masalit AI Platform - Development Mode (HTTPS)
color 0B

echo.
echo ========================================================
echo  Masalit AI Platform - Development Mode with HTTPS
echo ========================================================
echo.

REM Check if SSL certificates exist
if not exist "ssl\cert.pem" (
    echo ❌ SSL certificates not found!
    echo.
    echo Please run setup-ssl-windows.bat first to generate certificates.
    echo.
    pause
    exit /b 1
)

if not exist "ssl\key.pem" (
    echo ❌ SSL private key not found!
    echo.
    echo Please run setup-ssl-windows.bat first to generate certificates.
    echo.
    pause
    exit /b 1
)

echo ✅ SSL certificates found
echo.

REM Create logs directory if it doesn't exist
if not exist "logs" mkdir logs

echo [1/3] Starting Backend with SSL (FastAPI)...
echo Backend will be available at: https://buragatechnologies.com:8000
echo API Documentation: https://buragatechnologies.com:8000/docs
echo.

REM Start backend with SSL in a new window
start "Masalit AI - Backend (SSL)" cmd /k "python start-backend.py --ssl && echo Backend stopped. Press any key to close... && pause"

REM Wait for backend to start
echo Waiting for backend to initialize...
timeout /t 5 /nobreak > nul

echo [2/3] Starting Frontend (Next.js)...
echo Frontend will be available at: https://buragatechnologies.com
echo.

REM Check if pnpm is available, otherwise use npm
where pnpm >nul 2>nul
if %errorlevel% == 0 (
    echo Using pnpm...
    start "Masalit AI - Frontend" cmd /k "pnpm dev && echo Frontend stopped. Press any key to close... && pause"
) else (
    echo Using npm...
    start "Masalit AI - Frontend" cmd /k "npm run dev && echo Frontend stopped. Press any key to close... && pause"
)

REM Wait for frontend to start
echo Waiting for frontend to initialize...
timeout /t 3 /nobreak > nul

echo [3/3] HTTPS Development Environment Ready!
echo.
echo ========================================================
echo  🔒 HTTPS DEVELOPMENT SERVICES RUNNING
echo ========================================================
echo  Frontend:     https://buragatechnologies.com
echo  Backend:      https://buragatechnologies.com:8000
echo  API Docs:     https://buragatechnologies.com:8000/docs
echo  Health Check: https://buragatechnologies.com:8000/health
echo ========================================================
echo.
echo 📝 Logs are saved in the 'logs' directory
echo 🔧 Both services are running in separate windows
echo 🛑 Close the service windows to stop the servers
echo.
echo ⚠️  Your browser will show a security warning for
echo    self-signed certificates. Click "Advanced" and
echo    "Proceed to buragatechnologies.com" to continue.
echo.
echo Press any key to open the application in your browser...
pause > nul

REM Open the application in default browser
start https://buragatechnologies.com

echo.
echo ✅ HTTPS development environment is ready!
echo    Frontend opened in your default browser.
echo.
echo Press any key to exit this window...
pause > nul
