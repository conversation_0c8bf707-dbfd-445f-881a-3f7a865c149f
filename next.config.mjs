/** @type {import('next').NextConfig} */
const isDevelopment = process.env.NODE_ENV === 'development'

const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=31536000; includeSubDomains',
          },
          {
            key: 'Content-Security-Policy',
            value: isDevelopment
              ? [
                  // More permissive CSP for development
                  "default-src 'self'",
                  "script-src 'self' 'unsafe-eval' 'unsafe-inline' https: http:",
                  "style-src 'self' 'unsafe-inline' https: http:",
                  "img-src 'self' data: https: http: blob:",
                  "font-src 'self' data: https: http:",
                  "media-src 'self' blob: https://firebasestorage.googleapis.com https://storage.googleapis.com",
                  "connect-src 'self' http://127.0.0.1:8000 http://localhost:8000 http://localhost:3000 https: wss: ws:",
                  "frame-src 'self' https: http:",
                  "worker-src 'self' blob:",
                  "object-src 'none'",
                  "base-uri 'self'"
                ].join('; ') + ';'
              : [
                  // Strict CSP for production
                  "default-src 'self'",
                  "script-src 'self' 'unsafe-eval' 'unsafe-inline' https://www.google.com https://www.gstatic.com https://www.googletagmanager.com https://apis.google.com",
                  "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
                  "img-src 'self' data: https: blob:",
                  "font-src 'self' data: https://fonts.gstatic.com",
                  "media-src 'self' blob: https://firebasestorage.googleapis.com https://storage.googleapis.com",
                  "connect-src 'self' https://buragatechnologies.com https://www.google.com https://firebase.googleapis.com https://securetoken.googleapis.com https://firestore.googleapis.com https://identitytoolkit.googleapis.com https://www.googleapis.com https://storage.googleapis.com https://firebasestorage.googleapis.com",
                  "frame-src 'self' https://www.google.com https://www.recaptcha.net",
                  "worker-src 'self' blob:",
                  "object-src 'none'",
                  "base-uri 'self'"
                ].join('; ') + ';',
          },
        ],
      },
    ]
  },
}

export default nextConfig
