"""
Health check API endpoints
"""

import psutil
import GPUtil
import time
from fastapi import APIRouter, HTTPException
from datetime import datetime
from backend.services.firebase import firebase_service
from backend.services.firebase_clean import clean_firebase_service
from backend.services.gcs import gcs_service

# Store startup time for uptime calculation
startup_time = time.time()

router = APIRouter()

@router.get("/health")
async def health_check():
    """Basic health check endpoint"""
    uptime_seconds = int(time.time() - startup_time)
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "Masalit AI Platform Backend",
        "uptime": uptime_seconds
    }

@router.get("/health/detailed")
async def detailed_health_check():
    """Detailed health check with system information"""
    try:
        # System information
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # GPU information
        gpus = []
        try:
            gpu_list = GPUtil.getGPUs()
            for gpu in gpu_list:
                gpus.append({
                    "id": gpu.id,
                    "name": gpu.name,
                    "load": f"{gpu.load * 100:.1f}%",
                    "memory_used": f"{gpu.memoryUsed}MB",
                    "memory_total": f"{gpu.memoryTotal}MB",
                    "temperature": f"{gpu.temperature}°C"
                })
        except Exception:
            gpus = []
        
        # Service status
        services = {
            "firebase": "unknown",
            "gcs": "unknown"
        }
        
        # Check Firebase
        try:
            if firebase_service._initialized:
                services["firebase"] = "connected"
            else:
                services["firebase"] = "not_initialized"
        except Exception:
            services["firebase"] = "error"
        
        # Check GCS
        try:
            if gcs_service._initialized:
                services["gcs"] = "connected"
            else:
                services["gcs"] = "not_initialized"
        except Exception:
            services["gcs"] = "error"
        
        uptime_seconds = int(time.time() - startup_time)

        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "system": {
                "cpu_usage": f"{cpu_percent}%",
                "memory": {
                    "used": f"{memory.used / (1024**3):.1f}GB",
                    "total": f"{memory.total / (1024**3):.1f}GB",
                    "percent": f"{memory.percent}%"
                },
                "disk": {
                    "used": f"{disk.used / (1024**3):.1f}GB",
                    "total": f"{disk.total / (1024**3):.1f}GB",
                    "percent": f"{(disk.used / disk.total) * 100:.1f}%"
                },
                "gpus": gpus,
                "uptime": uptime_seconds
            },
            "services": services
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")

@router.get("/health/ready")
async def readiness_check():
    """Readiness check for deployment"""
    try:
        # Check if essential services are ready
        ready = True
        services = {}
        
        # Check Firebase
        try:
            firebase_service.initialize()
            services["firebase"] = "ready"
        except Exception as e:
            services["firebase"] = f"not_ready: {str(e)}"
            ready = False
        
        # Check GCS
        try:
            gcs_service.initialize()
            services["gcs"] = "ready"
        except Exception as e:
            services["gcs"] = f"not_ready: {str(e)}"
            ready = False
        
        return {
            "ready": ready,
            "timestamp": datetime.now().isoformat(),
            "services": services
        }
        
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Readiness check failed: {str(e)}")

@router.get("/health/live")
async def liveness_check():
    """Liveness check for deployment"""
    return {
        "alive": True,
        "timestamp": datetime.now().isoformat()
    }

@router.post("/health/monitor")
async def store_monitoring_data():
    """Store current system monitoring data to Firebase"""
    try:
        # Get current system status
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')

        # GPU information
        gpus = []
        try:
            gpu_list = GPUtil.getGPUs()
            for gpu in gpu_list:
                gpus.append({
                    "id": gpu.id,
                    "name": gpu.name,
                    "load": gpu.load * 100,
                    "memory_used": gpu.memoryUsed,
                    "memory_total": gpu.memoryTotal,
                    "temperature": gpu.temperature
                })
        except Exception:
            gpus = []

        # Prepare monitoring data
        monitoring_data = {
            "cpu_usage_percent": cpu_percent,
            "memory_used_gb": memory.used / (1024**3),
            "memory_total_gb": memory.total / (1024**3),
            "memory_percent": memory.percent,
            "disk_used_gb": disk.used / (1024**3),
            "disk_total_gb": disk.total / (1024**3),
            "disk_percent": (disk.used / disk.total) * 100,
            "gpus": gpus,
            "uptime_seconds": int(time.time() - startup_time),
            "timestamp": datetime.now().isoformat()
        }

        # Store to clean Firebase structure
        clean_firebase_service.initialize()
        clean_firebase_service.update_monitoring_data('system_health', monitoring_data)

        return {
            "message": "Monitoring data stored successfully",
            "timestamp": datetime.now().isoformat(),
            "data": monitoring_data
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to store monitoring data: {str(e)}")

@router.get("/health/history")
async def get_monitoring_history():
    """Get recent monitoring data history"""
    try:
        # This would require implementing a method to retrieve monitoring history
        # For now, return a placeholder
        return {
            "message": "Monitoring history retrieval not yet implemented",
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get monitoring history: {str(e)}")
