"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { <PERSON>ert, AlertDescription } from "@/components/ui/alert"
import { Skeleton } from "@/components/ui/skeleton"
import Link from "next/link"
import {
  Mic,
  Volume2,
  Activity,
  ArrowRight,
  RefreshCw,
  AlertCircle,
  CheckCircle2,
  XCircle,
  Clock,
  TrendingUp,
  Database,
  FileText,
  Settings,
  Play,
  Pause,
  BarChart3
} from "lucide-react"
import { checkServerStatus } from "@/lib/server-status"
import { cn } from "@/lib/utils"
import { AISystemStats, ModelCard, ActivityFeed } from "@/components/dashboard/ai-dashboard-stats"
import { db } from "@/lib/firebase"
import { doc, getDoc, collection, query, orderBy, limit, getDocs, where } from "firebase/firestore"

interface ServerStatus {
  isActive: boolean
  status: string
  uptime: number
  lastCheck: string
  resources?: {
    cpu: number
    memory: number
    storage: number
    memoryUsed?: string
    memoryTotal?: string
    storageUsed?: string
    storageTotal?: string
  }
  firebase: boolean
  backendUrl?: string
}

interface ModelStatus {
  status: string
  accuracy?: number
  lastTraining?: string
  version?: string
}

export default function AIDashboardPage() {
  const [serverStatus, setServerStatus] = useState<ServerStatus | null>(null)
  const [asrStatus, setAsrStatus] = useState<ModelStatus>({ status: 'unknown' })
  const [ttsStatus, setTtsStatus] = useState<ModelStatus>({ status: 'unknown' })
  const [loading, setLoading] = useState(true)
  const [lastError, setLastError] = useState<string | null>(null)
  const [activities, setActivities] = useState<Array<{
    id: string
    type: 'info' | 'success' | 'warning' | 'error'
    message: string
    timestamp: string
  }>>([]);
  const [totalLogCount, setTotalLogCount] = useState<number>(0)

  // Safe Firestore query helper
  const safeFirestoreQuery = async (queryFn: () => Promise<any>, errorContext: string) => {
    try {
      return await queryFn()
    } catch (error) {
      console.error(`${errorContext}:`, error)
      if (error instanceof Error && error.message.includes('index')) {
        console.warn(`Firestore index required for ${errorContext}. Skipping this query.`)
      }
      return null
    }
  }

  // Fetch real ASR training status from Firestore
  const fetchASRStatus = async (): Promise<ModelStatus> => {
    try {
      const statusDoc = await getDoc(doc(db, 'training_status', 'asr'))
      if (statusDoc.exists()) {
        const data = statusDoc.data()
        return {
          status: data.status === 'training' ? 'training' :
                  data.status === 'completed' ? 'active' :
                  data.status === 'not_started' ? 'inactive' : 'unknown',
          accuracy: data.current_accuracy || 85, // Temporary test value
          lastTraining: data.status === 'training' ? 'Currently training' :
                       data.end_time ? new Date(data.end_time).toLocaleDateString() : 'Never',
          version: data.model_version || 'No model'
        }
      }
      return { status: 'inactive', accuracy: 85 }
    } catch (error) {
      console.error('Error fetching ASR status:', error)
      return { status: 'error', accuracy: 85 }
    }
  }

  // Fetch real TTS training status from Firestore
  const fetchTTSStatus = async (): Promise<ModelStatus> => {
    try {
      const statusDoc = await getDoc(doc(db, 'training_status', 'tts'))
      if (statusDoc.exists()) {
        const data = statusDoc.data()
        return {
          status: data.status === 'training' ? 'training' :
                  data.status === 'completed' ? 'active' :
                  data.status === 'not_started' ? 'inactive' : 'unknown',
          accuracy: data.current_accuracy || 78, // Temporary test value
          lastTraining: data.status === 'training' ? 'Currently training' :
                       data.end_time ? new Date(data.end_time).toLocaleDateString() : 'Never',
          version: data.model_version || 'No model'
        }
      }
      return { status: 'inactive', accuracy: 78 }
    } catch (error) {
      console.error('Error fetching TTS status:', error)
      return { status: 'error', accuracy: 78 }
    }
  }

  // Fetch total log count from backend
  const fetchTotalLogCount = async () => {
    try {
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000'
      const response = await fetch(`${backendUrl}/api/logs/stats`)
      if (response.ok) {
        const data = await response.json()
        setTotalLogCount(data.total_logs || 0)
      }
    } catch (error) {
      console.log('Could not fetch log count from backend')
    }
  }

  // Fetch real activity data from Firestore
  const fetchActivities = async () => {
    try {
      const activities: Array<{
        id: string
        type: 'info' | 'success' | 'warning' | 'error'
        message: string
        timestamp: string
      }> = []

      // Get recent training activities
      const trainingSnapshot = await safeFirestoreQuery(
        () => getDocs(query(
          collection(db, 'training_status'),
          orderBy('updated_at', 'desc'),
          limit(5)
        )),
        'training activities'
      )

      if (trainingSnapshot) {
        trainingSnapshot.forEach((doc: any) => {
          const data = doc.data()
          if (data.updated_at) {
            activities.push({
              id: `training-${doc.id}`,
              type: data.status === 'completed' ? 'success' :
                    data.status === 'training' ? 'info' :
                    data.status === 'error' ? 'error' : 'info',
              message: `${doc.id.toUpperCase()} training ${data.status}`,
              timestamp: data.updated_at
            })
          }
        })
      }

      // Get recent audio uploads (without composite index requirement)
      const audioSnapshot = await safeFirestoreQuery(
        () => getDocs(query(
          collection(db, 'audio'),
          orderBy('created_at', 'desc'),
          limit(10)
        )),
        'audio activities'
      )

      if (audioSnapshot) {
        // Process audio documents with hierarchical structure
        for (const audioDoc of audioSnapshot.docs) {
          const data = audioDoc.data()

          try {
            // Check review status from subcollection
            const reviewStatusRef = doc(db, 'audio', audioDoc.id, 'review', 'status')
            const reviewStatusDoc = await getDoc(reviewStatusRef)

            if (reviewStatusDoc.exists()) {
              const reviewData = reviewStatusDoc.data()

              // Only include approved audio with valid data
              if (reviewData.action === 'approved' && data.title) {
                activities.push({
                  id: `audio-${audioDoc.id}`,
                  type: 'success' as const,
                  message: `New audio "${data.title}" approved for training`,
                  timestamp: data.created_at?.toDate?.()?.toISOString() || data.created_at
                })
              }
            }
          } catch (error) {
            console.warn(`Error processing audio ${audioDoc.id} for activities:`, error)
          }
        }
      }

      return activities.slice(0, 10) // Show more recent activities
    } catch (error) {
      console.error('Error fetching activities:', error)
      return []
    }
  }



  const fetchStatus = async () => {
    try {
      setLastError(null)
      const status = await checkServerStatus()
      setServerStatus(status)

      // Fetch real ASR and TTS status from Firestore
      const [asrStatusData, ttsStatusData] = await Promise.all([
        fetchASRStatus(),
        fetchTTSStatus()
      ])

      setAsrStatus(asrStatusData)
      setTtsStatus(ttsStatusData)

      // Fetch real activities from Firestore
      const realActivities = await fetchActivities()

      // Fetch total log count from backend
      await fetchTotalLogCount()

      // Add server status to activities
      const serverActivity = {
        id: 'server-status',
        type: status.isActive ? 'success' as const : 'error' as const,
        message: status.isActive ? 'Backend server is online and ready' : 'Backend server is offline',
        timestamp: status.lastCheck
      }

      // Ensure realActivities is always an array
      const safeRealActivities = Array.isArray(realActivities) ? realActivities : []
      setActivities([serverActivity, ...safeRealActivities])

    } catch (error) {
      console.error('Error fetching status:', error)
      setLastError(error instanceof Error ? error.message : 'Unknown error')

      // Still try to get Firestore data even if server is down
      try {
        const [asrStatusData, ttsStatusData] = await Promise.all([
          fetchASRStatus(),
          fetchTTSStatus()
        ])

        setAsrStatus(asrStatusData)
        setTtsStatus(ttsStatusData)

        const realActivities = await fetchActivities()
        // Ensure realActivities is always an array
        const safeRealActivities = Array.isArray(realActivities) ? realActivities : []
        setActivities([
          {
            id: 'fetch-error',
            type: 'error',
            message: `Backend server error: ${error instanceof Error ? error.message : 'Unknown error'}`,
            timestamp: new Date().toISOString()
          },
          ...safeRealActivities
        ])
      } catch (firestoreError) {
        console.error('Error fetching Firestore data:', firestoreError)
        setAsrStatus({ status: 'error', accuracy: 85 })
        setTtsStatus({ status: 'error', accuracy: 78 })
        setActivities([{
          id: 'firestore-error',
          type: 'error',
          message: `Failed to fetch data: ${firestoreError instanceof Error ? firestoreError.message : 'Database unavailable'}`,
          timestamp: new Date().toISOString()
        }])
      }
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchStatus()
    const interval = setInterval(fetchStatus, 30000) // Refresh every 30 seconds
    return () => clearInterval(interval)
  }, [])



  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-bold tracking-tight">AI Dashboard</h3>
          <p className="text-muted-foreground">
            Manage and monitor AI models for Masalit language processing
          </p>
        </div>
        <Button
          onClick={fetchStatus}
          disabled={loading}
          variant="outline"
          size="sm"
        >
          {loading ? (
            <RefreshCw className="h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4" />
          )}
          Refresh
        </Button>
      </div>

      {/* System Status Alert */}
      {lastError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Error connecting to backend: {lastError}
          </AlertDescription>
        </Alert>
      )}

      {/* System Overview */}
      <AISystemStats
        stats={serverStatus ? {
          cpu: serverStatus.resources?.cpu || 0,
          memory: serverStatus.resources?.memory || 0,
          storage: serverStatus.resources?.storage || 0,
          isOnline: serverStatus.isActive,
          uptime: serverStatus.uptime,
          backendUrl: serverStatus.backendUrl,
          memoryUsed: serverStatus.resources?.memoryUsed,
          memoryTotal: serverStatus.resources?.memoryTotal,
          storageUsed: serverStatus.resources?.storageUsed,
          storageTotal: serverStatus.resources?.storageTotal
        } : null}
        loading={loading}
      />

      {/* Model Status Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Link href="/dashboard/ai/asr">
          <ModelCard
            title="ASR Training"
            description="Automatic Speech Recognition"
            icon={<Mic className="h-5 w-5" />}
            status={asrStatus.status}
            accuracy={asrStatus.accuracy}
            lastTraining={asrStatus.lastTraining}
            version={asrStatus.version}
            href="/dashboard/ai/asr"
            loading={loading}
            isServerOnline={serverStatus?.isActive || false}
          />
        </Link>

        <Link href="/dashboard/ai/tts">
          <ModelCard
            title="TTS Training"
            description="Text-to-Speech Generation"
            icon={<Volume2 className="h-5 w-5" />}
            status={ttsStatus.status}
            accuracy={ttsStatus.accuracy}
            lastTraining={ttsStatus.lastTraining}
            version={ttsStatus.version}
            href="/dashboard/ai/tts"
            loading={loading}
            isServerOnline={serverStatus?.isActive || false}
          />
        </Link>

        <Link href="/dashboard/ai/logs">
          <Card className="hover:bg-accent/50 transition-colors cursor-pointer h-full">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Backend Logs
                </CardTitle>
                <Badge variant="outline" className="text-xs">
                  Live
                </Badge>
              </div>
              <CardDescription>Real-time system logs and training events</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {loading ? (
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-2 w-full" />
                    <Skeleton className="h-8 w-24" />
                  </div>
                ) : (
                  <>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Log Streaming</span>
                        <span className="text-sm font-medium text-blue-500">
                          {serverStatus?.isActive ? 'Active' : 'Offline'}
                        </span>
                      </div>
                      <Progress
                        value={serverStatus?.isActive ? 100 : 0}
                        className="h-2"
                        showDynamicColors={true}
                        colorScheme="inverse"
                      />
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Recent activity:</span>
                      <span>{Array.isArray(activities) ? activities.length : 0} events</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Total logs:</span>
                      <span>{totalLogCount} entries</span>
                    </div>
                    <div className="flex justify-end">
                      <Button variant="ghost" size="sm">
                        View Logs <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </Link>
      </div>

      {/* Quick Actions */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Quick Actions
            </CardTitle>
            <CardDescription>Common AI model operations</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3">
              <Link href="/dashboard/ai/asr">
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  disabled={!serverStatus?.isActive}
                >
                  <Play className="mr-2 h-4 w-4" />
                  Start ASR Training
                </Button>
              </Link>
              <Link href="/dashboard/ai/tts">
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  disabled={!serverStatus?.isActive}
                >
                  <Play className="mr-2 h-4 w-4" />
                  Start TTS Training
                </Button>
              </Link>
              <Link href="/dashboard/ai/logs">
                <Button variant="outline" className="w-full justify-start">
                  <FileText className="mr-2 h-4 w-4" />
                  View Training Logs
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        <ActivityFeed
          activities={activities}
          loading={loading}
        />
      </div>

      {/* Training Queue Status */}
      {serverStatus?.isActive && (
        <Card>
          <CardHeader>
            <CardTitle>Training Queue</CardTitle>
            <CardDescription>Current training tasks and schedule</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <Mic className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="font-medium">ASR Training</p>
                    <p className="text-sm text-muted-foreground">
                      {asrStatus.status === 'training' ? 'Currently training...' : 'Ready to start'}
                    </p>
                  </div>
                </div>
                <Link href="/dashboard/ai/asr">
                  <Button
                    variant={asrStatus.status === 'training' ? "secondary" : "default"}
                    size="sm"
                  >
                    {asrStatus.status === 'training' ? (
                      <>
                        <Pause className="mr-2 h-4 w-4" />
                        View Progress
                      </>
                    ) : (
                      <>
                        <Play className="mr-2 h-4 w-4" />
                        Start Training
                      </>
                    )}
                  </Button>
                </Link>
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <Volume2 className="h-5 w-5 text-purple-500" />
                  <div>
                    <p className="font-medium">TTS Training</p>
                    <p className="text-sm text-muted-foreground">
                      {ttsStatus.status === 'training' ? 'Currently training...' : 'Ready to start'}
                    </p>
                  </div>
                </div>
                <Link href="/dashboard/ai/tts">
                  <Button
                    variant={ttsStatus.status === 'training' ? "secondary" : "default"}
                    size="sm"
                  >
                    {ttsStatus.status === 'training' ? (
                      <>
                        <Pause className="mr-2 h-4 w-4" />
                        View Progress
                      </>
                    ) : (
                      <>
                        <Play className="mr-2 h-4 w-4" />
                        Start Training
                      </>
                    )}
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}