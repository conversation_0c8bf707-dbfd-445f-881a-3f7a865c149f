"""
TTS Training Pipeline using VITS (Variational Inference with adversarial learning for end-to-end Text-to-Speech)
"""

import os
import json
import logging
import asyncio
import tempfile
import tarfile
import librosa
import numpy as np
import subprocess
import queue
import threading
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

from backend.config.settings import settings as app_settings
from backend.services.firebase_clean import clean_firebase_service
from backend.services.gcs import gcs_service
from backend.services.logging_service import log_training_event

logger = logging.getLogger(__name__)

class TTSTrainer:
    """TTS training pipeline using VITS model"""

    def __init__(self):
        self.model = None
        self.is_training = False
        self.training_task = None
        self.status_queue = queue.Queue(maxsize=100)
        self.status_update_task = None
    
    async def start_training(self, training_settings: Dict[str, Any]) -> Dict[str, Any]:
        """Start TTS training with given settings"""
        try:
            if self.is_training:
                return {"error": "Training is already in progress"}

            # Add start time to training settings
            training_settings['start_time'] = datetime.now().isoformat()

            # Merge with saved TTS settings from Firestore
            try:
                clean_firebase_service.initialize()
                saved_settings = clean_firebase_service.get_settings('tts_training')
                # Merge saved settings with provided settings (provided settings take precedence)
                merged_settings = {**saved_settings, **training_settings}
                training_settings = merged_settings
                logger.info(f"Merged TTS training settings: {training_settings}")
            except Exception as e:
                logger.warning(f"Could not load saved TTS settings, using provided settings: {e}")

            # Update status to starting
            await self._update_status({
                "status": "starting",
                "message": "Initializing TTS training...",
                "progress": 0
            })

            # Start status update handler
            self.status_update_task = asyncio.create_task(
                self._handle_status_updates()
            )

            # Start training in background
            self.training_task = asyncio.create_task(
                self._train_model(training_settings)
            )

            # Log training start event
            log_training_event("tts", "started", {
                "settings": training_settings,
                "timestamp": datetime.now().isoformat()
            })

            return {"message": "TTS training started successfully", "status": "started"}

        except Exception as e:
            logger.error(f"Error starting TTS training: {e}")
            await self._update_status({
                "status": "failed",
                "error": str(e),
                "progress": 0
            })
            return {"error": str(e)}

    async def _handle_status_updates(self):
        """Handle status updates from the training thread"""
        try:
            logger.info("TTS status update handler started")
            while True:
                try:
                    # Get status update from queue with timeout
                    status_data = self.status_queue.get(timeout=1.0)
                    logger.info(f"Processing TTS status update: {status_data.get('status', 'unknown')}")
                    await self._update_status(status_data)
                    self.status_queue.task_done()
                except queue.Empty:
                    # If training is done and queue is empty, exit
                    if not self.is_training:
                        logger.info("TTS training finished, checking for remaining queue items...")
                        # Wait a bit more to ensure all updates are processed
                        await asyncio.sleep(0.5)
                        if self.status_queue.empty():
                            logger.info("Queue empty, exiting TTS status update handler")
                            break
                    continue
                except Exception as e:
                    logger.error(f"Error handling TTS status update: {e}")
                    continue
        except asyncio.CancelledError:
            logger.info("TTS status update handler cancelled")
        except Exception as e:
            logger.error(f"Error in TTS status update handler: {e}")

    async def stop_training(self) -> Dict[str, Any]:
        """Stop current TTS training"""
        try:
            if not self.is_training:
                return {"message": "No TTS training in progress"}

            if self.training_task:
                self.training_task.cancel()

            # Cancel status update task
            if self.status_update_task:
                self.status_update_task.cancel()
                self.status_update_task = None

            self.is_training = False

            await self._update_status({
                "status": "stopped",
                "message": "TTS training stopped by user",
                "progress": 0
            })

            return {"message": "TTS training stopped successfully"}

        except Exception as e:
            logger.error(f"Error stopping TTS training: {e}")
            return {"error": str(e)}
    
    async def get_training_status(self) -> Dict[str, Any]:
        """Get current TTS training status"""
        try:
            # Initialize Firebase if not already done
            clean_firebase_service.initialize()

            # Get status from new clean structure - check for active TTS training sessions
            training_docs = clean_firebase_service.db.collection('training').where('model_type', '==', 'tts').where('status', 'in', ['training', 'preparing_data']).limit(1).get()

            if training_docs:
                # Get progress from active session
                doc = training_docs[0]
                session_id = doc.id
                progress_ref = doc.reference.collection('progress').document('current')
                progress_doc = progress_ref.get()

                if progress_doc.exists:
                    return progress_doc.to_dict()
                else:
                    # Return basic session info
                    session_data = doc.to_dict()
                    return {
                        "status": session_data.get('status', 'unknown'),
                        "progress": 0,
                        "current_epoch": 0,
                        "total_epochs": 0,
                        "message": f"TTS training session {session_id} in progress"
                    }
            else:
                # No active training sessions
                return {
                    "status": "not_started",
                    "progress": 0,
                    "current_epoch": 0,
                    "total_epochs": 0,
                    "message": "No TTS training has been started yet"
                }

        except Exception as e:
            logger.error(f"Error getting TTS training status: {e}")
            return {
                "status": "error",
                "error": str(e),
                "progress": 0,
                "current_epoch": 0,
                "total_epochs": 0
            }
    
    async def _train_model(self, training_settings: Dict[str, Any]):
        """Main TTS training loop - placeholder for VITS implementation"""
        try:
            logger.info("=== STARTING TTS TRAINING PIPELINE ===")
            self.is_training = True

            # Step 1: Load training data from Firestore
            await self._update_status({
                "status": "loading_data",
                "message": "Loading TTS training data from Firestore...",
                "progress": 5
            })

            # Use clean firebase service for training data
            clean_firebase_service.initialize()
            training_data = clean_firebase_service.get_training_data(
                model_type='tts',
                max_samples=training_settings.get('number_of_samples', 100)
            )
            if not training_data:
                raise Exception("No TTS training data available")

            logger.info(f"Retrieved {len(training_data)} TTS samples from Firebase")

            # Step 2: Prepare TTS dataset
            await self._update_status({
                "status": "preparing_data",
                "message": "Preparing TTS dataset...",
                "progress": 25
            })

            # TODO: Implement VITS dataset preparation
            logger.info("TTS dataset preparation - placeholder implementation")

            # Step 3: Initialize VITS model
            await self._update_status({
                "status": "loading_model",
                "message": "Loading VITS model...",
                "progress": 40
            })

            # TODO: Implement VITS model loading
            logger.info("VITS model loading - placeholder implementation")

            # Step 4: Train VITS model
            await self._update_status({
                "status": "training",
                "message": "Training VITS model...",
                "progress": 50
            })

            # TODO: Implement actual VITS training
            logger.info("VITS training - placeholder implementation")
            
            # Simulate training progress
            epochs = training_settings.get('epochs', 100)
            for epoch in range(epochs):
                if not self.is_training:  # Check if training was cancelled
                    break
                    
                progress = 50 + (epoch / epochs) * 40
                await self._update_status({
                    "status": "training",
                    "message": f"Training epoch {epoch + 1}/{epochs}",
                    "progress": progress,
                    "current_epoch": epoch + 1,
                    "total_epochs": epochs
                })
                
                # Simulate epoch duration
                await asyncio.sleep(0.1)

            # Step 5: Save model
            await self._update_status({
                "status": "saving",
                "message": "Saving TTS model...",
                "progress": 90
            })

            model_version = f"vits-masalit-{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            # TODO: Implement model saving
            logger.info(f"TTS model saving - placeholder implementation: {model_version}")

            # Step 6: Mark data as trained
            audio_ids = [item['audio_id'] for item in training_data]
            session_id = f"tts_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            clean_firebase_service.mark_data_as_trained(audio_ids, session_id, model_type='tts')

            # Step 7: Complete
            await self._update_status({
                "status": "completed",
                "message": "TTS training completed successfully",
                "progress": 100,
                "model_version": model_version,
                "samples_trained": len(audio_ids)
            })

            logger.info("=== TTS TRAINING PIPELINE COMPLETED ===")

            # Log training completion event
            log_training_event("tts", "completed", {
                "samples_trained": len(audio_ids),
                "model_version": model_version,
                "end_time": datetime.now().isoformat()
            })

            return {"message": "TTS training completed successfully", "status": "completed"}

        except asyncio.CancelledError:
            await self._update_status({
                "status": "stopped",
                "message": "TTS training was cancelled",
                "progress": 0
            })
        except Exception as e:
            logger.error(f"TTS training failed: {e}")

            # Log training failure event
            log_training_event("tts", "failed", {
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })

            await self._update_status({
                "status": "failed",
                "error": str(e),
                "progress": 0
            })
        finally:
            logger.info("Entering TTS training cleanup")
            self.is_training = False

            # Cancel status update task if it exists
            if self.status_update_task:
                logger.info("Cancelling TTS status update task...")
                self.status_update_task.cancel()
                try:
                    await asyncio.wait_for(self.status_update_task, timeout=2.0)
                except (asyncio.CancelledError, asyncio.TimeoutError):
                    pass
                self.status_update_task = None
                logger.info("TTS status update task cancelled")

            logger.info("TTS training cleanup completed")

    async def _update_status(self, status_data: Dict[str, Any]):
        """Update TTS training status in clean Firebase structure"""
        try:
            clean_firebase_service.initialize()

            # Create or update a current TTS training session
            session_id = f"tts_current_{datetime.now().strftime('%Y%m%d')}"

            # Update progress in the training session
            training_ref = clean_firebase_service.db.collection('training').document(session_id)

            # Ensure main document exists
            training_ref.set({
                'session_id': session_id,
                'model_type': 'tts',
                'status': status_data.get('status', 'training'),
                'updated_at': datetime.now().isoformat()
            }, merge=True)

            # Update progress
            progress_ref = training_ref.collection('progress').document('current')
            progress_ref.set({
                **status_data,
                'updated_at': datetime.now().isoformat()
            }, merge=True)

        except Exception as e:
            logger.error(f"Error updating TTS status: {e}")

# Global TTS trainer instance
tts_trainer = TTSTrainer()
