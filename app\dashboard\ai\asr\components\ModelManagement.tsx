import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { useState, useEffect } from "react"
import { useToast } from "@/hooks/use-toast"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Package, Tag, Trash2, GitCompare } from "lucide-react"
import { Model, ModelComparison, listModels, compareModels, tagModel, cleanupModels } from "@/lib/asr"

interface ModelManagementProps {
  showComparisonOnly?: boolean
}

export function ModelManagement({ showComparisonOnly = false }: ModelManagementProps) {
  const { toast } = useToast()
  const [isCompareO<PERSON>, setIsCompareOpen] = useState(false)
  const [isTagOpen, setIsTagOpen] = useState(false)
  const [isCleanupOpen, setIsCleanupOpen] = useState(false)
  const [selectedModel1, setSelectedModel1] = useState("")
  const [selectedModel2, setSelectedModel2] = useState("")
  const [selectedModelForTag, setSelectedModelForTag] = useState("")
  const [tags, setTags] = useState("")
  const [daysToKeep, setDaysToKeep] = useState("30")
  const [keepTagged, setKeepTagged] = useState(true)
  const [models, setModels] = useState<Model[]>([])
  const [comparison, setComparison] = useState<ModelComparison | null>(null)

  useEffect(() => {
    loadModels()
  }, [])

  const loadModels = async () => {
    try {
      const modelsData = await listModels()
      setModels(modelsData)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load models",
        variant: "destructive",
      })
    }
  }

  const handleCompare = async () => {
    try {
      if (!selectedModel1 || !selectedModel2) {
        toast({
          title: "Error",
          description: "Please select two models to compare",
          variant: "destructive",
        })
        return
      }

      const result = await compareModels(selectedModel1, selectedModel2)
      setComparison(result)
      setIsCompareOpen(false)

      toast({
        title: "Comparison complete",
        description: "Model comparison results updated",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to compare models",
        variant: "destructive",
      })
    }
  }

  const handleTag = async () => {
    try {
      if (!selectedModelForTag || !tags) {
        toast({
          title: "Error",
          description: "Please select a model and enter tags",
          variant: "destructive",
        })
        return
      }

      const tagList = tags.split(",").map(tag => tag.trim())
      await tagModel(selectedModelForTag, tagList)
      setIsTagOpen(false)

      toast({
        title: "Tags updated",
        description: "Model tags have been updated successfully",
      })
      
      // Reload models to get updated tags
      await loadModels()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update model tags",
        variant: "destructive",
      })
    }
  }

  const handleCleanup = async () => {
    try {
      const days = parseInt(daysToKeep)
      if (isNaN(days) || days < 1) {
        toast({
          title: "Error",
          description: "Please enter a valid number of days",
          variant: "destructive",
        })
        return
      }

      const result = await cleanupModels(days, keepTagged)
      setIsCleanupOpen(false)

      toast({
        title: "Cleanup complete",
        description: `Deleted ${result.deleted} models, skipped ${result.skipped} tagged models`,
      })
      
      // Reload models after cleanup
      await loadModels()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to clean up models",
        variant: "destructive",
      })
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{showComparisonOnly ? "Model Comparison" : "Model Management"}</CardTitle>
        <CardDescription>{showComparisonOnly ? "Compare ASR model performance" : "Manage and compare ASR models"}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className={showComparisonOnly ? "grid grid-cols-1 gap-4" : "grid grid-cols-2 gap-4"}>
            <Dialog open={isCompareOpen} onOpenChange={setIsCompareOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" className="w-full">
                  <GitCompare className="mr-2 h-4 w-4" />
                  Compare Models
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Compare Models</DialogTitle>
                  <DialogDescription>
                    Select two models to compare their performance
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label>Model 1</Label>
                    <Select value={selectedModel1} onValueChange={setSelectedModel1}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select model" />
                      </SelectTrigger>
                      <SelectContent>
                        {models.map((model) => (
                          <SelectItem key={model.id} value={model.id}>
                            {model.version}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Model 2</Label>
                    <Select value={selectedModel2} onValueChange={setSelectedModel2}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select model" />
                      </SelectTrigger>
                      <SelectContent>
                        {models.map((model) => (
                          <SelectItem key={model.id} value={model.id}>
                            {model.version}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <DialogFooter>
                  <Button onClick={handleCompare}>Compare</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            {!showComparisonOnly && (
              <>
                <Dialog open={isTagOpen} onOpenChange={setIsTagOpen}>
                  <DialogTrigger asChild>
                    <Button variant="outline" className="w-full">
                      <Tag className="mr-2 h-4 w-4" />
                      Tag Model
                    </Button>
                  </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Tag Model</DialogTitle>
                  <DialogDescription>
                    Add tags to a model version
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label>Model</Label>
                    <Select value={selectedModelForTag} onValueChange={setSelectedModelForTag}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select model" />
                      </SelectTrigger>
                      <SelectContent>
                        {models.map((model) => (
                          <SelectItem key={model.id} value={model.id}>
                            {model.version}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Tags (comma-separated)</Label>
                    <Input
                      value={tags}
                      onChange={(e) => setTags(e.target.value)}
                      placeholder="e.g., production, stable, v1.0"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button onClick={handleTag}>Save Tags</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <Dialog open={isCleanupOpen} onOpenChange={setIsCleanupOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" className="w-full">
                  <Trash2 className="mr-2 h-4 w-4" />
                  Cleanup Models
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Cleanup Models</DialogTitle>
                  <DialogDescription>
                    Remove old models while preserving tagged ones
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label>Days to Keep</Label>
                    <Input
                      type="number"
                      value={daysToKeep}
                      onChange={(e) => setDaysToKeep(e.target.value)}
                      min="1"
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="keepTagged"
                      checked={keepTagged}
                      onChange={(e) => setKeepTagged(e.target.checked)}
                    />
                    <Label htmlFor="keepTagged">Keep Tagged Models</Label>
                  </div>
                </div>
                <DialogFooter>
                  <Button onClick={handleCleanup}>Cleanup</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
              </>
            )}
          </div>

          {comparison && (
            <div className="mt-4 p-4 border rounded-lg">
              <h3 className="text-lg font-semibold mb-2">Comparison Results</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium">Model 1: {comparison.model1.version}</h4>
                  <p>Accuracy: {comparison.model1.metrics?.accuracy.toFixed(2)}%</p>
                  <p>WER: {comparison.model1.metrics?.wer.toFixed(2)}%</p>
                  <p>CER: {comparison.model1.metrics?.cer.toFixed(2)}%</p>
                  <p>Confidence: {comparison.model1.metrics?.confidence.toFixed(2)}%</p>
                </div>
                <div>
                  <h4 className="font-medium">Model 2: {comparison.model2.version}</h4>
                  <p>Accuracy: {comparison.model2.metrics?.accuracy.toFixed(2)}%</p>
                  <p>WER: {comparison.model2.metrics?.wer.toFixed(2)}%</p>
                  <p>CER: {comparison.model2.metrics?.cer.toFixed(2)}%</p>
                  <p>Confidence: {comparison.model2.metrics?.confidence.toFixed(2)}%</p>
                </div>
              </div>
              <div className="mt-4">
                <h4 className="font-medium">Differences</h4>
                <p>Accuracy: {comparison.comparison.accuracy_diff.toFixed(2)}%</p>
                <p>WER: {comparison.comparison.wer_diff.toFixed(2)}%</p>
                <p>CER: {comparison.comparison.cer_diff.toFixed(2)}%</p>
                <p>Confidence: {comparison.comparison.confidence_diff.toFixed(2)}%</p>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
} 