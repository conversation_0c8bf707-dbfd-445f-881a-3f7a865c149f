# Subcollection Migration Guide

This guide explains the migration from top-level collections to user subcollections for better data organization and security.

## 🔄 **Migration Overview**

### **Before (Top-level Collections):**
```
activity_logs/
├── doc1 { userId: "user1", type: "login", ... }
├── doc2 { userId: "user2", type: "upload", ... }
└── doc3 { userId: "user1", type: "logout", ... }

security_events/
├── event1 { userId: "user1", event_type: "failed_login", ... }
└── event2 { userId: "user2", event_type: "suspicious_activity", ... }

user_sessions/
├── session1 { userId: "user1", login_time: "...", ... }
└── session2 { userId: "user2", login_time: "...", ... }
```

### **After (User Subcollections):**
```
users/
├── user1/
│   ├── activity_logs/
│   │   ├── doc1 { type: "login", ... }
│   │   └── doc3 { type: "logout", ... }
│   ├── security_events/
│   │   └── event1 { event_type: "failed_login", ... }
│   └── sessions/
│       └── session1 { login_time: "...", ... }
└── user2/
    ├── activity_logs/
    │   └── doc2 { type: "upload", ... }
    ├── security_events/
    │   └── event2 { event_type: "suspicious_activity", ... }
    └── sessions/
        └── session2 { login_time: "...", ... }
```

## 🎯 **Benefits**

1. **Better Data Organization**: Related data grouped under user
2. **Improved Security**: Automatic user-scoped access
3. **Better Performance**: Smaller subcollections, no userId filtering needed
4. **Cleaner Firestore Rules**: Simpler permission logic
5. **No Composite Indexes**: Single-field queries only

## 🚀 **Migration Steps**

### **Step 1: Run Migration Script**

```bash
# Install dependencies (if needed)
npm install firebase-admin

# Set up Firebase credentials
export GOOGLE_APPLICATION_CREDENTIALS="path/to/service-account-key.json"

# Run migration (dry run first)
node scripts/migrate-to-subcollections.js

# Run migration and delete old collections
node scripts/migrate-to-subcollections.js --delete-old
```

### **Step 2: Verify Migration**

Check Firebase Console to ensure:
- New subcollections are created under users/
- Data is correctly migrated
- Old collections are deleted (if --delete-old was used)

### **Step 3: Update Application Code**

The following files have been updated:
- `lib/activity-tracker.ts` - Uses user subcollections
- `lib/security-service.ts` - Uses user subcollections  
- `app/api/users/[userId]/security/route.ts` - Updated API endpoints
- `firestore.rules` - Updated security rules
- `firestore.indexes.json` - Removed unnecessary indexes

## 📋 **Code Changes Summary**

### **Activity Tracking**
```javascript
// Before
await addDoc(collection(db, 'activity_logs'), activity)

// After  
await addDoc(collection(db, 'users', userId, 'activity_logs'), activity)
```

### **Security Events**
```javascript
// Before
await addDoc(collection(db, 'security_events'), event)

// After
await addDoc(collection(db, 'users', userId, 'security_events'), event)
```

### **User Sessions**
```javascript
// Before
await addDoc(collection(db, 'user_sessions'), session)

// After
await addDoc(collection(db, 'users', userId, 'sessions'), session)
```

### **Queries**
```javascript
// Before
query(collection(db, 'activity_logs'), where('userId', '==', userId))

// After (no userId filter needed!)
query(collection(db, 'users', userId, 'activity_logs'))
```

## 🔒 **Security Rules**

New rules automatically scope access to user's own data:

```javascript
match /users/{userId} {
  match /activity_logs/{activityId} {
    allow read, write: if request.auth.uid == userId;
    allow read: if isAdmin(); // Admins can read all
  }
  
  match /security_events/{eventId} {
    allow read, write: if request.auth.uid == userId;
    allow read, write: if isAdmin(); // Admins can read/write all
  }
  
  match /sessions/{sessionId} {
    allow read, write: if request.auth.uid == userId;
    allow read: if isAdmin(); // Admins can read all
  }
}
```

## 📊 **Performance Improvements**

### **Before:**
- Required composite indexes for userId + timestamp
- Large collections with all users' data mixed
- Complex queries with where clauses

### **After:**
- No composite indexes needed (single-field only)
- Small subcollections per user
- Simple queries without filtering

## 🧪 **Testing**

### **Test Scenarios:**
1. **Activity Logging**: Verify activities are logged to user subcollections
2. **Security Events**: Test security event creation and retrieval
3. **Session Management**: Test session creation, updates, and cleanup
4. **Admin Access**: Verify admins can access all user subcollections
5. **User Access**: Verify users can only access their own subcollections

### **Test Commands:**
```bash
# Test activity logging
curl -X POST /api/users/USER_ID/activity \
  -H "Content-Type: application/json" \
  -d '{"type": "test", "description": "Test activity"}'

# Test security events
curl -X POST /api/users/USER_ID/security \
  -H "Content-Type: application/json" \
  -d '{"action": "create_security_event", "event_type": "test"}'

# Test session management
curl -X POST /api/users/USER_ID/security \
  -H "Content-Type: application/json" \
  -d '{"action": "create_session", "session_id": "test123"}'
```

## 🔧 **Troubleshooting**

### **Migration Issues:**
- **Missing userId**: Documents without userId will be skipped
- **Permission errors**: Ensure Firebase Admin credentials are set
- **Timeout errors**: Large collections may need batch processing

### **Application Issues:**
- **Function signature changes**: Some functions now require userId parameter
- **Query updates**: Remove userId where clauses from subcollection queries
- **Index errors**: Single-field indexes are automatic, no configuration needed

## 📝 **Rollback Plan**

If rollback is needed:
1. Keep old collections during migration (don't use --delete-old)
2. Revert code changes to use old collection structure
3. Re-deploy previous Firestore rules and indexes

## ✅ **Migration Checklist**

- [ ] Backup existing data
- [ ] Run migration script
- [ ] Verify data migration in Firebase Console
- [ ] Test application functionality
- [ ] Monitor for errors in production
- [ ] Clean up old collections (after verification)

## 🎉 **Completion**

After successful migration:
- Data is better organized under user subcollections
- Security is improved with automatic user scoping
- Performance is enhanced with smaller collections
- Maintenance is simplified with cleaner structure
