"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Plus, Upload, FileSpreadsheet, Mic } from "lucide-react"
import { useRouter } from "next/navigation"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

export function QuickActions() {
  const [isOpen, setIsOpen] = useState(false)
  const router = useRouter()

  const actions = [
    {
      icon: <Upload className="h-4 w-4" />,
      label: "Upload Audio",
      onClick: () => router.push("/dashboard/upload"),
    },
    {
      icon: <FileSpreadsheet className="h-4 w-4" />,
      label: "Bulk Upload",
      onClick: () => router.push("/dashboard/bulk-upload"),
    },
    {
      icon: <Mic className="h-4 w-4" />,
      label: "Record Audio",
      onClick: () => router.push("/dashboard/record"),
    },
  ]

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <TooltipProvider>
        <div className="flex flex-col items-end gap-2">
          {isOpen && (
            <div className="flex flex-col gap-2 mb-2">
              {actions.map((action, index) => (
                <Tooltip key={index}>
                  <TooltipTrigger asChild>
                    <Button
                      variant="secondary"
                      size="icon"
                      className="h-10 w-10 rounded-full shadow-lg"
                      onClick={action.onClick}
                    >
                      {action.icon}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="left">
                    <p>{action.label}</p>
                  </TooltipContent>
                </Tooltip>
              ))}
            </div>
          )}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="default"
                size="icon"
                className="h-12 w-12 rounded-full shadow-lg"
                onClick={() => setIsOpen(!isOpen)}
              >
                <Plus className={`h-6 w-6 transition-transform ${isOpen ? 'rotate-45' : ''}`} />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Quick Actions</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </TooltipProvider>
    </div>
  )
} 