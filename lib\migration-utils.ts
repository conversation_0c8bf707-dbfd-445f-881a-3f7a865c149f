/**
 * Migration utilities for updating Firestore timestamps
 */

export interface MigrationOptions {
  dryRun?: boolean
  batchSize?: number
  verbose?: boolean
}

export interface MigrationProgress {
  totalDocuments: number
  processedDocuments: number
  updatedDocuments: number
  errors: string[]
  startTime: Date
  estimatedTimeRemaining?: number
}

export class TimestampMigration {
  private options: MigrationOptions
  private progress: MigrationProgress

  constructor(options: MigrationOptions = {}) {
    this.options = {
      dryRun: false,
      batchSize: 50,
      verbose: false,
      ...options
    }
    
    this.progress = {
      totalDocuments: 0,
      processedDocuments: 0,
      updatedDocuments: 0,
      errors: [],
      startTime: new Date()
    }
  }

  /**
   * Run the timestamp migration
   */
  async migrate(): Promise<MigrationProgress> {
    try {
      console.log('🚀 Starting timestamp migration...')
      console.log(`📋 Options:`, this.options)

      const response = await fetch('/api/admin/migrate-timestamps', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(this.options)
      })

      if (!response.ok) {
        throw new Error(`Migration failed: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Migration failed')
      }

      this.progress = {
        ...this.progress,
        totalDocuments: result.result.totalProcessed,
        processedDocuments: result.result.totalProcessed,
        updatedDocuments: result.result.updated,
        errors: result.result.errors
      }

      console.log('✅ Migration completed successfully!')
      this.logResults(result.result)

      return this.progress

    } catch (error) {
      console.error('❌ Migration failed:', error)
      this.progress.errors.push(error instanceof Error ? error.message : 'Unknown error')
      throw error
    }
  }

  /**
   * Run a dry run to see what would be updated
   */
  async dryRun(): Promise<MigrationProgress> {
    const originalDryRun = this.options.dryRun
    this.options.dryRun = true
    
    try {
      const result = await this.migrate()
      console.log('🔍 Dry run completed - no changes were made')
      return result
    } finally {
      this.options.dryRun = originalDryRun
    }
  }

  /**
   * Get current migration progress
   */
  getProgress(): MigrationProgress {
    const elapsed = Date.now() - this.progress.startTime.getTime()
    const rate = this.progress.processedDocuments / (elapsed / 1000)
    const remaining = this.progress.totalDocuments - this.progress.processedDocuments
    
    this.progress.estimatedTimeRemaining = remaining > 0 ? remaining / rate : 0
    
    return { ...this.progress }
  }

  private logResults(result: any) {
    console.log('\n📊 Migration Results:')
    console.log(`   Total documents processed: ${result.totalProcessed}`)
    console.log(`   Documents updated: ${result.updated}`)
    console.log(`   Errors: ${result.errors.length}`)
    
    console.log('\n📋 Details:')
    console.log(`   Main documents: ${result.details.mainDocuments}`)
    console.log(`   Transcriptions: ${result.details.transcriptions}`)
    console.log(`   Metadata: ${result.details.metadata}`)
    console.log(`   Reviews: ${result.details.reviews}`)
    console.log(`   Training: ${result.details.training}`)
    console.log(`   Analytics: ${result.details.analytics}`)

    if (result.errors.length > 0) {
      console.log('\n❌ Errors:')
      result.errors.forEach((error: string, index: number) => {
        console.log(`   ${index + 1}. ${error}`)
      })
    }
  }
}

/**
 * Convenience functions for common migration tasks
 */

export async function runTimestampMigration(options?: MigrationOptions): Promise<MigrationProgress> {
  const migration = new TimestampMigration(options)
  return await migration.migrate()
}

export async function runTimestampDryRun(options?: MigrationOptions): Promise<MigrationProgress> {
  const migration = new TimestampMigration(options)
  return await migration.dryRun()
}

/**
 * Validate timestamp format
 */
export function isValidISOTimestamp(timestamp: any): boolean {
  if (!timestamp || typeof timestamp !== 'string') return false
  
  // Check if it matches ISO 8601 format
  const isoRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z$/
  if (!isoRegex.test(timestamp)) return false
  
  // Check if it's a valid date
  const date = new Date(timestamp)
  return !isNaN(date.getTime())
}

/**
 * Convert various timestamp formats to ISO
 */
export function normalizeTimestamp(timestamp: any): string {
  if (!timestamp) return new Date().toISOString()
  
  // Already ISO format
  if (isValidISOTimestamp(timestamp)) return timestamp
  
  // Firestore timestamp
  if (timestamp && typeof timestamp === 'object' && timestamp.seconds) {
    return new Date(timestamp.seconds * 1000).toISOString()
  }
  
  // Try to parse as date
  try {
    const date = new Date(timestamp)
    if (isNaN(date.getTime())) {
      return new Date().toISOString()
    }
    return date.toISOString()
  } catch {
    return new Date().toISOString()
  }
}

/**
 * Check if a document needs timestamp migration
 */
export function needsTimestampMigration(document: any): boolean {
  const timestampFields = ['created_at', 'updated_at', 'reviewed_at', 'last_trained_at']
  
  return timestampFields.some(field => {
    const value = document[field]
    return value && !isValidISOTimestamp(value)
  })
}
