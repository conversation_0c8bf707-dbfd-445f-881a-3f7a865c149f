'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { listAllAudio, updateReviewStatus, getUsernames } from "@/lib/audio-api-v2"
import { useAuth } from "@/components/auth-provider"
import { useToast } from "@/hooks/use-toast"
import { StatsSkeleton, AudioListSkeleton } from "@/components/ui/loading-skeleton"
import {
  Search,
  Filter,
  Play,
  Pause,
  CheckCircle,
  XCircle,
  Clock,
  ChevronLeft,
  ChevronRight,
  Star,
  Flag,
  BarChart3,
  FileAudio,
  Users,
  TrendingUp
} from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectItem,
  Select<PERSON><PERSON><PERSON>,
  Select<PERSON><PERSON>ue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"

interface ReviewSubmission {
  id: string
  title: string
  audio_url: string
  duration: number
  format: string
  created_at: string
  user_id: string
  username: string
  source: string

  // Hierarchical data (flattened for UI)
  transcription_content?: string
  gender?: string
  language?: string
  action?: string
  approved?: number
  trained_asr?: boolean
  tts_trained?: boolean
  quality_score?: number
  review_notes?: string
  reviewed_at?: string
  reviewed_by?: string

  // Subcollections (loaded on demand)
  transcriptions?: {
    primary?: {
      content: string
      language: string
      transcription_source: string
      created_at: string
      type: string
      speaker_count: number
    }
  }
  metadata?: {
    details?: {
      gender: string
      language: string
      recording_context?: string
    }
  }
  review?: {
    status?: {
      action: 'pending' | 'approved' | 'rejected'
      reviewed_by?: string
      reviewed_at?: string
      feedback?: string
      is_flagged: boolean
    }
  }
  training?: {
    status?: {
      trained_asr: boolean
      tts_trained: boolean
      training_sessions: string[]
      last_trained_at?: string
    }
  }
}

export default function ReviewSubmissionsPage() {
  const { user } = useAuth()
  const { toast } = useToast()

  const [submissions, setSubmissions] = useState<ReviewSubmission[]>([])
  const [filteredSubmissions, setFilteredSubmissions] = useState<ReviewSubmission[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("pending")
  const [sourceFilter, setSourceFilter] = useState("all")
  const [selectedSubmission, setSelectedSubmission] = useState<ReviewSubmission | null>(null)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [playingAudio, setPlayingAudio] = useState<string | null>(null)
  const [reviewNotes, setReviewNotes] = useState("")
  const [qualityScore, setQualityScore] = useState([3])
  const [sortBy, setSortBy] = useState("created_at")
  const [sortOrder, setSortOrder] = useState("desc")
  const [userFilter, setUserFilter] = useState("")

  // Statistics
  const totalSubmissions = submissions.length
  const pendingSubmissions = submissions.filter(s => s.action === 'pending').length
  const approvedSubmissions = submissions.filter(s => s.action === 'approved').length
  const rejectedSubmissions = submissions.filter(s => s.action === 'rejected').length

  useEffect(() => {
    if (user) {
      loadSubmissions()
    }
  }, [user])

  useEffect(() => {
    filterAndSortSubmissions()
  }, [submissions, searchTerm, statusFilter, sourceFilter, userFilter, sortBy, sortOrder])

  // Ensure currentIndex stays within bounds when filteredSubmissions change
  useEffect(() => {
    if (filteredSubmissions.length > 0 && currentIndex >= filteredSubmissions.length) {
      setCurrentIndex(Math.max(0, filteredSubmissions.length - 1))
    } else if (filteredSubmissions.length === 0) {
      setCurrentIndex(0)
    }
  }, [filteredSubmissions.length, currentIndex])

  useEffect(() => {
    if (filteredSubmissions.length > 0) {
      setSelectedSubmission(filteredSubmissions[currentIndex] || filteredSubmissions[0])
      setCurrentIndex(Math.min(currentIndex, filteredSubmissions.length - 1))
    }
  }, [filteredSubmissions, currentIndex])

  const loadSubmissions = async () => {
    try {
      setLoading(true)
      console.log('Loading submissions...')

      const result = await listAllAudio({
        limit: 100,
        page: 1,
        sortBy: 'created_at',
        sortOrder: 'desc'
      })

      console.log(`Loaded ${result.audio_records.length} submissions`)

      // Get unique user IDs and fetch usernames
      const userIds = [...new Set(result.audio_records.map(item => item.user_id))]
      const usernames = await getUsernames(userIds)

      // Transform hierarchical data to flat structure for UI compatibility
      const transformedData: ReviewSubmission[] = result.audio_records.map(item => ({
        ...item,
        username: usernames[item.user_id] || item.user_id,
        // Flatten transcription data
        transcription_content: item.transcriptions?.primary?.content || '',
        // Flatten metadata
        gender: item.metadata?.details?.gender || '',
        language: item.metadata?.details?.language || 'masalit',
        // Flatten review status
        action: item.review?.status?.action || 'pending',
        approved: item.review?.status?.action === 'approved' ? 1 : 0,
        reviewed_at: item.review?.status?.reviewed_at || '',
        reviewed_by: item.review?.status?.reviewed_by || '',
        review_notes: item.review?.status?.feedback || '',
        // Flatten training status
        trained_asr: item.training?.status?.trained_asr || false,
        tts_trained: item.training?.status?.tts_trained || false,
        quality_score: 3,
      }))

      setSubmissions(transformedData)
      console.log('Submissions loaded and transformed successfully')
    } catch (error) {
      console.error('Error loading submissions:', error)
      toast({
        title: "Error",
        description: "Failed to load submissions",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const filterAndSortSubmissions = () => {
    let filtered = submissions

    if (searchTerm) {
      filtered = filtered.filter(submission =>
        submission.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        submission.transcription_content?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        submission.username.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (statusFilter !== "all") {
      filtered = filtered.filter(submission => submission.action === statusFilter)
    }

    if (sourceFilter !== "all") {
      filtered = filtered.filter(submission => submission.source === sourceFilter)
    }

    if (userFilter) {
      filtered = filtered.filter(submission =>
        submission.username.toLowerCase().includes(userFilter.toLowerCase())
      )
    }

    // Sort submissions
    filtered.sort((a, b) => {
      let aValue: any = a[sortBy as keyof ReviewSubmission]
      let bValue: any = b[sortBy as keyof ReviewSubmission]

      // Handle date sorting
      if (sortBy === 'created_at' || sortBy === 'reviewed_at') {
        aValue = new Date(aValue || 0).getTime()
        bValue = new Date(bValue || 0).getTime()
      }

      // Handle string sorting
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    setFilteredSubmissions(filtered)
  }

  const handleApprove = async (submission?: ReviewSubmission) => {
    const target = submission || selectedSubmission
    if (!target) return

    try {
      await updateReviewStatus(target.id, {
        action: 'approved',
        reviewed_by: user?.id,
        reviewed_at: new Date().toISOString(),
        feedback: reviewNotes,
        is_flagged: false
      })

      // Update the submission status in state
      setSubmissions(prev => prev.map(s =>
        s.id === target.id
          ? {
              ...s,
              action: 'approved',
              approved: 1,
              review_notes: reviewNotes,
              reviewed_at: new Date().toISOString(),
              reviewed_by: user?.id
            }
          : s
      ))

      toast({
        title: "Approved",
        description: `"${target.title}" has been approved`,
      })

      // Reset form
      setReviewNotes("")
      setQualityScore([3])

      // Handle navigation after status change
      handlePostReviewNavigation('approved')
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to approve submission",
        variant: "destructive",
      })
    }
  }

  const handleReject = async (submission?: ReviewSubmission) => {
    const target = submission || selectedSubmission
    if (!target) return

    try {
      await updateReviewStatus(target.id, {
        action: 'rejected',
        reviewed_by: user?.id,
        reviewed_at: new Date().toISOString(),
        feedback: reviewNotes,
        is_flagged: false
      })

      // Update the submission status in state
      setSubmissions(prev => prev.map(s =>
        s.id === target.id
          ? {
              ...s,
              action: 'rejected',
              approved: 0,
              review_notes: reviewNotes,
              reviewed_at: new Date().toISOString(),
              reviewed_by: user?.id
            }
          : s
      ))

      toast({
        title: "Rejected",
        description: `"${target.title}" has been rejected`,
      })

      // Reset form
      setReviewNotes("")
      setQualityScore([3])

      // Handle navigation after status change
      handlePostReviewNavigation('rejected')
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to reject submission",
        variant: "destructive",
      })
    }
  }

  const handlePostReviewNavigation = (newStatus: 'approved' | 'rejected') => {
    // Wait for the next tick to allow state updates to propagate
    setTimeout(() => {
      // Check if the current submission still matches the filter after status change
      const currentSubmission = selectedSubmission
      if (!currentSubmission) return

      const updatedSubmission = { ...currentSubmission, action: newStatus }
      const wouldMatchFilter = checkSubmissionMatchesFilter(updatedSubmission)

      if (!wouldMatchFilter) {
        // Current submission no longer matches filter, move to next valid submission
        const remainingSubmissions = filteredSubmissions.filter((_, index) => index !== currentIndex)

        if (remainingSubmissions.length > 0) {
          // If there are submissions after current index, stay at same index (which will show next item)
          // If current was last item, move to previous index
          const newIndex = currentIndex >= remainingSubmissions.length ? Math.max(0, currentIndex - 1) : currentIndex
          setCurrentIndex(newIndex)
        } else {
          // No more submissions match the filter
          setCurrentIndex(0)
        }
      }
    }, 100)
  }

  const checkSubmissionMatchesFilter = (submission: ReviewSubmission): boolean => {
    // Check if submission matches current filters
    if (statusFilter !== "all" && submission.action !== statusFilter) return false
    if (sourceFilter !== "all" && submission.source !== sourceFilter) return false
    if (userFilter && !submission.username.toLowerCase().includes(userFilter.toLowerCase())) return false
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase()
      const matchesSearch =
        submission.title.toLowerCase().includes(searchLower) ||
        submission.transcription_content?.toLowerCase().includes(searchLower) ||
        submission.username.toLowerCase().includes(searchLower)
      if (!matchesSearch) return false
    }
    return true
  }

  const handleNext = () => {
    if (currentIndex < filteredSubmissions.length - 1) {
      setCurrentIndex(currentIndex + 1)
      setReviewNotes("")
      setQualityScore([3])
      setPlayingAudio(null)
    }
  }

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1)
      setReviewNotes("")
      setQualityScore([3])
      setPlayingAudio(null)
    }
  }

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A'

    const date = new Date(dateString)
    if (isNaN(date.getTime())) return 'Invalid Date'

    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const toggleAudio = (audioUrl: string) => {
    if (playingAudio === audioUrl) {
      setPlayingAudio(null)
    } else {
      setPlayingAudio(audioUrl)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold">Review Submissions</h1>
          <p className="text-muted-foreground">
            Review and approve audio submissions from contributors
          </p>
        </div>

        {/* Statistics Cards Skeleton */}
        <StatsSkeleton />

        {/* Filters Skeleton */}
        <div className="h-32 bg-muted/20 rounded-lg animate-pulse" />

        {/* Content Skeleton */}
        <AudioListSkeleton count={3} />
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Review Submissions</h1>
          <p className="text-muted-foreground">
            Review and approve audio submissions from contributors
          </p>
        </div>

      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Submissions</CardTitle>
            <FileAudio className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalSubmissions}</div>
            <p className="text-xs text-muted-foreground">
              All submissions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{pendingSubmissions}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting review
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{approvedSubmissions}</div>
            <p className="text-xs text-muted-foreground">
              Ready for training
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rejected</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{rejectedSubmissions}</div>
            <p className="text-xs text-muted-foreground">
              Need revision
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Search and Filter</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search submissions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            <div className="flex-1">
              <Input
                placeholder="Filter by username..."
                value={userFilter}
                onChange={(e) => setUserFilter(e.target.value)}
              />
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>

            <Select value={sourceFilter} onValueChange={setSourceFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by source" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Sources</SelectItem>
                <SelectItem value="direct_upload">Direct Upload</SelectItem>
                <SelectItem value="bulk_upload">Bulk Upload</SelectItem>
                <SelectItem value="recording">Recording</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex flex-col sm:flex-row gap-4">
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created_at">Date Created</SelectItem>
                <SelectItem value="reviewed_at">Date Reviewed</SelectItem>
                <SelectItem value="username">Username</SelectItem>
                <SelectItem value="title">Title</SelectItem>
                <SelectItem value="duration">Duration</SelectItem>
                <SelectItem value="action">Status</SelectItem>
              </SelectContent>
            </Select>

            <Select value={sortOrder} onValueChange={setSortOrder}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Order" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="desc">Newest First</SelectItem>
                <SelectItem value="asc">Oldest First</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Review Interface */}
      {selectedSubmission && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Submission Details */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Review Submission</CardTitle>
                <div className="flex items-center space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handlePrevious}
                    disabled={currentIndex === 0}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <span className="text-sm text-muted-foreground">
                    {currentIndex + 1} of {filteredSubmissions.length}
                  </span>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleNext}
                    disabled={currentIndex === filteredSubmissions.length - 1}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-medium text-lg">{selectedSubmission.title}</h3>
                <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                  <span>By {selectedSubmission.username}</span>
                  <span>{formatDuration(selectedSubmission.duration)}</span>
                  <span>{formatDate(selectedSubmission.created_at)}</span>
                  <Badge variant={
                    selectedSubmission.action === 'approved' ? 'default' :
                    selectedSubmission.action === 'rejected' ? 'destructive' :
                    'secondary'
                  }>
                    {selectedSubmission.action === 'approved' ? '✓ Approved' :
                     selectedSubmission.action === 'rejected' ? '✗ Rejected' :
                     '⏳ Pending'}
                  </Badge>
                </div>
              </div>

              {/* Audio Player */}
              <div className="space-y-2">
                <Button
                  onClick={() => toggleAudio(selectedSubmission.audio_url)}
                  className="w-full"
                >
                  {playingAudio === selectedSubmission.audio_url ? (
                    <>
                      <Pause className="h-4 w-4 mr-2" />
                      Pause Audio
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4 mr-2" />
                      Play Audio
                    </>
                  )}
                </Button>

                {playingAudio === selectedSubmission.audio_url && (
                  <audio
                    src={selectedSubmission.audio_url}
                    controls
                    autoPlay
                    onEnded={() => setPlayingAudio(null)}
                    className="w-full"
                  />
                )}
              </div>

              {/* Transcription */}
              {selectedSubmission.transcription_content && (
                <div>
                  <label className="text-sm font-medium">Transcription</label>
                  <div className="mt-1 p-3 bg-muted rounded text-sm">
                    {selectedSubmission.transcription_content}
                  </div>
                </div>
              )}

              {/* Metadata */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Gender:</span> {selectedSubmission.gender}
                </div>
                <div>
                  <span className="font-medium">Language:</span> {selectedSubmission.language}
                </div>
                <div>
                  <span className="font-medium">Source:</span> {selectedSubmission.source}
                </div>
                <div>
                  <span className="font-medium">Format:</span> {selectedSubmission.format}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Review Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Review Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Quality Score */}
              <div>
                <label className="text-sm font-medium">Quality Score</label>
                <div className="mt-2">
                  <Slider
                    value={qualityScore}
                    onValueChange={setQualityScore}
                    max={5}
                    min={1}
                    step={1}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>Poor</span>
                    <span>Excellent</span>
                  </div>
                  <div className="text-center mt-1">
                    <span className="text-sm font-medium">{qualityScore[0]}/5</span>
                  </div>
                </div>
              </div>

              {/* Review Notes */}
              <div>
                <label className="text-sm font-medium">Review Notes</label>
                <Textarea
                  value={reviewNotes}
                  onChange={(e) => setReviewNotes(e.target.value)}
                  placeholder="Add notes about this submission..."
                  rows={4}
                  className="mt-1"
                />
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-2">
                <Button
                  onClick={() => handleApprove()}
                  className="flex-1 bg-green-600 hover:bg-green-700"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Approve
                </Button>
                <Button
                  onClick={() => handleReject()}
                  variant="destructive"
                  className="flex-1"
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  Reject
                </Button>
              </div>

              {/* Quick Actions */}
              <div className="pt-4 border-t">
                <p className="text-sm font-medium mb-2">Quick Actions</p>
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      setReviewNotes("Good quality recording")
                      setQualityScore([4])
                    }}
                  >
                    Good Quality
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      setReviewNotes("Excellent recording")
                      setQualityScore([5])
                    }}
                  >
                    Excellent
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      setReviewNotes("Poor audio quality")
                      setQualityScore([2])
                    }}
                  >
                    Poor Quality
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      setReviewNotes("Transcription mismatch")
                      setQualityScore([1])
                    }}
                  >
                    Mismatch
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {filteredSubmissions.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <div className="text-muted-foreground">
              No submissions found matching your criteria.
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
