"use client"

import React, { useState, useEffect } from 'react'
import { auth, db } from '@/lib/firebase'
import { onAuthStateChanged } from 'firebase/auth'

interface FirebaseStatusProps {
  showInDevelopment?: boolean
}

export function FirebaseStatus({ showInDevelopment = true }: FirebaseStatusProps) {
  const [status, setStatus] = useState<{
    auth: 'connected' | 'disconnected' | 'unknown'
    firestore: 'connected' | 'disconnected' | 'unknown'
    message?: string
  }>({
    auth: 'unknown',
    firestore: 'unknown'
  })

  useEffect(() => {
    // Only show in development if enabled
    if (process.env.NODE_ENV !== 'development' && showInDevelopment) {
      return
    }

    let authUnsubscribe: (() => void) | null = null

    const checkFirebaseStatus = async () => {
      try {
        // Check Auth connectivity with real-time listener
        authUnsubscribe = onAuthStateChanged(auth, (user) => {
          const authStatus = user ? 'connected' : 'disconnected'
          setStatus(prev => ({
            ...prev,
            auth: authStatus
          }))
        })

        // Check Firestore initialization (simple check)
        try {
          // Just check if db is initialized
          const firestoreStatus = db ? 'connected' : 'disconnected'

          setStatus(prev => ({
            ...prev,
            firestore: firestoreStatus,
            message: process.env.NODE_ENV === 'development'
              ? 'Firebase status in development mode'
              : undefined
          }))
        } catch (firestoreError: any) {
          console.warn('Firestore initialization check failed:', firestoreError)
          setStatus(prev => ({
            ...prev,
            firestore: 'disconnected',
            message: `Firestore: ${firestoreError.message || 'initialization failed'}`
          }))
        }
      } catch (error) {
        console.warn('Firebase status check failed:', error)
        setStatus({
          auth: 'disconnected',
          firestore: 'disconnected',
          message: 'Firebase connection failed'
        })
      }
    }

    checkFirebaseStatus()

    // Cleanup function
    return () => {
      if (authUnsubscribe) {
        authUnsubscribe()
      }
    }
  }, [showInDevelopment])

  // Don't render in production unless explicitly requested
  if (process.env.NODE_ENV === 'production' && showInDevelopment) {
    return null
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'text-green-600'
      case 'disconnected': return 'text-red-600'
      default: return 'text-yellow-600'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected': return '🟢'
      case 'disconnected': return '🔴'
      default: return '🟡'
    }
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-3 text-xs z-50">
      <div className="font-semibold text-gray-700 mb-1">Firebase Status</div>
      <div className="space-y-1">
        <div className={`flex items-center gap-2 ${getStatusColor(status.auth)}`}>
          <span>{getStatusIcon(status.auth)}</span>
          <span>Auth: {status.auth}</span>
        </div>
        <div className={`flex items-center gap-2 ${getStatusColor(status.firestore)}`}>
          <span>{getStatusIcon(status.firestore)}</span>
          <span>Firestore: {status.firestore}</span>
        </div>
        {status.message && (
          <div className="text-gray-500 text-xs mt-1">
            {status.message}
          </div>
        )}
      </div>
    </div>
  )
}

export default FirebaseStatus
