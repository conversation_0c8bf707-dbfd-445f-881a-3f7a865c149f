import { GetServerSidePropsContext } from 'next'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { doc, getDoc } from 'firebase/firestore'
import { db } from '@/lib/firebase'

export interface AuthenticatedUser {
  id: string
  email: string
  name: string
  role: 'admin' | 'user'
  isDisabled: boolean
  email_verified: boolean
}

export interface AuthResult {
  user: AuthenticatedUser | null
  redirect?: {
    destination: string
    permanent: boolean
  }
}

/**
 * Server-side authentication check for pages
 * Returns user data if authenticated, or redirect if not
 */
export async function requireAuth(
  context: GetServerSidePropsContext,
  options: {
    adminOnly?: boolean
    redirectTo?: string
  } = {}
): Promise<AuthResult> {
  try {
    // Get session from NextAuth
    const session = await getServerSession(context.req, context.res, authOptions)
    
    if (!session || !session.user?.id) {
      return {
        user: null,
        redirect: {
          destination: `/auth/signin?callbackUrl=${encodeURIComponent(context.resolvedUrl)}`,
          permanent: false
        }
      }
    }

    // Get user data from Firestore
    const userDoc = await getDoc(doc(db, 'users', session.user.id))
    
    if (!userDoc.exists()) {
      console.error('User document not found:', session.user.id)
      return {
        user: null,
        redirect: {
          destination: '/auth/signin?error=user-not-found',
          permanent: false
        }
      }
    }

    const userData = userDoc.data()
    
    // Check if user is disabled
    if (userData.isDisabled) {
      return {
        user: null,
        redirect: {
          destination: '/auth/signin?error=account-disabled',
          permanent: false
        }
      }
    }

    // Check admin requirement
    if (options.adminOnly && userData.role !== 'admin') {
      return {
        user: null,
        redirect: {
          destination: options.redirectTo || '/dashboard?error=admin-required',
          permanent: false
        }
      }
    }

    const authenticatedUser: AuthenticatedUser = {
      id: session.user.id,
      email: userData.email,
      name: userData.name,
      role: userData.role || 'user',
      isDisabled: userData.isDisabled || false,
      email_verified: userData.email_verified || false
    }

    return {
      user: authenticatedUser
    }

  } catch (error) {
    console.error('Server-side auth error:', error)
    return {
      user: null,
      redirect: {
        destination: '/auth/signin?error=auth-error',
        permanent: false
      }
    }
  }
}

/**
 * Convenience function for admin-only pages
 */
export async function requireAdmin(context: GetServerSidePropsContext): Promise<AuthResult> {
  return requireAuth(context, { adminOnly: true })
}

/**
 * Check if user has admin role (for API routes)
 */
export async function isAdmin(userId: string): Promise<boolean> {
  try {
    const userDoc = await getDoc(doc(db, 'users', userId))
    if (!userDoc.exists()) {
      return false
    }
    
    const userData = userDoc.data()
    return userData.role === 'admin' && !userData.isDisabled
  } catch (error) {
    console.error('Error checking admin status:', error)
    return false
  }
}

/**
 * Get user data by ID (for API routes)
 */
export async function getUserById(userId: string): Promise<AuthenticatedUser | null> {
  try {
    const userDoc = await getDoc(doc(db, 'users', userId))
    if (!userDoc.exists()) {
      return null
    }
    
    const userData = userDoc.data()
    
    return {
      id: userId,
      email: userData.email,
      name: userData.name,
      role: userData.role || 'user',
      isDisabled: userData.isDisabled || false,
      email_verified: userData.email_verified || false
    }
  } catch (error) {
    console.error('Error getting user by ID:', error)
    return null
  }
}
