"""
Firebase/Firestore service integration
"""

import json
import logging
from typing import Dict, List, Optional, Any
from google.cloud.firestore_v1.base_query import FieldFilter
from datetime import datetime
import firebase_admin
from firebase_admin import credentials, firestore, storage
try:
    from backend.config.settings import settings
except ImportError:
    from config.settings import settings

logger = logging.getLogger(__name__)

class FirebaseService:
    """Firebase service for Firestore and Storage operations"""
    
    def __init__(self):
        self.db = None
        self.bucket = None
        self._initialized = False
    
    def initialize(self):
        """Initialize Firebase Admin SDK"""
        if self._initialized:
            return
        
        try:
            # Initialize Firebase Admin SDK
            cred = credentials.Certificate(settings.firebase_credentials)
            firebase_admin.initialize_app(cred, {
                'storageBucket': settings.FIREBASE_STORAGE_BUCKET
            })
            
            # Initialize Firestore client
            self.db = firestore.client()
            
            # Initialize Storage client
            self.bucket = storage.bucket()
            
            self._initialized = True
            logger.info("Firebase initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Firebase: {e}")
            raise

    def _convert_firestore_timestamps(self, obj):
        """Recursively convert Firestore DatetimeWithNanoseconds to ISO strings"""
        if hasattr(obj, 'timestamp'):  # Firestore DatetimeWithNanoseconds
            return obj.isoformat()
        elif isinstance(obj, dict):
            return {k: self._convert_firestore_timestamps(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_firestore_timestamps(item) for item in obj]
        else:
            return obj

    def get_training_settings(self) -> Dict[str, Any]:
        """Get training settings from Firestore"""
        try:
            doc_ref = self.db.collection('training_settings').document('asr')
            doc = doc_ref.get()

            if doc.exists:
                data = doc.to_dict()
                # Convert Firestore timestamps to ISO strings for JSON compatibility
                return self._convert_firestore_timestamps(data)
            else:
                # Return default settings if none exist
                default_settings = {
                    'epochs': settings.DEFAULT_EPOCHS,
                    'learning_rate': settings.DEFAULT_LEARNING_RATE,
                    'number_of_samples': 100,
                    'model_name': 'small',
                    'validation_split': settings.DEFAULT_VALIDATION_SPLIT,
                    'early_stopping_patience': 3,
                    'use_augmentation': False,
                    'eval_steps': 100,
                    'updated_at': datetime.now().isoformat()
                }
                # Save default settings
                doc_ref.set(default_settings)
                return default_settings

        except Exception as e:
            logger.error(f"Error getting training settings: {e}")
            raise
    
    def update_training_status(self, status_data: Dict[str, Any]):
        """Update training status in Firestore"""
        try:
            doc_ref = self.db.collection('training_status').document('asr')

            # Convert any Firestore timestamps to ISO strings before saving
            clean_status_data = self._convert_firestore_timestamps(status_data.copy())
            clean_status_data['updated_at'] = datetime.now().isoformat()

            doc_ref.set(clean_status_data, merge=True)
            logger.info(f"Training status updated: {clean_status_data.get('status', 'unknown')}")

        except Exception as e:
            logger.error(f"Error updating training status: {e}")
            raise
    
    def get_training_data(self) -> List[Dict[str, Any]]:
        """Get approved audio and transcription data for training"""
        try:
            # Get approved audio files that haven't been trained yet
            audio_query = self.db.collection('audio').where(
                filter=FieldFilter('action', '==', 'approved')
            ).where(
                filter=FieldFilter('trained_asr', '==', False)
            )
            audio_docs = list(audio_query.stream())

            logger.info(f"Found {len(audio_docs)} approved, untrained audio samples")

            training_data = []

            for audio_doc in audio_docs:
                audio_data = audio_doc.to_dict()
                audio_id = audio_data.get('id')

                if not audio_id:
                    logger.warning("Audio document missing 'id' field")
                    continue

                # Get corresponding transcription (any transcription for this audio_id)
                transcription_query = self.db.collection('transcription').where(
                    filter=FieldFilter('audio_id', '==', audio_id)
                )
                transcription_docs = list(transcription_query.stream())

                if transcription_docs:
                    transcription_data = transcription_docs[0].to_dict()
                    transcription_content = transcription_data.get('content')

                    if not transcription_content or transcription_content.strip() == "":
                        logger.warning(f"Empty transcription for audio {audio_id}")
                        continue

                    training_item = {
                        'audio_id': audio_id,
                        'audio_url': audio_data.get('audio_url'),
                        'transcription': transcription_content.strip(),
                        'language': transcription_data.get('language', 'masalit'),
                        'dialect': transcription_data.get('dialect'),
                        'duration': audio_data.get('duration', 0),
                        'gender': audio_data.get('gender'),
                        'format': audio_data.get('format', 'audio/wav')
                    }

                    # Validate required fields
                    if training_item['audio_url'] and training_item['transcription']:
                        training_data.append(training_item)
                        logger.debug(f"Added training sample: {audio_id}")
                    else:
                        logger.warning(f"Skipping {audio_id}: missing audio_url or transcription")
                else:
                    logger.warning(f"No transcription found for audio {audio_id}")

            logger.info(f"Retrieved {len(training_data)} valid training samples")
            return training_data

        except Exception as e:
            logger.error(f"Error getting training data: {e}")
            raise
    
    def mark_data_as_trained(self, audio_ids: List[str]):
        """Mark audio data as trained"""
        try:
            batch = self.db.batch()

            for audio_id in audio_ids:
                # Update audio document only (transcriptions don't need to be marked)
                audio_ref = self.db.collection('audio').document(audio_id)
                batch.update(audio_ref, {
                    'trained_asr': True,
                    'last_trained_at': datetime.now().isoformat(),
                    'updated_at': datetime.now().isoformat()
                })

            batch.commit()
            logger.info(f"Marked {len(audio_ids)} audio samples as trained")

        except Exception as e:
            logger.error(f"Error marking data as trained: {e}")
            raise
    
    def save_training_metrics(self, metrics: Dict[str, Any]):
        """Save training metrics to Firestore"""
        try:
            doc_ref = self.db.collection('training_metrics').document()

            # Convert any Firestore timestamps to ISO strings before saving
            clean_metrics = self._convert_firestore_timestamps(metrics.copy())
            clean_metrics['timestamp'] = datetime.now().isoformat()

            doc_ref.set(clean_metrics)
            logger.info("Training metrics saved")

        except Exception as e:
            logger.error(f"Error saving training metrics: {e}")
            raise

    def save_training_history(self, model_type: str, training_data: Dict[str, Any]):
        """Save training history record to Firestore"""
        try:
            # Convert any Firestore timestamps to ISO strings before saving
            clean_data = self._convert_firestore_timestamps(training_data.copy())
            clean_data['model_type'] = model_type
            clean_data['timestamp'] = datetime.now().isoformat()

            # Create a unique document ID based on model version or timestamp
            doc_id = clean_data.get('model_version', f"{model_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}")

            doc_ref = self.db.collection('training_history').document(doc_id)
            doc_ref.set(clean_data)

            logger.info(f"Training history saved for {model_type}: {doc_id}")
            return doc_id

        except Exception as e:
            logger.error(f"Error saving training history: {e}")
            raise

    def save_model_record(self, model_data: Dict[str, Any]):
        """Save or update model record in the models collection"""
        try:
            # Convert any Firestore timestamps to ISO strings before saving
            clean_data = self._convert_firestore_timestamps(model_data.copy())
            clean_data['updated_at'] = datetime.now().isoformat()

            # Use model_id as document ID, or generate one
            model_id = clean_data.get('model_id') or f"{clean_data['model_type']}_{clean_data['model_version']}"

            doc_ref = self.db.collection('models').document(model_id)
            doc_ref.set(clean_data, merge=True)

            logger.info(f"Model record saved: {model_id}")
            return model_id

        except Exception as e:
            logger.error(f"Error saving model record: {e}")
            raise

    def get_model_info(self, model_type: str):
        """Get current active model information from models collection"""
        try:
            # Get all models of this type and filter in Python to avoid index requirements
            models_query = (self.db.collection('models')
                          .where('model_type', '==', model_type))

            all_models = models_query.get()

            # Filter for active models and sort by last_trained
            active_models = []
            for doc in all_models:
                model_data = doc.to_dict()
                if model_data.get('status') == 'active':
                    model_data['doc_id'] = doc.id
                    active_models.append(model_data)

            # Sort by last_trained (most recent first)
            active_models.sort(key=lambda x: x.get('last_trained', ''), reverse=True)
            models = active_models[:1]  # Take the most recent

            if models:
                model_data = models[0]
                return {
                    "model_id": model_data.get('doc_id'),
                    "version": model_data.get('model_version', 'Unknown'),
                    "last_trained": model_data.get('last_trained', 'Never'),
                    "first_trained": model_data.get('first_trained', 'Unknown'),
                    "confidence": model_data.get('confidence', 0.0),
                    "accuracy": model_data.get('accuracy', 0.0),
                    "wer": model_data.get('wer', 0.0),
                    "cer": model_data.get('cer', 0.0),
                    "ser": model_data.get('ser', 0.0),
                    "samples_trained": model_data.get('samples_trained', 0),
                    "total_training_sessions": model_data.get('total_training_sessions', 0),
                    "status": model_data.get('status', 'inactive'),
                    "base_model": model_data.get('base_model', 'Unknown'),
                    "model_path": model_data.get('model_path', ''),
                    "gcs_url": model_data.get('gcs_url', '')
                }
            else:
                return {
                    "model_id": None,
                    "version": "No model",
                    "last_trained": "Never",
                    "first_trained": "Never",
                    "confidence": 0.0,
                    "accuracy": 0.0,
                    "wer": 0.0,
                    "cer": 0.0,
                    "ser": 0.0,
                    "samples_trained": 0,
                    "total_training_sessions": 0,
                    "status": "inactive",
                    "base_model": "None",
                    "model_path": "",
                    "gcs_url": ""
                }

        except Exception as e:
            logger.error(f"Error getting model info for {model_type}: {e}")
            return {
                "model_id": None,
                "version": "Error",
                "last_trained": "Unknown",
                "first_trained": "Unknown",
                "confidence": 0.0,
                "accuracy": 0.0,
                "wer": 0.0,
                "cer": 0.0,
                "ser": 0.0,
                "samples_trained": 0,
                "total_training_sessions": 0,
                "status": "error",
                "base_model": "Error",
                "model_path": "",
                "gcs_url": ""
            }

    def list_models(self, model_type: str = None, status: str = None, limit: int = 50):
        """List models with optional filtering"""
        try:
            # Start with basic query to avoid index requirements
            if model_type:
                query = self.db.collection('models').where('model_type', '==', model_type)
            else:
                query = self.db.collection('models')

            all_docs = query.get()

            # Filter and sort in Python to avoid index requirements
            models = []
            for doc in all_docs:
                model_data = doc.to_dict()
                model_data['model_id'] = doc.id

                # Apply status filter if specified
                if status and model_data.get('status') != status:
                    continue

                models.append(model_data)

            # Sort by last_trained (most recent first)
            models.sort(key=lambda x: x.get('last_trained', ''), reverse=True)

            # Apply limit
            return models[:limit]

        except Exception as e:
            logger.error(f"Error listing models: {e}")
            return []

# Global Firebase service instance
firebase_service = FirebaseService()
