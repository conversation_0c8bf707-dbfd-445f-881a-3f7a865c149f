"use client"

import { usePathname, useRouter } from "next/navigation"
import { useAuth } from "@/components/auth-provider"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import { Home, Upload, History, CheckSquare, User, LogOut, Mic, Moon, Sun, FileText, Video, Settings, BookOpen, Database, FileSpreadsheet } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useTheme } from "next-themes"
import Link from "next/link"

export function DashboardSidebar() {
  const { user, logout } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const { theme, setTheme } = useTheme()

  const handleLogout = () => {
    logout()
    router.push("/")
  }

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark")
  }

  const isActive = (path: string) => pathname === path

  return (
    <Sidebar>
      <SidebarHeader className="border-b border-border">
        <div className="flex items-center gap-2 px-4 py-3">
          <Mic className="h-6 w-6 text-primary" />
          <div>
            <h2 className="text-lg font-semibold">Masalit Platform</h2>
            <p className="text-xs text-muted-foreground">
              {user?.role === "admin" ? "Admin Dashboard" : "Contributor Dashboard"}
            </p>
          </div>
          <SidebarTrigger className="ml-auto md:hidden" />
        </div>
      </SidebarHeader>
      <SidebarContent>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton isActive={isActive("/dashboard")} onClick={() => router.push("/dashboard")}>
              <Home className="h-5 w-5" />
              <span>Overview</span>
            </SidebarMenuButton>
          </SidebarMenuItem>

          <div className="px-4 py-2">
            <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Contributions</h3>
          </div>

          <SidebarMenuItem>
            <SidebarMenuButton
              isActive={isActive("/dashboard/upload")}
              onClick={() => router.push("/dashboard/upload")}
            >
              <Upload className="h-5 w-5" />
              <span>Upload Audio</span>
            </SidebarMenuButton>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <SidebarMenuButton
              isActive={isActive("/dashboard/bulk-upload")}
              onClick={() => router.push("/dashboard/bulk-upload")}
            >
              <FileSpreadsheet className="h-5 w-5" />
              <span>Bulk Upload</span>
            </SidebarMenuButton>
          </SidebarMenuItem>

          <SidebarMenuItem>
            <SidebarMenuButton
              isActive={isActive("/dashboard/history")}
              onClick={() => router.push("/dashboard/history")}
            >
              <History className="h-5 w-5" />
              <span>My Recordings</span>
            </SidebarMenuButton>
          </SidebarMenuItem>

          {user?.role === "admin" && (
            <>
              <div className="px-4 py-2">
                <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Administration</h3>
              </div>

              <SidebarMenuItem>
                <SidebarMenuButton
                  isActive={isActive("/dashboard/review")}
                  onClick={() => router.push("/dashboard/review")}
                >
                  <CheckSquare className="h-5 w-5" />
                  <span>Review Submissions</span>
                </SidebarMenuButton>
              </SidebarMenuItem>

              <SidebarMenuItem>
                <SidebarMenuButton
                  isActive={isActive("/dashboard/managed")}
                  onClick={() => router.push("/dashboard/managed")}
                >
                  <Database className="h-5 w-5" />
                  <span>Managed Recordings</span>
                </SidebarMenuButton>
              </SidebarMenuItem>

              <SidebarMenuItem>
                <SidebarMenuButton
                  isActive={isActive("/dashboard/admin")}
                  onClick={() => router.push("/dashboard/admin")}
                >
                  <Settings className="h-5 w-5" />
                  <span>User Management</span>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </>
          )}

          <div className="px-4 py-2">
            <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Account</h3>
          </div>

          <SidebarMenuItem>
            <SidebarMenuButton
              isActive={isActive("/dashboard/profile")}
              onClick={() => router.push("/dashboard/profile")}
            >
              <User className="h-5 w-5" />
              <span>My Profile</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarContent>
      <SidebarFooter className="border-t border-border p-4">
        <div className="flex flex-col gap-2">
          <Button variant="outline" size="sm" className="justify-start" onClick={toggleTheme}>
            {theme === "dark" ? <Sun className="mr-2 h-4 w-4" /> : <Moon className="mr-2 h-4 w-4" />}
            {theme === "dark" ? "Light Mode" : "Dark Mode"}
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="justify-start text-destructive hover:text-destructive"
            onClick={handleLogout}
          >
            <LogOut className="mr-2 h-4 w-4" />
            Sign Out
          </Button>
        </div>
      </SidebarFooter>
    </Sidebar>
  )
}
