"use client"

import { Brain, Activity, Cpu, Target, TrendingUp } from "lucide-react"
import { TrainingStatus, ModelInfo } from "@/types/asr"

interface ASRHeroSectionProps {
  isServerAvailable: boolean
  status: TrainingStatus
  modelInfo: ModelInfo | null
}

export function ASRHeroSection({ isServerAvailable, status, modelInfo }: ASRHeroSectionProps) {
  return (
    <div className="relative overflow-hidden rounded-3xl bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 p-8 text-white shadow-2xl">
      <div className="absolute inset-0 bg-black/20"></div>
      <div className="relative z-10">
        <div className="flex items-center justify-between">
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="rounded-full bg-white/20 p-3">
                <Brain className="h-8 w-8" />
              </div>
              <div>
                <h1 className="text-4xl font-bold tracking-tight">ASR Training Hub</h1>
                <p className="text-blue-100 text-lg">
                  Advanced Speech Recognition Model Training & Management
                </p>
              </div>
            </div>
            
            {/* Status Indicators */}
            <div className="flex items-center space-x-4">
              <div className={`flex items-center space-x-2 rounded-full px-4 py-2 ${
                isServerAvailable 
                  ? 'bg-green-500/20 text-green-100' 
                  : 'bg-red-500/20 text-red-100'
              }`}>
                <div className={`h-2 w-2 rounded-full ${
                  isServerAvailable ? 'bg-green-400' : 'bg-red-400'
                } animate-pulse`}></div>
                <span className="text-sm font-medium">
                  {isServerAvailable ? 'System Online' : 'System Offline'}
                </span>
              </div>
              
              <div className={`flex items-center space-x-2 rounded-full px-4 py-2 ${
                status.status === 'training' 
                  ? 'bg-yellow-500/20 text-yellow-100' 
                  : status.status === 'completed'
                  ? 'bg-green-500/20 text-green-100'
                  : 'bg-blue-500/20 text-blue-100'
              }`}>
                <Activity className="h-4 w-4" />
                <span className="text-sm font-medium capitalize">
                  {status.status.replace('_', ' ')}
                </span>
              </div>
            </div>
          </div>

          {/* Current Model Information */}
          {modelInfo && (
            <div className="space-y-3">
              <h3 className="text-lg font-semibold text-white/90">Current Model</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-white/10 rounded-lg p-4 backdrop-blur-sm">
                  <div className="flex items-center space-x-2 mb-2">
                    <Cpu className="h-4 w-4 text-blue-200" />
                    <span className="text-sm font-medium text-blue-200">Version</span>
                  </div>
                  <p className="text-white font-semibold truncate" title={modelInfo.version}>
                    {modelInfo.version !== 'No model' ? modelInfo.version : 'No trained model'}
                  </p>
                  <p className="text-xs text-blue-200 mt-1">
                    {modelInfo.samples_trained} samples trained
                  </p>
                </div>

                <div className="bg-white/10 rounded-lg p-4 backdrop-blur-sm">
                  <div className="flex items-center space-x-2 mb-2">
                    <Target className="h-4 w-4 text-green-200" />
                    <span className="text-sm font-medium text-green-200">Accuracy</span>
                  </div>
                  <p className="text-white font-semibold">
                    {modelInfo.accuracy > 0 ? `${(modelInfo.accuracy * 100).toFixed(1)}%` : 'Not validated'}
                  </p>
                  <p className="text-xs text-green-200 mt-1">
                    WER: {modelInfo.wer > 0 ? `${(modelInfo.wer * 100).toFixed(1)}%` : 'N/A'}
                  </p>
                </div>

                <div className="bg-white/10 rounded-lg p-4 backdrop-blur-sm">
                  <div className="flex items-center space-x-2 mb-2">
                    <TrendingUp className="h-4 w-4 text-purple-200" />
                    <span className="text-sm font-medium text-purple-200">Confidence</span>
                  </div>
                  <p className="text-white font-semibold">
                    {modelInfo.confidence > 0 ? `${(modelInfo.confidence * 100).toFixed(1)}%` : 'Not validated'}
                  </p>
                  <p className="text-xs text-purple-200 mt-1">
                    Last trained: {modelInfo.last_trained !== 'Never' ? new Date(modelInfo.last_trained).toLocaleDateString() : 'Never'}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* Decorative Elements */}
      <div className="absolute top-0 right-0 -mt-4 -mr-4 h-24 w-24 rounded-full bg-white/10"></div>
      <div className="absolute bottom-0 left-0 -mb-8 -ml-8 h-32 w-32 rounded-full bg-white/5"></div>
    </div>
  )
}
