@echo off
title SSL Setup for Windows 11 Development
color 0B

echo.
echo ========================================================
echo  SSL Certificate Setup for Windows 11 Development
echo ========================================================
echo.

REM Check if OpenSSL is available
where openssl >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ OpenSSL not found!
    echo.
    echo Please install OpenSSL first:
    echo 1. Download from: https://slproweb.com/products/Win32OpenSSL.html
    echo 2. Install the "Win64 OpenSSL" version
    echo 3. Add OpenSSL to your PATH environment variable
    echo.
    echo Alternative: Install via Chocolatey:
    echo    choco install openssl
    echo.
    echo Or via Scoop:
    echo    scoop install openssl
    echo.
    pause
    exit /b 1
)

echo ✅ OpenSSL found
echo.

echo [1/3] Generating SSL certificates...
python generate-ssl.py

if %errorlevel% neq 0 (
    echo ❌ Failed to generate SSL certificates
    pause
    exit /b 1
)

echo.
echo [2/3] Setting up hosts file...
echo.
echo ⚠️  ADMINISTRATOR PRIVILEGES REQUIRED
echo.
echo To complete the setup, you need to add this line to your hosts file:
echo    127.0.0.1 buragatechnologies.com
echo.
echo Hosts file location: C:\Windows\System32\drivers\etc\hosts
echo.
echo Would you like to open the hosts file in Notepad? (y/n)
set /p choice=

if /i "%choice%"=="y" (
    echo Opening hosts file...
    notepad C:\Windows\System32\drivers\etc\hosts
    echo.
    echo Please add this line to the hosts file:
    echo 127.0.0.1 buragatechnologies.com
    echo.
    echo Save the file and close Notepad when done.
    pause
)

echo.
echo [3/3] Setup complete!
echo.
echo ========================================================
echo  🔒 SSL SETUP COMPLETE
echo ========================================================
echo  Certificates: ssl/cert.pem, ssl/key.pem
echo  Domain:       buragatechnologies.com
echo  Local IP:     127.0.0.1
echo ========================================================
echo.
echo Next steps:
echo 1. Start development with SSL: start-development-ssl.bat
echo 2. Or start regular development: start-development.bat
echo.
echo Note: Your browser will show a security warning for
echo       self-signed certificates. Click "Advanced" and
echo       "Proceed to buragatechnologies.com" to continue.
echo.
pause
