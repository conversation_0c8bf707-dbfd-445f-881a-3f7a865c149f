#!/bin/bash

# Ubuntu Production Setup Script
# Run this script on your Ubuntu server for initial setup

set -e  # Exit on any error

echo "🚀 Setting up Masalit AI Platform on Ubuntu Server"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root for security reasons"
   exit 1
fi

print_status "Starting Ubuntu production setup..."

# Step 1: Update system
print_status "Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Step 2: Install Node.js
print_status "Installing Node.js 18..."
if ! command -v node &> /dev/null; then
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

# Step 3: Install Python dependencies
print_status "Installing Python 3 and pip..."
sudo apt install python3 python3-pip python3-venv -y

# Step 4: Install pnpm
print_status "Installing pnpm..."
if ! command -v pnpm &> /dev/null; then
    npm install -g pnpm
fi

# Step 5: Install Git
print_status "Installing Git..."
sudo apt install git -y

# Step 6: Install Nginx (if not already installed)
print_status "Installing Nginx..."
if ! command -v nginx &> /dev/null; then
    sudo apt install nginx -y
fi

# Step 7: Create project directory
print_status "Setting up project directory..."
sudo mkdir -p /var/www
cd /var/www

# Step 8: Clone repository (if not already cloned)
if [[ ! -d "masalit-ai" ]]; then
    print_status "Cloning repository..."
    sudo git clone https://github.com/iabakar/masalit-ai.git
    sudo chown -R $USER:$USER masalit-ai
else
    print_status "Repository already exists, pulling latest changes..."
    cd masalit-ai
    git pull origin main
    cd ..
fi

cd masalit-ai

# Step 9: Create logs directory
print_status "Creating logs directory..."
mkdir -p logs

# Step 10: Install frontend dependencies
print_status "Installing frontend dependencies..."
pnpm install

# Step 11: Install backend dependencies
print_status "Installing backend dependencies..."
cd backend
python3 -m pip install -r requirements.txt --user

# Step 12: Setup backend environment
print_status "Setting up backend environment..."
if [[ ! -f ".env" ]]; then
    if [[ -f ".env.example" ]]; then
        cp .env.example .env
        print_warning "Backend .env file created from template."
        print_warning "You MUST edit backend/.env with your actual credentials!"
        print_warning "Run: nano backend/.env"
    else
        print_error "Backend .env template not found!"
        exit 1
    fi
else
    print_status "Backend .env file already exists"
fi

cd ..

# Step 13: Build frontend
print_status "Building frontend for production..."
pnpm build

# Step 14: Configure Nginx
print_status "Configuring Nginx..."
if [[ ! -f "/etc/nginx/sites-available/buragatechnologies.com" ]]; then
    sudo cp nginx.conf /etc/nginx/sites-available/buragatechnologies.com
    sudo ln -s /etc/nginx/sites-available/buragatechnologies.com /etc/nginx/sites-enabled/
    
    # Test Nginx configuration
    if sudo nginx -t; then
        print_status "Nginx configuration is valid"
        sudo systemctl reload nginx
    else
        print_error "Nginx configuration is invalid. Please check manually."
    fi
else
    print_status "Nginx configuration already exists"
fi

# Step 15: Make scripts executable
print_status "Making scripts executable..."
chmod +x *.sh

# Step 16: Setup systemd services (optional)
print_status "Setting up systemd services..."

# Create systemd service for backend
sudo tee /etc/systemd/system/masalit-backend.service > /dev/null <<EOF
[Unit]
Description=Masalit AI Backend
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=/var/www/masalit-ai
ExecStart=/usr/bin/python3 start-backend.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Create systemd service for frontend
sudo tee /etc/systemd/system/masalit-frontend.service > /dev/null <<EOF
[Unit]
Description=Masalit AI Frontend
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=/var/www/masalit-ai
ExecStart=/usr/bin/pnpm start
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Reload systemd and enable services
sudo systemctl daemon-reload
sudo systemctl enable masalit-backend
sudo systemctl enable masalit-frontend

print_status "Setup completed successfully!"
echo ""
echo "📋 Next Steps:"
echo "1. Edit backend environment: nano backend/.env"
echo "2. Add your Firebase and GCS credentials"
echo "3. Start services: ./deploy-production.sh"
echo "4. Or use systemd: sudo systemctl start masalit-backend masalit-frontend"
echo ""
echo "📊 Service Management:"
echo "  Start:   sudo systemctl start masalit-backend masalit-frontend"
echo "  Stop:    sudo systemctl stop masalit-backend masalit-frontend"
echo "  Status:  sudo systemctl status masalit-backend masalit-frontend"
echo "  Logs:    sudo journalctl -u masalit-backend -f"
echo ""
echo "🌐 Access Points:"
echo "  Frontend: https://buragatechnologies.com"
echo "  Backend:  http://127.0.0.1:8000 (internal only)"
echo ""
echo "⚠️  IMPORTANT: Edit backend/.env with your actual credentials before starting services!"
