/**
 * Server-side reCAPTCHA verification utility
 */

interface RecaptchaVerificationResponse {
  success: boolean
  challenge_ts?: string
  hostname?: string
  'error-codes'?: string[]
  score?: number
  action?: string
}

export async function verifyRecaptcha(token: string): Promise<boolean> {
  const secretKey = process.env.RECAPTCHA_SECRET_KEY
  const isDevelopment = process.env.NODE_ENV === 'development'

  // Allow bypass in development
  if (isDevelopment && token === 'dev-bypass-token') {
    console.log('reCAPTCHA bypassed in development mode')
    return true
  }

  if (!secretKey) {
    console.error('reCAPTCHA secret key not found in environment variables')
    return false
  }

  if (!token) {
    console.error('No reCAPTCHA token provided')
    return false
  }

  try {
    const response = await fetch('https://www.google.com/recaptcha/api/siteverify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        secret: secretKey,
        response: token,
      }),
    })

    if (!response.ok) {
      console.error('reCAPTCHA verification request failed:', response.statusText)
      return false
    }

    const data: RecaptchaVerificationResponse = await response.json()
    
    if (!data.success) {
      console.error('reCAPTCHA verification failed:', data['error-codes'])
      return false
    }

    // For v3 reCAPTCHA, check the score (optional)
    if (data.score !== undefined) {
      // Score ranges from 0.0 to 1.0, where 1.0 is very likely a good interaction
      const minimumScore = 0.5
      if (data.score < minimumScore) {
        console.warn(`reCAPTCHA score too low: ${data.score}`)
        return false
      }
    }

    return true
  } catch (error) {
    console.error('Error verifying reCAPTCHA:', error)
    return false
  }
}

/**
 * Middleware function to verify reCAPTCHA in API routes
 */
export async function requireRecaptcha(request: Request): Promise<boolean> {
  try {
    const body = await request.json()
    const token = body.recaptchaToken

    if (!token) {
      return false
    }

    return await verifyRecaptcha(token)
  } catch (error) {
    console.error('Error parsing request for reCAPTCHA verification:', error)
    return false
  }
}
