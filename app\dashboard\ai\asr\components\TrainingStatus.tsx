import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Progress, getDynamicTextColor } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, Loader2, CheckCircle } from "lucide-react"
import { TrainingStatus as TrainingStatusType } from "@/types/asr"
import { Badge } from "@/components/ui/badge"
import { formatDistanceToNow } from "date-fns"

interface TrainingStatusProps {
  status: TrainingStatusType;
}

export function TrainingStatus({ status }: TrainingStatusProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "training":
        return "bg-blue-500"
      case "completed":
        return "bg-green-500"
      case "failed":
        return "bg-red-500"
      case "error":
        return "bg-red-500"
      default:
        return "bg-gray-500"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "training":
        return <Loader2 className="h-4 w-4 animate-spin" />
      case "completed":
        return <CheckCircle className="h-4 w-4" />
      case "failed":
      case "error":
        return <AlertCircle className="h-4 w-4" />
      default:
        return null
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Training Status</CardTitle>
        <CardDescription>Current training progress</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className={getStatusColor(status.status)}>
                {getStatusIcon(status.status)}
                <span className="ml-2 capitalize">{status.status}</span>
              </Badge>
              {status.start_time && (
                <span className="text-sm text-muted-foreground">
                  Started {formatDistanceToNow(new Date(status.start_time), { addSuffix: true })}
                </span>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Overall Progress</span>
              <span className={getDynamicTextColor(Math.round(status.progress * 100), 'inverse')}>
                {Math.round(status.progress * 100)}%
              </span>
            </div>
            <Progress
              value={status.progress * 100}
              showDynamicColors={true}
              colorScheme="inverse"
            />
          </div>

          {status.status === "training" && (
            <>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Current Epoch</span>
                  <span className="text-sm text-muted-foreground">{status.current_epoch}/{status.total_epochs}</span>
                </div>
                <Progress
                  value={(status.current_epoch / status.total_epochs) * 100}
                  showDynamicColors={true}
                  colorScheme="inverse"
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Samples Processed</span>
                  <span className="text-sm text-muted-foreground">{status.samples_processed}/{status.total_samples}</span>
                </div>
                <Progress
                  value={(status.samples_processed / status.total_samples) * 100}
                  showDynamicColors={true}
                  colorScheme="inverse"
                />
              </div>

              {status.current_accuracy > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Current Accuracy</span>
                    <span className={getDynamicTextColor(Math.round(status.current_accuracy * 100), 'inverse')}>
                      {Math.round(status.current_accuracy * 100)}%
                    </span>
                  </div>
                  <Progress
                    value={status.current_accuracy * 100}
                    showDynamicColors={true}
                    colorScheme="inverse"
                  />
                </div>
              )}

              {status.current_confidence > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Current Confidence</span>
                    <span className={getDynamicTextColor(Math.round(status.current_confidence * 100), 'inverse')}>
                      {Math.round(status.current_confidence * 100)}%
                    </span>
                  </div>
                  <Progress
                    value={status.current_confidence * 100}
                    showDynamicColors={true}
                    colorScheme="inverse"
                  />
                </div>
              )}
            </>
          )}

          {status.error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{status.error}</AlertDescription>
            </Alert>
          )}
        </div>
      </CardContent>
    </Card>
  )
} 