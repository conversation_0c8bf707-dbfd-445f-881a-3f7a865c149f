import { doc, setDoc, collection, query, where, getDocs, updateDoc } from "firebase/firestore"
import { db } from "@/lib/firebase"

export type TranscriptionSource = 'human/manual' | 'AI-generated' | 'mixed'
export type TranscriptionType = 'pdf' | 'txt' | 'docx' | 'json'

// NEW: Transcriptions are now stored as subcollections within audio documents
// Path: audio/{audio_id}/transcriptions/primary/
export type TranscriptionData = {
  content: string
  transcription_source: TranscriptionSource
  type: TranscriptionType
  created_at: Date
  updated_at: Date
  language: string
  dialect?: string | null
  speaker_count: number
  quality_rating?: number | null
  notes?: string | null
  // Note: Training status is now managed at the audio level
  // Use audio/{audio_id}/training/status/ for unified training status
}

export async function createTranscription(
  audioId: string,
  content: string,
  source: TranscriptionSource,
  type: TranscriptionType,
  userId: string,
  language: string = 'masalit',
  dialect: string | null = null,
  speakerCount: number = 1
): Promise<string> {
  try {
    const timestamp = Date.now()
    const transcriptionId = `transcription_${timestamp}`
    
    const transcriptionData: TranscriptionData = {
      audio_id: audioId,
      content,
      transcription_source: source,
      type,
      created_at: new Date(),
      updated_at: new Date(),
      user_id: userId,
      status: 'pending',
      language,
      dialect: dialect || null,
      speaker_count: speakerCount,
      trained_asr: false,
      training_epoch: null,
      is_flagged: false
    }

    const transcriptionRef = doc(db, "transcription", transcriptionId)
    await setDoc(transcriptionRef, transcriptionData)

    return transcriptionId
  } catch (error) {
    console.error("Error creating transcription:", error)
    throw error
  }
}

export async function getTranscriptionsByAudioId(audioId: string) {
  try {
    const transcriptionRef = collection(db, "transcription")
    const q = query(transcriptionRef, where("audio_id", "==", audioId))
    const querySnapshot = await getDocs(q)
    
    const transcriptions = []
    for (const doc of querySnapshot.docs) {
      const data = doc.data()
      transcriptions.push({
        id: doc.id,
        ...data,
        created_at: data.created_at.toDate(),
        updated_at: data.updated_at.toDate(),
        reviewed_at: data.reviewed_at?.toDate()
      })
    }
    
    return transcriptions
  } catch (error) {
    console.error("Error fetching transcriptions:", error)
    throw error
  }
}

export async function updateTranscriptionStatus(
  transcriptionId: string,
  status: 'approved' | 'rejected',
  reviewedBy: string,
  notes?: string
) {
  try {
    const transcriptionRef = doc(db, "transcription", transcriptionId)
    await updateDoc(transcriptionRef, {
      status,
      reviewed_by: reviewedBy,
      reviewed_at: new Date(),
      notes,
      updated_at: new Date()
    })
    return true
  } catch (error) {
    console.error("Error updating transcription status:", error)
    throw error
  }
}