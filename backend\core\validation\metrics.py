"""
Metrics Calculator

Calculates various performance metrics for different model types
"""

import re
import time
import logging
from typing import List, Dict, Any, Tuple, Optional
import numpy as np
from difflib import SequenceMatcher

from .types import ValidationMetrics, MetricType

logger = logging.getLogger(__name__)


class MetricsCalculator:
    """Calculate performance metrics for model validation"""
    
    def __init__(self):
        self.start_time = None
        self.memory_usage = []
    
    def start_timing(self):
        """Start timing for latency calculation"""
        self.start_time = time.time()
    
    def end_timing(self) -> float:
        """End timing and return latency in milliseconds"""
        if self.start_time is None:
            return 0.0
        return (time.time() - self.start_time) * 1000
    
    def calculate_asr_metrics(
        self, 
        references: List[str], 
        predictions: List[str],
        confidences: Optional[List[float]] = None
    ) -> ValidationMetrics:
        """Calculate ASR-specific metrics"""
        try:
            if len(references) != len(predictions):
                raise ValueError("References and predictions must have same length")
            
            # Calculate WER, CER, SER
            wer_scores = []
            cer_scores = []
            ser_scores = []
            
            for ref, pred in zip(references, predictions):
                wer_scores.append(self._calculate_wer(ref, pred))
                cer_scores.append(self._calculate_cer(ref, pred))
                ser_scores.append(self._calculate_ser(ref, pred))
            
            # Calculate averages
            avg_wer = np.mean(wer_scores) if wer_scores else 0.0
            avg_cer = np.mean(cer_scores) if cer_scores else 0.0
            avg_ser = np.mean(ser_scores) if ser_scores else 0.0
            
            # Calculate accuracy (1 - WER)
            accuracy = max(0.0, 1.0 - avg_wer)
            
            # Calculate confidence
            avg_confidence = np.mean(confidences) if confidences else 0.0
            
            return ValidationMetrics(
                accuracy=accuracy,
                confidence=avg_confidence,
                wer=avg_wer,
                cer=avg_cer,
                ser=avg_ser
            )
            
        except Exception as e:
            logger.error(f"Error calculating ASR metrics: {e}")
            return ValidationMetrics()
    
    def calculate_tts_metrics(
        self, 
        reference_audio: List[Any], 
        generated_audio: List[Any],
        reference_text: Optional[List[str]] = None
    ) -> ValidationMetrics:
        """Calculate TTS-specific metrics"""
        try:
            # For now, return placeholder metrics
            # In a real implementation, you would calculate:
            # - MOS (Mean Opinion Score) using a trained model
            # - Voice similarity using speaker embeddings
            # - Naturalness using prosody analysis
            
            # Placeholder calculations
            similarity = 0.85  # Would use actual audio comparison
            naturalness = 0.80  # Would use prosody analysis
            mos = (similarity + naturalness) / 2
            
            return ValidationMetrics(
                accuracy=mos,
                confidence=0.9,
                mos=mos,
                similarity=similarity,
                naturalness=naturalness
            )
            
        except Exception as e:
            logger.error(f"Error calculating TTS metrics: {e}")
            return ValidationMetrics()
    
    def _calculate_wer(self, reference: str, prediction: str) -> float:
        """Calculate Word Error Rate"""
        try:
            ref_words = self._normalize_text(reference).split()
            pred_words = self._normalize_text(prediction).split()
            
            if len(ref_words) == 0:
                return 1.0 if len(pred_words) > 0 else 0.0
            
            # Calculate edit distance
            distance = self._edit_distance(ref_words, pred_words)
            return distance / len(ref_words)
            
        except Exception as e:
            logger.error(f"Error calculating WER: {e}")
            return 1.0
    
    def _calculate_cer(self, reference: str, prediction: str) -> float:
        """Calculate Character Error Rate"""
        try:
            ref_chars = list(self._normalize_text(reference))
            pred_chars = list(self._normalize_text(prediction))
            
            if len(ref_chars) == 0:
                return 1.0 if len(pred_chars) > 0 else 0.0
            
            # Calculate edit distance
            distance = self._edit_distance(ref_chars, pred_chars)
            return distance / len(ref_chars)
            
        except Exception as e:
            logger.error(f"Error calculating CER: {e}")
            return 1.0
    
    def _calculate_ser(self, reference: str, prediction: str) -> float:
        """Calculate Sentence Error Rate"""
        try:
            ref_normalized = self._normalize_text(reference)
            pred_normalized = self._normalize_text(prediction)
            
            # Sentence is correct if normalized texts match exactly
            return 0.0 if ref_normalized == pred_normalized else 1.0
            
        except Exception as e:
            logger.error(f"Error calculating SER: {e}")
            return 1.0
    
    def _normalize_text(self, text: str) -> str:
        """Normalize text for comparison"""
        try:
            # Convert to lowercase
            text = text.lower()
            
            # Remove punctuation and extra whitespace
            text = re.sub(r'[^\w\s]', '', text)
            text = re.sub(r'\s+', ' ', text)
            
            return text.strip()
            
        except Exception as e:
            logger.error(f"Error normalizing text: {e}")
            return ""
    
    def _edit_distance(self, seq1: List[str], seq2: List[str]) -> int:
        """Calculate edit distance between two sequences"""
        try:
            m, n = len(seq1), len(seq2)
            
            # Create DP table
            dp = [[0] * (n + 1) for _ in range(m + 1)]
            
            # Initialize base cases
            for i in range(m + 1):
                dp[i][0] = i
            for j in range(n + 1):
                dp[0][j] = j
            
            # Fill DP table
            for i in range(1, m + 1):
                for j in range(1, n + 1):
                    if seq1[i-1] == seq2[j-1]:
                        dp[i][j] = dp[i-1][j-1]
                    else:
                        dp[i][j] = 1 + min(
                            dp[i-1][j],    # deletion
                            dp[i][j-1],    # insertion
                            dp[i-1][j-1]   # substitution
                        )
            
            return dp[m][n]
            
        except Exception as e:
            logger.error(f"Error calculating edit distance: {e}")
            return max(len(seq1), len(seq2))
    
    def calculate_performance_metrics(
        self, 
        latencies: List[float],
        memory_usage: List[float],
        sample_count: int,
        duration_seconds: float
    ) -> Dict[str, float]:
        """Calculate performance metrics"""
        try:
            metrics = {}
            
            if latencies:
                metrics['latency_ms'] = np.mean(latencies)
                metrics['latency_p95_ms'] = np.percentile(latencies, 95)
                metrics['latency_p99_ms'] = np.percentile(latencies, 99)
            
            if memory_usage:
                metrics['memory_usage_mb'] = np.mean(memory_usage)
                metrics['peak_memory_mb'] = np.max(memory_usage)
            
            if duration_seconds > 0:
                metrics['throughput_samples_per_sec'] = sample_count / duration_seconds
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating performance metrics: {e}")
            return {}
    
    def calculate_confidence_distribution(self, confidences: List[float]) -> Dict[str, int]:
        """Calculate confidence score distribution"""
        try:
            if not confidences:
                return {}
            
            # Define confidence ranges
            ranges = {
                "very_low": (0.0, 0.2),
                "low": (0.2, 0.4),
                "medium": (0.4, 0.6),
                "high": (0.6, 0.8),
                "very_high": (0.8, 1.0)
            }
            
            distribution = {range_name: 0 for range_name in ranges}
            
            for confidence in confidences:
                for range_name, (min_val, max_val) in ranges.items():
                    if min_val <= confidence < max_val or (range_name == "very_high" and confidence == 1.0):
                        distribution[range_name] += 1
                        break
            
            return distribution
            
        except Exception as e:
            logger.error(f"Error calculating confidence distribution: {e}")
            return {}
    
    def calculate_error_distribution(
        self, 
        references: List[str], 
        predictions: List[str]
    ) -> Dict[str, int]:
        """Calculate error type distribution"""
        try:
            error_types = {
                "perfect_match": 0,
                "minor_errors": 0,
                "major_errors": 0,
                "complete_mismatch": 0
            }
            
            for ref, pred in zip(references, predictions):
                similarity = SequenceMatcher(None, ref.lower(), pred.lower()).ratio()
                
                if similarity >= 0.95:
                    error_types["perfect_match"] += 1
                elif similarity >= 0.7:
                    error_types["minor_errors"] += 1
                elif similarity >= 0.3:
                    error_types["major_errors"] += 1
                else:
                    error_types["complete_mismatch"] += 1
            
            return error_types
            
        except Exception as e:
            logger.error(f"Error calculating error distribution: {e}")
            return {}
    
    def aggregate_metrics(self, individual_metrics: List[ValidationMetrics]) -> ValidationMetrics:
        """Aggregate individual sample metrics into overall metrics"""
        try:
            if not individual_metrics:
                return ValidationMetrics()
            
            # Extract all metric values
            accuracies = [m.accuracy for m in individual_metrics if m.accuracy is not None]
            confidences = [m.confidence for m in individual_metrics if m.confidence is not None]
            wers = [m.wer for m in individual_metrics if m.wer is not None]
            cers = [m.cer for m in individual_metrics if m.cer is not None]
            sers = [m.ser for m in individual_metrics if m.ser is not None]
            
            # Calculate averages
            return ValidationMetrics(
                accuracy=np.mean(accuracies) if accuracies else 0.0,
                confidence=np.mean(confidences) if confidences else 0.0,
                wer=np.mean(wers) if wers else None,
                cer=np.mean(cers) if cers else None,
                ser=np.mean(sers) if sers else None
            )
            
        except Exception as e:
            logger.error(f"Error aggregating metrics: {e}")
            return ValidationMetrics()
