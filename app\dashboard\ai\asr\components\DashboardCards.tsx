"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Target, FileAudio, Calendar, Package, Clock, GitCompare } from "lucide-react"
import { TrainingStatus, ModelInfo } from "@/types/asr"
import { useState, useEffect } from "react"
import { collection, query, where, getDocs, doc, getDoc } from "firebase/firestore"
import { db } from "@/lib/firebase"

interface DashboardCardsProps {
  status: TrainingStatus
  isServerAvailable: boolean
  onManageClick?: () => void
  onCompareClick?: () => void
  onScheduleClick?: () => void
  modelInfo: ModelInfo | null
}

interface AudioStats {
  totalAudio: number
  approvedAudio: number
  trainedAudio: number
  untrainedAudio: number
}

interface ModelStats {
  availableModels: number
  currentModel: string
}

export function DashboardCards({
  status,
  isServerAvailable,
  onManageClick,
  onCompareClick,
  onScheduleClick,
  modelInfo
}: DashboardCardsProps) {
  const [audioStats, setAudioStats] = useState<AudioStats>({
    totalAudio: 0,
    approvedAudio: 0,
    trainedAudio: 0,
    untrainedAudio: 0
  })

  const [modelStats, setModelStats] = useState<ModelStats>({
    availableModels: 0,
    currentModel: 'N/A'
  })

  useEffect(() => {
    loadAudioStats()
    loadModelStats()
  }, [])

  const loadAudioStats = async () => {
    try {
      console.log('🔄 Loading audio stats from hierarchical Firestore structure...')

      // Get total audio count
      const audioQuery = query(collection(db, 'audio'))
      const audioSnapshot = await getDocs(audioQuery)
      const totalAudio = audioSnapshot.size
      console.log('📊 Total audio files:', totalAudio)

      let approvedAudio = 0
      let trainedAudio = 0
      let untrainedAudio = 0

      // Process each audio document to check hierarchical subcollections
      for (const audioDoc of audioSnapshot.docs) {
        const audioId = audioDoc.id

        try {
          // Check review status from subcollection
          const reviewStatusRef = doc(db, 'audio', audioId, 'review', 'status')
          const reviewStatusDoc = await getDoc(reviewStatusRef)

          let isApproved = false
          if (reviewStatusDoc.exists()) {
            const reviewData = reviewStatusDoc.data()
            isApproved = reviewData.action === 'approved'
            if (isApproved) {
              approvedAudio++
            }
          }

          // Check training status from subcollection
          const trainingStatusRef = doc(db, 'audio', audioId, 'training', 'status')
          const trainingStatusDoc = await getDoc(trainingStatusRef)

          let isTrained = false
          if (trainingStatusDoc.exists()) {
            const trainingData = trainingStatusDoc.data()
            isTrained = trainingData.trained_asr === true
            if (isTrained) {
              trainedAudio++
            }
          }

          // Count untrained approved audio
          if (isApproved && !isTrained) {
            untrainedAudio++
          }

        } catch (error) {
          console.warn(`Error processing audio ${audioId}:`, error)
        }
      }

      console.log('✅ Approved audio files:', approvedAudio)
      console.log('🎯 Trained audio files:', trainedAudio)
      console.log('⏳ Untrained audio files:', untrainedAudio)

      const stats = {
        totalAudio,
        approvedAudio,
        trainedAudio,
        untrainedAudio
      }

      setAudioStats(stats)

      console.log('✅ Audio stats loaded successfully:', stats)

      // If no data exists, show a helpful message
      if (totalAudio === 0) {
        console.log('ℹ️ No audio data found in Firestore. Upload some audio files to see statistics.')
      }
    } catch (error) {
      console.error('❌ Error loading audio stats:', error)

      // Keep zeros to show the actual state
      setAudioStats({
        totalAudio: 0,
        approvedAudio: 0,
        trainedAudio: 0,
        untrainedAudio: 0
      })
    }
  }

  const loadModelStats = async () => {
    try {
      // Fetch models from our new API
      const response = await fetch('/api/asr/models/list', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        const data = await response.json()
        const models = data.models || []

        setModelStats({
          availableModels: models.length,
          currentModel: models.length > 0 ? models[0].model_version : 'N/A'
        })
      } else {
        console.warn('Backend not available, using fallback model stats')
        setModelStats({
          availableModels: 0,
          currentModel: 'Backend unavailable'
        })
      }
    } catch (error) {
      console.warn('Error loading model stats (backend may not be running):', error)
      setModelStats({
        availableModels: 0,
        currentModel: 'Backend unavailable'
      })
    }
  }
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
      {/* Model Performance */}
      <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader className="pb-4">
          <div className="flex items-center space-x-3">
            <div className="rounded-full bg-purple-100 p-2">
              <Target className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <CardTitle className="text-lg">Performance</CardTitle>
              <CardDescription>Model metrics</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Accuracy</span>
                <span className="text-sm font-bold text-purple-600">
                  {modelInfo?.accuracy && modelInfo.accuracy > 0
                    ? `${Math.round(modelInfo.accuracy * 100)}%`
                    : status.current_accuracy
                    ? `${Math.round(status.current_accuracy * 100)}%`
                    : 'Not validated'}
                </span>
              </div>
              <Progress
                value={modelInfo?.accuracy && modelInfo.accuracy > 0
                  ? modelInfo.accuracy * 100
                  : status.current_accuracy
                  ? status.current_accuracy * 100
                  : 0}
                className="h-2"
              />
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Confidence</span>
                <span className="text-sm font-bold text-purple-600">
                  {modelInfo?.confidence && modelInfo.confidence > 0
                    ? `${Math.round(modelInfo.confidence * 100)}%`
                    : status.current_confidence
                    ? `${Math.round(status.current_confidence * 100)}%`
                    : 'Not validated'}
                </span>
              </div>
              <Progress
                value={modelInfo?.confidence && modelInfo.confidence > 0
                  ? modelInfo.confidence * 100
                  : status.current_confidence
                  ? status.current_confidence * 100
                  : 0}
                className="h-2"
              />
            </div>
            {modelInfo && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">WER</span>
                  <span className="text-sm font-bold text-red-600">
                    {modelInfo.wer > 0 ? `${Math.round(modelInfo.wer * 100)}%` : 'N/A'}
                  </span>
                </div>
                <Progress value={modelInfo.wer > 0 ? modelInfo.wer * 100 : 0} className="h-2" />
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Audio Data */}
      <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader className="pb-4">
          <div className="flex items-center space-x-3">
            <div className="rounded-full bg-orange-100 p-2">
              <FileAudio className="h-5 w-5 text-orange-600" />
            </div>
            <div>
              <CardTitle className="text-lg">Audio Data</CardTitle>
              <CardDescription>Training samples</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {audioStats.totalAudio === 0 ? (
            <div className="text-center py-4">
              <p className="text-sm text-muted-foreground">No audio data found</p>
              <p className="text-xs text-muted-foreground mt-1">Upload audio files to see statistics</p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Total</span>
                  <span className="text-sm font-bold text-gray-600">{audioStats.totalAudio}</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Approved</span>
                  <span className="text-sm font-bold text-green-600">{audioStats.approvedAudio}</span>
                </div>
                <Progress
                  value={audioStats.totalAudio > 0 ? (audioStats.approvedAudio / audioStats.totalAudio) * 100 : 0}
                  className="h-2"
                />
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Trained</span>
                  <span className="text-sm font-bold text-blue-600">{audioStats.trainedAudio}</span>
                </div>
                <Progress
                  value={audioStats.approvedAudio > 0 ? (audioStats.trainedAudio / audioStats.approvedAudio) * 100 : 0}
                  className="h-2"
                />
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Untrained</span>
                  <span className="text-sm font-bold text-orange-600">{audioStats.untrainedAudio}</span>
                </div>
                <Progress
                  value={audioStats.approvedAudio > 0 ? (audioStats.untrainedAudio / audioStats.approvedAudio) * 100 : 0}
                  className="h-2"
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Training Schedule */}
      <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader className="pb-4">
          <div className="flex items-center space-x-3">
            <div className="rounded-full bg-indigo-100 p-2">
              <Calendar className="h-5 w-5 text-indigo-600" />
            </div>
            <div>
              <CardTitle className="text-lg">Schedule</CardTitle>
              <CardDescription>Auto training</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Status</span>
              <Badge variant="secondary" className="text-xs">
                Disabled
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Frequency</span>
              <span className="text-sm font-bold text-indigo-600">Weekly</span>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={onScheduleClick}
            >
              <Clock className="mr-2 h-4 w-4" />
              Configure
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Model Management */}
      <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader className="pb-4">
          <div className="flex items-center space-x-3">
            <div className="rounded-full bg-teal-100 p-2">
              <Package className="h-5 w-5 text-teal-600" />
            </div>
            <div>
              <CardTitle className="text-lg">Models</CardTitle>
              <CardDescription>Manage versions</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Available</span>
              <span className="text-sm font-bold text-teal-600">{modelStats.availableModels}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Current</span>
              <span className="text-sm font-bold text-teal-600 truncate" title={modelInfo?.version || 'N/A'}>
                {modelInfo?.version !== 'No model' ? modelInfo?.version || 'N/A' : 'No model'}
              </span>
            </div>
            {modelInfo && (
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Samples</span>
                <span className="text-sm font-bold text-teal-600">{modelInfo.samples_trained}</span>
              </div>
            )}
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onManageClick}
              >
                <Package className="mr-1 h-3 w-3" />
                Manage
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={onCompareClick}
              >
                <GitCompare className="mr-1 h-3 w-3" />
                Compare
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
